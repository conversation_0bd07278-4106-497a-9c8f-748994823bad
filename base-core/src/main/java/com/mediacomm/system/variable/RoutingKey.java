package com.mediacomm.system.variable;

/**
 * RoutingKey.
 */
public class RoutingKey {

  public static final String DEAD_LETTER_ROUTING = "dlq-queue";
  public static final String CAESAR = "kvm.caesar.";
  public static final String CAESAR_KVM_OPT = "kvm.caesar.#";
  public static final String CAESAR_KVM_ADD = CAESAR + RoutingOperation.ADD;
  public static final String CAESAR_KVM_DELETE = CAESAR + RoutingOperation.DELETE;
  public static final String CAESAR_KVM_REBOOT = CAESAR + RoutingOperation.REBOOT;
  public static final String CAESAR_KVM_REFRESH_EXTEND_DEVICE =
      CAESAR + RoutingOperation.EXT_REFRESH;
  public static final String CAESAR_KVM_REFRESH_CONFIG = CAESAR + RoutingOperation.CONFIG_REFRESH;
  public static final String CAESAR_KVM_GET_TX_SNAPSHOT = CAESAR + RoutingOperation.SNAPSHOT_TX_GET;
  @Deprecated
  public static final String CAESAR_KVM_GET_VW_SCENE = CAESAR + RoutingOperation.VW_SCENE_GET;
  public static final String CAESAR_KVM_GET_VW_PANELS = CAESAR + RoutingOperation.VW_PANELS_GET;
  public static final String CAESAR_KVM_OPEN_VW_PANEL = CAESAR + RoutingOperation.VW_PANEL_OPEN;
  public static final String CAESAR_KVM_CLOSE_VW_PANEL = CAESAR + RoutingOperation.VW_PANEL_CLOSE;
  public static final String CAESAR_KVM_CLOSE_ALL_VW_PANEL =
      CAESAR + RoutingOperation.VW_PANELS_CLOSE;
  public static final String CAESAR_KVM_OPEN_VW_PANELS = CAESAR + RoutingOperation.VW_PANELS_OPEN;
  public static final String CAESAR_KVM_MOVE_VW_PANEL = CAESAR + RoutingOperation.VW_PANEL_MOVE;
  public static final String CAESAR_KVM_SWAP_VW_PANEL_LAYER =
      CAESAR + RoutingOperation.VW_PANEL_LAYER_SWAP;
  public static final String CAESAR_KVM_GET_SEAT_PANELS = CAESAR + RoutingOperation.SEAT_PANELS_GET;
  public static final String CAESAR_KVM_SEAT_OPEN_TX = CAESAR + RoutingOperation.SEAT_PANEL_OPEN;
  public static final String CAESAR_KVM_SEAT_OPEN_TXES = CAESAR + RoutingOperation.SEAT_PANELS_OPEN;
  public static final String CAESAR_KVM_CLOSE_ALL_SEAT_PANEL =
      CAESAR + RoutingOperation.SEAT_PANELS_CLOSE;
  public static final String CAESAR_KVM_CLOSE_SEAT_PANEL =
          CAESAR + RoutingOperation.SEAT_PANEL_CLOSE;
  public static final String CAESAR_KVM_CONNECT_TX_RX = CAESAR + RoutingOperation.TX_RX_CONNECT;
  public static final String CAESAR_KVM_GET_SNAPSHOT = CAESAR + RoutingOperation.SNAPSHOT_GET;
  public static final String CAESAR_KVM_CLIENT_HEARTBEAT = CAESAR + RoutingOperation.CLIENT_HEARTBEAT;
  public static final String CAESAR_VIDEO_WALL_UPDATE = CAESAR + RoutingOperation.VW_UPDATE;
  public static final String CAESAR_EXTEND_UPDATE = CAESAR + RoutingOperation.ASSET_UPDATE;
  public static final String CAESAR_EXTEND_VERSION = CAESAR + RoutingOperation.ASSET_VERSION_GET;
  public static final String CAESAR_KVM_OPEN_VS = CAESAR + RoutingOperation.VS_OPEN;
  public static final String CAESAR_CONNECT_TX_ENCODER = CAESAR
      + RoutingOperation.TX_ENCODER_CONNECT;
  public static final String CAESAR_BANNER_GET = CAESAR + RoutingOperation.BANNER_GET;
  public static final String CAESAR_BANNER_SET = CAESAR + RoutingOperation.BANNER_SET;
  public static final String CAESAR_BANNER_ENABLE = CAESAR + RoutingOperation.BANNER_ENABLE;
  public static final String CAESAR_BANNER_BG_COLOR_ENABLE = CAESAR + RoutingOperation.BANNER_BG_COLOR_ENABLE;
  public static final String CAESAR_VW_BOTTOM_IMAGE_SET = CAESAR + RoutingOperation.VW_BOTTOM_IMAGE_SET;
  public static final String CAESAR_VW_BOTTOM_IMAGE_ENABLE = CAESAR + RoutingOperation.VW_BOTTOM_IMAGE_ENABLE;
  public static final String CAESAR_START_UPGRADE_PACKAGE = CAESAR + RoutingOperation.START_UPGRADE_PACKAGE;
  public static final String CAESAR_CHECK_UPGRADE_PACKAGE = CAESAR + RoutingOperation.CHECK_UPGRADE_PACKAGE;
  public static final String CAESAR_STOP_UPGRADE_PACKAGE = CAESAR + RoutingOperation.STOP_UPGRADE_PACKAGE;
  /**
   * kaito.
   */
  public static final String KAITO = "kvm.kaito.";
  public static final String KAITO_KVM_OPT = "kvm.kaito.#";
  public static final String KAITO_KVM_REFRESH_CONFIG = KAITO + RoutingOperation.CONFIG_REFRESH;
  public static final String KAITO_KVM_GET_VW_PANELS = KAITO + RoutingOperation.VW_PANELS_GET;
  public static final String KAITO_KVM_OPEN_VW_PANEL = KAITO + RoutingOperation.VW_PANEL_OPEN;
  public static final String KAITO_KVM_OPEN_VW_PANELS = KAITO + RoutingOperation.VW_PANELS_OPEN;
  public static final String KAITO_KVM_CLOSE_VW_PANEL = KAITO + RoutingOperation.VW_PANEL_CLOSE;
  public static final String KAITO_KVM_CLOSE_ALL_VW_PANEL =
          KAITO + RoutingOperation.VW_PANELS_CLOSE;
  public static final String KAITO_KVM_MOVE_VW_PANEL = KAITO + RoutingOperation.VW_PANEL_MOVE;
  public static final String KAITO_KVM_GET_TX_SNAPSHOT = KAITO + RoutingOperation.SNAPSHOT_TX_GET;
  public static final String KAITO_BANNER_GET = KAITO + RoutingOperation.BANNER_GET;
  public static final String KAITO_BANNER_ENABLE = KAITO + RoutingOperation.BANNER_ENABLE;
  public static final String KAITO_SWAP_VW_PANEL_LAYER = KAITO + RoutingOperation.VW_PANEL_LAYER_SWAP;

  /**
   * aircon.
   */
  public static final String AIRCON = "kvm.aircon.";
  public static final String AIRCON_KVM_OPT = "kvm.aircon.#";
  public static final String AIRCON_KVM_ADD = AIRCON + RoutingOperation.ADD;
  public static final String AIRCON_KVM_DELETE = AIRCON + RoutingOperation.DELETE;
  public static final String AIRCON_KVM_REFRESH_EXTEND_DEVICE =
      AIRCON + RoutingOperation.EXT_REFRESH;
  public static final String AIRCON_KVM_REFRESH_CONFIG = AIRCON + RoutingOperation.CONFIG_REFRESH;
  public static final String AIRCON_KVM_GET_VW_PANELS = AIRCON + RoutingOperation.VW_PANELS_GET;
  public static final String AIRCON_KVM_OPEN_VW_PANEL = AIRCON + RoutingOperation.VW_PANEL_OPEN;
  public static final String AIRCON_KVM_CLOSE_VW_PANEL = AIRCON + RoutingOperation.VW_PANEL_CLOSE;
  public static final String AIRCON_KVM_CLOSE_ALL_VW_PANEL =
      AIRCON + RoutingOperation.VW_PANELS_CLOSE;
  public static final String AIRCON_KVM_OPEN_VW_PANELS = AIRCON + RoutingOperation.VW_PANELS_OPEN;
  public static final String AIRCON_KVM_MOVE_VW_PANEL = AIRCON + RoutingOperation.VW_PANEL_MOVE;
  public static final String AIRCON_KVM_SWAP_VW_PANEL_LAYER =
      AIRCON + RoutingOperation.VW_PANEL_LAYER_SWAP;
  public static final String AIRCON_KVM_GET_SEAT_PANELS = AIRCON + RoutingOperation.SEAT_PANELS_GET;
  public static final String AIRCON_KVM_CLOSE_ALL_SEAT_PANEL =
      AIRCON + RoutingOperation.SEAT_PANELS_CLOSE;
  public static final String AIRCON_KVM_CLOSE_SEAT_PANEL = AIRCON + RoutingOperation.SEAT_PANEL_CLOSE;
  public static final String AIRCON_KVM_OPEN_SEAT_PANELS =
      AIRCON + RoutingOperation.SEAT_PANELS_OPEN;
  public static final String AIRCON_KVM_GET_TX_SNAPSHOT = AIRCON + RoutingOperation.SNAPSHOT_TX_GET;
  public static final String AIRCON_KVM_SEAT_OPEN_TX = AIRCON + RoutingOperation.SEAT_PANEL_OPEN;
  public static final String AIRCON_CONNECT_TX_ENCODER = AIRCON
          + RoutingOperation.TX_ENCODER_CONNECT;
  public static final String AIRCON_KVM_GET_SNAPSHOT = AIRCON + RoutingOperation.SNAPSHOT_GET;

  public static final String HIKVISION = "server.hikvision.";
  public static final String HIKVISION_SERVER_OPT = "server.hikvision.#";
  public static final String HIKVISION_SERVER_REFRESH_CONFIG =
      HIKVISION + RoutingOperation.CONFIG_REFRESH;
  public static final String HIKVISION_SERVER_GET_TX_SNAPSHOT =
      HIKVISION + RoutingOperation.SNAPSHOT_TX_GET;

  /**
   * GB Gateway.
   */
  public static final String GB_GATEWAY = "server.gbgateway.";
  public static final String GB_GATEWAY_SERVER_OPT = "server.gbgateway.#";
  public static final String GB_GATEWAY_SERVER_REFRESH_CONFIG =
      GB_GATEWAY + RoutingOperation.CONFIG_REFRESH;
  /**
   * Monitor.
   */
  public static final String MONITOR = "server.monitor.";
  public static final String MONITOR_SERVER_OPT = "server.monitor.#";
  public static final String MONITOR_DEVICE_ALARM_BUILD = MONITOR + "device.alarm.build";
  public static final String MONITOR_DEVICE_GET_VALUE = MONITOR + "device.value.get";
  /**
   * WebServer.
   */
  public static final String WEB_SERVER = "server.web.";
  public static final String WEB_SERVER_OPT = "server.web.#";
  public static final String WEB_SERVER_CHANGE_ALARM_STATUS = WEB_SERVER + "alarm.status.change";
  /**
   * ptz.
   */
  public static final String PTZ = "server.ptz.";
  public static final String PTZ_SERVER_OPT = "server.ptz.#";
  public static final String PTZ_CONTROL = PTZ + "control";

  /**
   * tsingli.
   */
  public static final String TSINGLI = "server.tsingli.";
  public static final String TSINGLI_SERVER_OPT = "server.tsingli.#";
  public static final String TSINGLI_SERVER_EXECUTE_COMMAND = TSINGLI + RoutingOperation.EXECUTE_COMMAND;


  /**
   * tsingli.
   */
  public static final String KVM_SWITCHER = "kvm.switcher.";
  public static final String KVM_SWITCHER_OPT = "kvm.switcher.#";
  public static final String KVM_SWITCHER_SCAN = KVM_SWITCHER + RoutingOperation.SCAN;
  public static final String KVM_SWITCHER_SET_IP = KVM_SWITCHER + RoutingOperation.SET_IP;
  public static final String KVM_SWITCHER_REGISTER = KVM_SWITCHER + RoutingOperation.REGISTER;
  public static final String KVM_SWITCHER_UNREGISTER = KVM_SWITCHER + RoutingOperation.UNREGISTER;
  public static final String KVM_SWITCHER_CHECK_UPGRADE_PACKAGE =
          KVM_SWITCHER + RoutingOperation.CHECK_UPGRADE_PACKAGE;
  public static final String KVM_SWITCHER_DELETE_UPGRADE_PACKAGE =
          KVM_SWITCHER + RoutingOperation.DELETE_UPGRADE_PACKAGE;
  public static final String KVM_SWITCHER_GET_UPGRADE_LIST =
          KVM_SWITCHER + RoutingOperation.GET_UPGRADE_LIST;
}
