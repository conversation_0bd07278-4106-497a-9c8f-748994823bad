package com.mediacomm.system.variable;

/**
 * web服务Http根路径.
 */
public class ResUrlDef {
  /**
   * 接口版本+项目名称.
   */
  public static final String API_V = "v1";
  public static final String ROOT = "/skylink-api";
  /**
   * login.
   */
  public static final String LOGIN = ROOT + "/login";
  /**
   * 刷新token.
   */
  public static final String TOKEN_REFRESH = ROOT + "/author/refresh";
  /**
   * 登出.
   */
  public static final String LOGOUT = ROOT + "/logout";
  /**
   * 监控事件分发.
   */
  public static final String MONITOR = ROOT + "/rpc/monitor";
  /**
   * 用户管理>部门管理.
   */
  public static final String DEPARTMENT = ROOT + "/user/department";
  /**
   * 用户管理>人员管理.
   */
  public static final String PERSONNEL = ROOT + "/user/personnel";
  /**
   * 用户管理>账号管理.
   */
  public static final String ACCOUNT = ROOT + "/user/account";
  /**
   * 用户管理>角色管理.
   */
  public static final String ROLE = ROOT + "/user/role";
  /**
   * 基础信息配置>设备分组.
   */
  public static final String DEVICE_GROUP = ROOT + "/general/device-group";
  /**
   * 基础信息配置>设备型号.
   */
  public static final String DEVICE_MODEL = ROOT + "/general/device-model";
  /**
   * 基础信息配置>设备型号(信号量).
   */
  public static final String DEVICE_MODEL_SIGNAL = ROOT + "/general/device-model-signal";
  /**
   * 基础信息配置>房间位置配置.
   */
  public static final String POSITION_GROUP = ROOT + "/general/position-group";
  /**
   * 配置管理>KVM主机.
   */
  public static final String KVM = ROOT + "/kvm/kvm-master";
  /**
   * 配置管理>KVM外设配置.
   */
  public static final String EXTEND = ROOT + "/kvm/kvm-extend-device";
  /**
   * 配置管理>KVM板卡配置.
   */
  public static final String SLOT = ROOT + "/kvm/kvm-slot";
  /**
   * 配置管理>Tx分组配置 或 Rx分组配置.
   */
  public static final String ASSET_GROUP = ROOT + "/kvm/kvm-asset-group";
  /**
   * 配置管理>KVM用户管理.
   */
  public static final String KVM_USER = ROOT + "/kvm/kvm-user";
  /**
   * 监控设备配置.
   */
  public static final String ENV_DEVICE = ROOT + "/env/env-device";
  /**
   * 历史记录查询>告警事件处理.
   */
  public static final String ALARM = ROOT + "/history/alarm";
  /**
   * 告警处理日志.
   */
  public static final String ALARM_REPAIR_DESC = ROOT + "/history/alarm-repair-desc";
  /**
   * 历史记录查询>屏蔽告警.
   */
  public static final String DISABLED_ALARM = ROOT + "/history/disable-alarm";
  /**
   * 历史记录查询>系统操作日志.
   */
  public static final String OPERATION_LOG = ROOT + "/history/operate-log";
  /**
   * 历史记录查询>KVM切换日志.
   */
  public static final String SWITCH_LOG = ROOT + "/history/switch-log";
  /**
   * 历史记录查询>KVM操作日志.
   */
  public static final String KVM_OPERATION_LOG = ROOT + "/history/kvm-operation-log";
  /**
   * 可视化配置>坐席管理.
   */
  public static final String KVM_SEAT = ROOT + "/visualization/kvm-seat";
  /**
   * 可视化配置>可视化终端.
   */
  public static final String VIS_DEVICE = ROOT + "/visualization/visualization-device";
  /**
   * 可视化配置>大屏管理.
   */
  public static final String KVM_VIDEO_WALL = ROOT + "/visualization/kvm-video-wall";
  /**
   * 可视化配置>同屏管理.
   */
  public static final String SYNC_VIDEO_WALL = ROOT + "/visualization/sync-video-wall";
  /**
   * 可视化配置>可视化界面.
   */
  public static final String VIS_SCENE = ROOT + "/visualization/visualization-scene";
  /**
   * 可视化配置>可视化布局.
   */
  public static final String VIS_LAYOUT = ROOT + "/visualization/visualization-layout";
  /**
   * 控件状态.
   */
  public static final String VIS_UI = ROOT + "/visualization/ui";
  /**
   * 图片素材.
   */
  public static final String PIC_MATERIAL = ROOT + "/picture-material";
  /**
   * 图片.
   */
  public static final String PIC = ROOT + "/picture";
  /**
   * 大屏预案.
   */
  public static final String VIS_WALL_SCENE = ROOT + "/visualization/video-wall/scene";
  /**
   * 坐席预案.
   */
  public static final String VIS_SEAT_SCENE = ROOT + "/visualization/seat/scene";
  /**
   * 大屏横幅配置.
   */
  public static final String VIS_WALL_BANNER = ROOT + "/visualization/video-wall/banner";
  /**
   * 远程调用主机.
   */
  public static final String KVM_RPC = ROOT + "/rpc/kvm-master";
  /**
   * 远程调用Kvm外设.
   */
  public static final String KVM_ASSET_RPC = ROOT + "/rpc/kvm-asset";
  /**
   * 远程调用大屏.
   */
  public static final String KVM_VIDEO_WALL_RPC = ROOT + "/rpc/video-wall";
  /**
   * 远程调用坐席.
   */
  public static final String KVM_SEAT_RPC = ROOT + "/rpc/seat";
  /**
   * 远程调用摄像头.
   */
  public static final String PTZ_RPC = ROOT + "/rpc/ptz";

  /**
   * 远程调用清立.
   */
  public static final String TSINGLI_RPC = ROOT + "/rpc/tsingli";
  /**
   * 远程指令发送.
   */
  public static final String PROTOCOL_COMMAND_RPC = ROOT + "/rpc/protocol/command";
  /**
   * 外设升级管理.
   */
  public static final String PERIPHERAL_UPGRADE = ROOT + "/peripheral/upgrade";
  /**
   * 全局.
   */
  public static final String GLOBAL_COMMON = "/global";
  /**
   * 系统安全运维.
   */
  public static final String SYSTEM_SEC_OPT = ROOT + "/system/sec";
  /**
   * 后台管理页面websocket.
   */
  public static final String WS_BACKSTAGE_NOTICE = ROOT + "/notice/backstage";
  /**
   * 终端websocket.
   */
  public static final String WS_TERMINAL_NOTICE = ROOT + "/notice/terminal";
  /**
   * 二进制文件.
   */
  public static final String WS_FILE_NOTICE = ROOT + "/notice/file";

  /**
   * 远程调用Kvm切换器.
   */
  public static final String KVM_SWITCHER_RPC = ROOT + "/rpc/kvm-switcher";
}
