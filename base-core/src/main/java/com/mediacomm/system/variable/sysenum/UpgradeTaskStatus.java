package com.mediacomm.system.variable.sysenum;

/**
 * 升级任务状态.
 */
public enum UpgradeTaskStatus {
    /**
     * 等待升级.
     */
    WAITING(0, "等待升级"),
    /**
     * 升级中.
     */
    UPGRADING(1, "升级中"),
    /**
     * 升级成功.
     */
    SUCCESS(2, "升级成功"),
    /**
     * 升级失败.
     */
    FAILED(3, "升级失败"),
    /**
     * 升级取消.
     */
    CANCEL(4, "升级取消"),
    /**
     * 升级超时.
     */
    TIMEOUT(5, "升级超时");

  private final Integer code;
  private final String description;

  UpgradeTaskStatus(Integer code, String description) {
    this.code = code;
    this.description = description;
  }

  public Integer getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }
}
