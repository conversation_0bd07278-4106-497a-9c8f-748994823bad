package com.mediacomm.system.variable.sysenum;

/**
 * redis key prefix.
 */
public class RedisSignalKey {
  // 信号量
  /**
   * .
   */
  public static final String LINK_STATUS = "link.status";
  public static final String EDID_VALID = "edidValid";
  public static final String LINK_STATUS_1 = "linkStatus.1";
  public static final String LINK_STATUS_2 = "linkStatus.2";
  public static final String CONNECTION_STATUS = "connectionStatus";
  /**
   * 电压.
   */
  public static final String VOLT_STATUS = "voltStatus";
  /**
   * 插槽插入板卡的类型.
   */
  public static final String CARD_TYPE = "cardType";
  /**
   * 插槽插入板卡的类型.
   */
  public static final String CARD_POSITION = "cardPosition";
  /**
   * 视频线接线状态.
   */
  public static final String VIDEO_LINE_STATUS = "videoLineStatus";
  /**
   * 链路状态.
   */
  public static final String LINE_STATUS = "lineStatus";
  /**
   * .
   */
  public static final String POWER_1_STATUS = "powerStatus.1";
  public static final String POWER_2_STATUS = "powerStatus.2";
  public static final String POWER_3_STATUS = "powerStatus.3";
  public static final String POWER_4_STATUS = "powerStatus.4";
  public static final String CPU_RATE = "cpuRate";
  public static final String MEM_RATE = "memRate";
  public static final String DISK_RATE = "diskRate";
  public static final String TEMPERATURE = "temperature";
  public static final String RESOLUTION = "videoResolution";
  public static final String TOTAL_RX_NUMBER = "total.rx.number";
  public static final String TOTAL_TX_NUMBER = "total.tx.number";
  public static final String ONLINE_RX_NUMBER = "online.rx.number";
  public static final String ONLINE_TX_NUMBER = "online.tx.number";
  public static final String FAN_1_ONLINE = "fanOnline.1";
  public static final String FAN_2_ONLINE = "fanOnline.2";
  public static final String FAN_3_ONLINE = "fanOnline.3";
  public static final String FAN_1_SPEED = "fanSpeed.1";
  public static final String FAN_2_SPEED = "fanSpeed.2";
  public static final String FAN_3_SPEED = "fanSpeed.3";
  public static final String PORT_STATUS = "port.status";
  public static final String SLOT_STATUS = "slot.status";

  // 辅助描述信息
  /**
   * 最近一次更新时间.
   */
  public static final String UPDATE_TIME = "last.update.time";

  public static String getDeviceStatusKey(String deviceType, String id) {
    return String.format("signal:%s:%s", deviceType, id);
  }

  public static String getDeviceDecKey(String deviceType, String id) {
    return String.format("desc:%s:%s", deviceType, id);
  }

  public static String buildNewSignalKey(String signalKey, int num) {
    return String.format("%s.%d", signalKey, num);
  }

}
