package com.mediacomm.system.annotation;

import com.mediacomm.system.variable.sysenum.FillType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * .
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface JacksonFill {
  FillType value() default FillType.BRACE;
}
