package com.mediacomm.system.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * Ip地址格式验证类.
 */
public class IpValidator implements ConstraintValidator<IpValidation, String> {

  @Override
  public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
    Pattern ipAddrPattern = Pattern.compile("^([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])"
        + "(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}$");
    if (s != null && !s.isEmpty()) {
      return ipAddrPattern.matcher(s).matches();
    }
    return false;
  }
}
