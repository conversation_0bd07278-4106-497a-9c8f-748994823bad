package com.mediacomm.system.base.kvm;

import java.net.URI;
import java.net.URISyntaxException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@Getter
@Setter
public abstract class KvmWebSocket {
  private boolean exit = false;

  public abstract void onMessage(String msg);

  public abstract String getWebSocketFullPath();

  public abstract String getMasterIp();

  public URI makeUri() {
    try {
      return new URI(getWebSocketFullPath());
    } catch (URISyntaxException e) {
      log.error("Error uri format {}", getWebSocketFullPath(), e);
    }
    return null;
  }
}
