package com.mediacomm.system.base.controller;

import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Controller基类.
 *
 * @author: Wu<PERSON><PERSON><PERSON><PERSON>
 */
@Slf4j
@ApiResponse(responseCode = "200", description = "成功返回")
public class SkyLinkController<T, S extends IService<T>> {
  @Autowired
  protected S service;

}
