package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VisualizationLayout;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

/**
 * 电视墙.
 */
@Mapper
public interface KvmVideoWallMapper extends BaseMapper<KvmVideoWall> {
  @Select("select wall.*, dm.model_name, dm.device_type, master.name as master_name, "
      + "pg.name as room_name from kvm_video_wall as wall "
      + "inner join kvm_master as master on (wall.master_id = master.master_id) "
      + "inner join device_model as dm on (wall.device_model = dm.model_id) "
      + "inner join position_group as pg on (pg.position_id = #{roomId}) "
      + "where wall.room_id = #{roomId}")
  @Results(id = "videoWallMap", value = {
      @Result(property = "decoders", column = "decoders", typeHandler = JacksonTypeHandler.class),
      @Result(property = "properties", column = "properties",
          typeHandler = JacksonTypeHandler.class),
      @Result(property = "collectorProperties", column = "collector_properties",
          typeHandler = JacksonTypeHandler.class),
      @Result(property = "fullScreen", column = "full_screen",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "screenEnable", column = "screen_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "layoutEnable", column = "layout_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "roamingEnable", column = "roaming_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "swapLayerEnable", column = "swapLayer_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "staticEnable", column = "static_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT),
      @Result(property = "pollingScenesEnable", column = "polling_scenes_enable",
          typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
      })
  Collection<KvmVideoWall> findByRoomId(@Param("roomId") Integer roomId);

  @Select("select wall.*, dm.model_name, dm.device_type, master.name as master_name "
      + "from kvm_video_wall as wall, device_model as dm, kvm_master as master "
      + "where wall.master_id = master.master_id and dm.model_id = wall.device_model "
      + "and wall.room_id is NULL")
  @ResultMap(value = "videoWallMap")
  Collection<KvmVideoWall> findNotInRoom();

  @Delete({"<script>",
      "delete from wall_layout_merge where wid = #{wallId} and lid in ",
      "<foreach collection='layoutIds' item='layoutId' open='(' separator=',' close=')'>",
      "#{layoutId}",
      "</foreach>",
      "</script>"})
  boolean delFromMerge(@Param("wallId") Integer wallId,
                       @Param("layoutIds") Collection<Integer> layoutIds);

  @Update("update kvm_video_wall set polling_scenes_enable = #{enable} "
      + "where wall_id = #{wallId}")
  void updatePollingStatusValue(Integer wallId, boolean enable);

  @Insert({"<script>",
      "insert into wall_layout_merge (wid, lid) VALUES ",
      "<foreach collection='layoutIds' item='lid' separator=','>",
      "(#{wallId}, #{lid})",
      "</foreach>",
      "</script>"})
  boolean insertToMerge(Integer wallId, Collection<Integer> layoutIds);

  @Select("select * from visualization_layout where layout_id "
      + "in (select lid from wall_layout_merge WHERE wid = #{wallId});")
  @Results(id = "layoutMap", value = {
      @Result(property = "layoutData", column = "layout_data",
          typeHandler = JacksonTypeHandler.class),
  })
  Collection<VisualizationLayout> findLayoutByWallId(Integer wallId);
}
