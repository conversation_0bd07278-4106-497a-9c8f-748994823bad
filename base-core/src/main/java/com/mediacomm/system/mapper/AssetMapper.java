package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.vo.KvmAssetVo;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * 外围设备.
 */
@Mapper
public interface AssetMapper extends BaseMapper<KvmAsset> {

  /**
   * 通过Tx/Rx分组Id查找分组内外设信息.
   *
   * @param gid 分组Id.
   * @return Collection.
   */
  @Select("select ka.* from kvm_asset as ka INNER JOIN asset_group_merge as am on "
      + " ( am.gid = #{gid} and am.aid = ka.asset_id ) ORDER BY am.seq_in_group")
  @Results(id = "kvmAssetMap", value = {
      @Result(property = "properties", column = "properties",
              typeHandler = JacksonTypeHandler.class),
      @Result(property = "collectorProperties", column = "collector_properties",
              typeHandler = JacksonTypeHandler.class)
  })
  Collection<KvmAsset> findByGroupId(@Param("gid") Integer gid);

  @Select("select ka.*, km.name as master_name, dm.device_type, dm.model_name, dm.sub_system as sub_system_type "
          + "from kvm_asset as ka "
          + "inner join kvm_master as km on (km.master_id = #{masterId}) "
          + "inner join device_model as dm on (ka.device_model = dm.model_id) "
          + "where ka.master_id = #{masterId}")
  @Results(id = "kvmAssetVoMap", value = {
      @Result(property = "properties", column = "properties",
              typeHandler = JacksonTypeHandler.class),
      @Result(property = "collectorProperties", column = "collector_properties",
              typeHandler = JacksonTypeHandler.class)
  })
  Collection<KvmAssetVo> allByMasterId(@Param("masterId") String masterId);
}
