package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.VideoWallScene;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * 电视墙预案.
 */
@Mapper
public interface VideoWallSceneMapper extends BaseMapper<VideoWallScene> {
  /**
   * 修改预案的轮询状态.
   *
   * @param sceneId 预案ID.
   * @param enable 轮询状态.
   */
  @Update("update video_wall_scene set polling_scenes_enable = #{enable} "
      + "where scene_id = #{sceneId}")
  void updatePollingScenesEnableValue(Integer sceneId, boolean enable);
}
