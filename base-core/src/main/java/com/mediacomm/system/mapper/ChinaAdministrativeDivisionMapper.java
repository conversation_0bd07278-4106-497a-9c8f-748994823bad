package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.ChinaAdministrativeDivision;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 中国行政区划Mapper.
 */
@Mapper
public interface ChinaAdministrativeDivisionMapper extends BaseMapper<ChinaAdministrativeDivision> {

  /**
   * 根据一个父级行政区划代码，查询出它所有直属的下一级行政区划中，那些实际拥有设备的区划列表.
   *
   * @param parentCode 父级行政区划代码
   * @return 符合条件的子级行政区划列表
   */
  @Select("""
SELECT DISTINCT d.*
FROM china_administrative_divisions d
WHERE d.parent_code = #{parentCode}
  AND EXISTS (
    SELECT 1
    FROM kvm_asset a
    WHERE a.hardcode IS NOT NULL AND a.hardcode LIKE '%.%'
      AND SUBSTRING_INDEX(a.hardcode, '.', -1) LIKE CONCAT(
          -- 使用CASE语句判断并截取有效的前缀
          CASE
              -- 如果是省级代码 (后4位是'0000'), 取前2位
              WHEN RIGHT(d.code, 4) = '0000' THEN SUBSTRING(d.code, 1, 2)
              -- 如果是市级代码 (后2位是'00'), 取前4位
              WHEN RIGHT(d.code, 2) = '00' THEN SUBSTRING(d.code, 1, 4)
              -- 否则 (区/县级), 取全部6位代码
              ELSE d.code
          END,
          '%' -- 拼接'%'进行前缀匹配
      )
)
ORDER BY d.code;
  """)
  List<ChinaAdministrativeDivision> findChildrenRegionsWithDevices(@Param("parentCode") String parentCode);

  /**
   * 查询所有拥有（直接或间接）设备的顶级（省级）行政区划列表.
   *
   * @return 符合条件的顶级行政区划列表
   */
  @Select("""
SELECT DISTINCT d.*
FROM china_administrative_divisions d
WHERE d.parent_code IS NULL AND EXISTS (
    SELECT 1
    FROM kvm_asset a
    WHERE a.hardcode IS NOT NULL AND a.hardcode LIKE '%.%'
      -- 比较双方的前两位字符(省级)是否相等
      AND SUBSTRING(SUBSTRING_INDEX(a.hardcode, '.', -1), 1, 2) = SUBSTRING(d.code, 1, 2)
)
ORDER BY d.code;
  """)
  List<ChinaAdministrativeDivision> findTopLevelRegionsWithDevices();
}
