package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.Permission;
import java.util.Collection;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 系统权限.
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {
  @Select("select * from permission where menu_id in ("
      + "select pid from role_permission_merge where rid = #{roleId})")
  Collection<Permission> findPermissionsByRoleId(Integer roleId);

  @Insert({"<script>",
      "insert into role_permission_merge (rid, pid) VALUES ",
      "<foreach collection='permissionIds' item='permissionId' separator=','>",
      "(#{roleId}, #{permissionId})",
      "</foreach>",
      "</script>"})
  boolean insertToMerge(Integer roleId, Collection<Integer> permissionIds);

  @Delete("delete from role_permission_merge where rid = #{roleId}")
  boolean delFromMerge(Integer roleId);
}
