package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mediacomm.entity.dao.DefaultPicture;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * .
 */
@Mapper
public interface DefaultPicMapper extends BaseMapper<DefaultPicture> {

  @Select("select dp.*, pic.content from default_picture as dp "
          + "inner join picture as pic on (dp.pic_hash = pic.id) where name = #{name} ")
  DefaultPicture findByName(String name);
}
