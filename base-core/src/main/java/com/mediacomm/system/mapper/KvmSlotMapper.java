package com.mediacomm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.vo.KvmSlotVo;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * .
 */
@Mapper
public interface KvmSlotMapper extends BaseMapper<KvmSlot> {

  /**
   * 通过主机Id查看关联板卡信息.
   *
   * @param masterId 主机Id.
   * @return Collection.
   */
  @Select("select ks.*, km.name as master_name, dm.device_type, dm.model_name from kvm_slot ks, "
      + "kvm_master km, device_model dm where ks.master_id = #{masterId}"
      + " and km.master_id = #{masterId} "
      + "and ks.device_model = dm.model_id")
  @Results(id = "slotMap", value = {
      @Result(property = "properties", column = "properties",
          typeHandler = JacksonTypeHandler.class),
      @Result(property = "collectorProperties", column = "collector_properties",
          typeHandler = JacksonTypeHandler.class)
  })
  Collection<KvmSlotVo> findByMasterId(@Param("masterId") String masterId);

  @Select("select ks.*, km.name as master_name, dm.device_type, dm.model_name from kvm_slot ks, "
      + "kvm_master km, device_model dm where ks.master_id = km.master_id"
      + "and ks.device_model = dm.model_id")
  @ResultMap(value = "slotMap")
  Collection<KvmSlotVo> findAll();
}
