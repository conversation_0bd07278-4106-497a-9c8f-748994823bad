package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.entity.vo.KvmSeatVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmSeatMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmSeatService extends SkyLinkServiceImpl<KvmSeatMapper, KvmSeat> {
  @Autowired
  KvmMasterService masterService;

  /**
   * 根据坐席组筛选或查询所有坐席.
   *
   * @param positionId 坐席组Id.
   * @return Collection.
   */
  public Collection<KvmSeatVo> allByPositionId(Integer positionId) {
    LambdaQueryWrapper<KvmSeat> wrapper = Wrappers.lambdaQuery(KvmSeat.class);
    if (positionId != null) {
      wrapper.eq(KvmSeat::getPositionId, positionId);
    }
    Collection<KvmSeatVo> vos = new ArrayList<>();
    list(wrapper).forEach(kvmSeat -> {
      KvmSeatVo vo = Optional.ofNullable(kvmSeat).map(KvmSeatVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addKvmSeatInfo);
      vos.add(vo);
    });
    return vos;
  }

  /**
   * 根据主机ID获取所有该主机关联的坐席.
   */
  public Collection<KvmSeat> allByMasterId(String masterId) {
    LambdaQueryWrapper<KvmSeat> wrapper = Wrappers.lambdaQuery(KvmSeat.class)
        .eq(KvmSeat::getMasterId, masterId);
    return list(wrapper);
  }

  /**
   * 根据主控ID和解码器ID获取对应的KVM解码器实例
   *
   * @param masterId  主控设备唯一标识符，用于查询关联的KVM座位
   * @param decoderId 目标解码器的设备ID，用于精确匹配解码器
   * @return 匹配的KVM解码器实例，若未找到则返回null
   */
  public KvmSeatDecoder getDecoderByMasterIdAndDecoderId(String masterId, Integer decoderId) {
      return allByMasterId(masterId).stream()
              .flatMap(kvmSeat -> kvmSeat.getDecoders().stream())
              .filter(decoder -> Objects.equals(decoder.getDeviceId(), decoderId))
              .findAny()
              .orElse(null);
  }


  /**
   * 通过Id查询指定坐席.
   *
   * @param seatId 坐席Id.
   * @return .
   */
  public KvmSeatVo oneById(Integer seatId) {
    KvmSeat seat = getById(seatId);
    KvmSeatVo vo = Optional.ofNullable(seat).map(KvmSeatVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addKvmSeatInfo);
    return vo;
  }

  /**
   * 更新或保存新的坐席信息.
   *
   * @param seat 实体对象.
   * @return 成功/失败.
   */
  public boolean saveOrUpdate(KvmSeat seat, String masterId) {
    LambdaQueryWrapper<KvmSeat> lambdaQueryWrapper = Wrappers.lambdaQuery(KvmSeat.class)
        .eq(KvmSeat::getMasterId, masterId)
        .and(wrp -> wrp.eq(KvmSeat::getDeviceId, seat.getDeviceId()));
    KvmSeat old = getOne(lambdaQueryWrapper);
    if (old != null) {
      seat.setPositionId(old.getPositionId());
      seat.setOptModelEnable(old.isOptModelEnable());
      seat.setSeatId(old.getSeatId());
      return updateById(seat);
    } else {
      return save(seat);
    }
  }

  /**
   * 通过主机Id及坐席实际Id查询.
   *
   * @param masterId 主机Id.
   * @param deviceId 坐席实际Id.
   * @return KvmSeat.
   */
  public KvmSeat oneByMasterIdAndDeviceId(String masterId, Integer deviceId) {
    LambdaQueryWrapper<KvmSeat> wrapper = Wrappers.lambdaQuery(KvmSeat.class)
        .eq(KvmSeat::getMasterId, masterId)
        .and(wrap -> wrap.eq(KvmSeat::getDeviceId, deviceId));
    return getOne(wrapper);
  }

  public Collection<VisualizationLayout> allLayoutsBySeatId(Integer seatId) {
    return baseMapper.findLayoutBySeatId(seatId);
  }

  public boolean saveBatchLayoutById(Integer seatId, Collection<Integer> layoutIds) {
    return baseMapper.insertToMerge(seatId, layoutIds);
  }

  public boolean delBatchLayoutById(Integer seatId, Collection<Integer> layoutIds) {
    return baseMapper.delFromMerge(seatId, layoutIds);
  }

  private void addKvmSeatInfo(KvmSeatVo vo) {
    KvmMaster master = masterService.getById(vo.getMasterId());
    Optional.ofNullable(master).ifPresent(m -> vo.setMasterName(m.getName()));
  }
}
