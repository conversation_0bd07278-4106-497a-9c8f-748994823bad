package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.DeviceGroup;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.DeviceGroupMapper;
import com.mediacomm.system.mapper.DeviceModelMapper;
import com.mediacomm.system.mapper.KvmMasterMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmMasterService extends SkyLinkServiceImpl<KvmMasterMapper, KvmMaster> {

  @Autowired
  DeviceGroupMapper groupMapper;
  @Autowired
  DeviceModelMapper modelMapper;

  /**
   * 联表查询所有主机及关联分组和型号.
   *
   * @return Collection.
   */
  public Collection<KvmMasterVo> all() {
    Collection<KvmMaster> kvmMasters = list();
    Collection<KvmMasterVo> kvmMasterVos = new ArrayList<>();
    kvmMasters.forEach(master -> {
      KvmMasterVo vo = Optional.ofNullable(master).map(KvmMasterVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addKvmMasterInfo);
      kvmMasterVos.add(vo);
    });
    return kvmMasterVos;
  }

  /**
   * 联表查询指定id的主机及关联分组和型号.
   *
   * @param id 主机Id.
   * @return KvmMasterVo.
   */
  public KvmMasterVo oneById(String id) {
    KvmMaster master = getById(id);
    KvmMasterVo vo = Optional.ofNullable(master).map(KvmMasterVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addKvmMasterInfo);
    return vo;
  }

  private void addKvmMasterInfo(KvmMasterVo vo) {
    DeviceGroup group = groupMapper.selectById(vo.getDeviceGroup());
    DeviceModel model = modelMapper.selectById(vo.getDeviceModel());
    Optional.ofNullable(group).ifPresent(g -> vo.setGroupName(g.getGroupName()));
    Optional.ofNullable(model).ifPresent(m -> {
      vo.setModelName(m.getModelName());
      vo.setDeviceType(DeviceType.valueOf(m.getDeviceType()));
    });
  }

  /**
   * 获取所有指定设备类型的kvm主机.
   */
  public Collection<KvmMaster> allByDeviceModel(Integer deviceModelId) {
    LambdaQueryWrapper<KvmMaster> wrapper = Wrappers.lambdaQuery(KvmMaster.class)
        .eq(KvmMaster::getDeviceModel, deviceModelId);
    return super.list(wrapper);
  }
}
