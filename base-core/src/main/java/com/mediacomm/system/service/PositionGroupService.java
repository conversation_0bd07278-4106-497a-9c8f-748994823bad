package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.PositionGroup;
import com.mediacomm.entity.dao.PositionLevel;
import com.mediacomm.entity.vo.PositionGroupVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PositionGroupMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class PositionGroupService extends SkyLinkServiceImpl<PositionGroupMapper, PositionGroup> {

  @Autowired
  PositionLevelService levelService;

  /**
   * 联表查询所有房间.
   *
   * @return Collection.
   */
  public Collection<PositionGroupVo> all(Integer parentId) {
    Collection<PositionGroup> groups = null;
    if (parentId != null) {
      LambdaQueryWrapper<PositionGroup> wrapper = Wrappers.lambdaQuery(PositionGroup.class)
          .eq(PositionGroup::getParentId, parentId);
      groups = list(wrapper);
    } else {
      groups = list();
    }

    Collection<PositionGroupVo> vos = new ArrayList<>();
    groups.forEach(group -> {
      PositionGroupVo vo = Optional.ofNullable(group).map(PositionGroupVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addLevelInfo);
      vos.add(vo);
    });
    return vos;
  }

  private void addLevelInfo(PositionGroupVo vo) {
    PositionLevel level = levelService.getById(vo.getPositionLevel());
    Optional.ofNullable(level).ifPresent(
        positionLevel -> vo.setLevelTitle(positionLevel.getLevelTitle()));
  }
}
