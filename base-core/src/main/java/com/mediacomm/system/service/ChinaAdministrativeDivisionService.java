package com.mediacomm.system.service;

import com.mediacomm.entity.dao.ChinaAdministrativeDivision;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.ChinaAdministrativeDivisionMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 中国行政区划服务.
 */
@Slf4j
@Service
public class ChinaAdministrativeDivisionService
    extends SkyLinkServiceImpl<ChinaAdministrativeDivisionMapper, ChinaAdministrativeDivision> {

  /**
   * 根据一个父级行政区划代码，查询出它所有直属的下一级行政区划中，那些实际拥有设备的区划列表.
   *
   * @param parentCode 父级行政区划代码
   * @return 符合条件的子级行政区划列表
   */
  public List<ChinaAdministrativeDivision> getChildDivisions(String parentCode) {
    DeviceType.GB_GATEWAY_IPC.getDeviceTypeId();
    return baseMapper.findChildrenRegionsWithDevices(parentCode);
  }

  /**
   * 查询所有拥有（直接或间接）设备的顶级（省级）行政区划列表.
   *
   * @return 符合条件的顶级行政区划列表
   */
  public List<ChinaAdministrativeDivision> getTopLevelDivisions() {
    return baseMapper.findTopLevelRegionsWithDevices();
  }
}
