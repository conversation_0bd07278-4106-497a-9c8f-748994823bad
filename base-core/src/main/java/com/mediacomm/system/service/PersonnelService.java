package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.Department;
import com.mediacomm.entity.dao.Personnel;
import com.mediacomm.entity.vo.PersonnelVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PersonnelMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class PersonnelService extends SkyLinkServiceImpl<PersonnelMapper, Personnel> {
  @Autowired
  DepartmentService departmentService;

  /**
   * 查询部门内人员信息.
   *
   * @param departmentId 部门Id.
   * @return Collection.
   */
  public Collection<Personnel> listByForeignKey(Integer departmentId) {
    LambdaQueryWrapper<Personnel> wrapper = Wrappers.lambdaQuery(Personnel.class)
        .eq(Personnel::getDepartment, departmentId);
    return list(wrapper);
  }

  /**
   * 根据部门ID和绑定条件查询Personnel对象的集合.
   *
   * @param departmentId 部门ID.
   * @return 符合条件的Personnel对象的集合.
   */
  public Collection<Personnel> listByDepartmentIdAndBound(Integer departmentId) {
    return baseMapper.findPersonnelByDepartmentAndUnused(departmentId);
  }

  /**
   * 联表查询所有人员及关联部门.
   *
   * @return Collection.
   */
  public Collection<PersonnelVo> all() {
    Collection<Personnel> personnels = list();
    Collection<PersonnelVo> personnelVos = new ArrayList<>();
    personnels.forEach(personnel -> {
      PersonnelVo personnelVo = Optional.ofNullable(personnel).map(PersonnelVo::new).orElse(null);
      Optional.ofNullable(personnelVo).ifPresent(this::addDepartmentInfo);
      personnelVos.add(personnelVo);
    });
    return personnelVos;
  }

  /**
   * 联表查询单个人员及部门.
   *
   * @param personnelId 人员Id.
   * @return PersonnelVo.
   */
  public PersonnelVo oneById(Integer personnelId) {
    Personnel personnel = getById(personnelId);
    PersonnelVo personnelVo = Optional.ofNullable(personnel).map(PersonnelVo::new).orElse(null);
    Optional.ofNullable(personnelVo).ifPresent(this::addDepartmentInfo);
    return personnelVo;
  }

  private void addDepartmentInfo(PersonnelVo vo) {
    LambdaQueryWrapper<Department> wrapper = Wrappers.lambdaQuery(Department.class)
        .eq(Department::getDepartmentId, vo.getDepartment());
    Department department = departmentService.getOne(wrapper);
    Optional.ofNullable(department).ifPresent(e ->
        vo.setDepartmentName(department.getDepartmentName()));
  }
}
