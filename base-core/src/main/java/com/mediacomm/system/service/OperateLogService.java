package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.mediacomm.entity.dao.OperateLog;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.OperateLogMapper;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class OperateLogService extends SkyLinkServiceImpl<OperateLogMapper, OperateLog> {

  /**
   * 根据条件分页查询.
   *
   * @param currentPage 当前页码.
   * @param pageSize 每页大小.
   * @param startTime 开始时间.
   * @param endTime 结束时间.
   * @param name 人员名称.
   * @return .
   */
  public PageResult<OperateLog> allByPageAndOther(Integer currentPage, Integer pageSize,
                                                              long startTime, long endTime, String name) {
    LambdaQueryWrapper<OperateLog> wrapper = Wrappers.lambdaQuery(OperateLog.class);
    wrapper.ge(OperateLog::getOperateTime, startTime)
        .and(wrap -> wrap.le(OperateLog::getOperateTime, endTime));
    if (!Strings.isNullOrEmpty(name)) {
      wrapper.and(wrap -> wrap.eq(OperateLog::getOperator, name));
    }
    Page<OperateLog> paramPage = new Page<>(currentPage, pageSize);
    IPage<OperateLog> pageData = page(paramPage, wrapper);
    return new PageResult<>(pageData.getCurrent(), pageData.getSize(), pageData.getTotal(),
        pageData.getPages(), pageData.getRecords());
  }
}
