package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSwitchLog;
import com.mediacomm.entity.vo.KvmSwitchLogVo;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmSwitchLogMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmSwitchLogService extends SkyLinkServiceImpl<KvmSwitchLogMapper, KvmSwitchLog> {
  @Autowired
  KvmMasterService masterService;

  public PageResult<KvmSwitchLogVo> allByPage(Integer currentPage, Integer pageSize, long startDate,
                                              long endDate, String rxName, String txName) {
    LambdaQueryWrapper<KvmSwitchLog> wrapper = Wrappers.lambdaQuery(KvmSwitchLog.class)
        .between(KvmSwitchLog::getSwitchTime, startDate, endDate);
    if (rxName!= null) {
      wrapper.and(w -> w.eq(KvmSwitchLog::getCon, rxName));
    }
    if (txName!= null) {
      wrapper.and(w -> w.eq(KvmSwitchLog::getCpu, txName));
    }
    Page<KvmSwitchLog> page = new Page<>(currentPage, pageSize);
    List<KvmSwitchLogVo> vos = new ArrayList<>();
    page(page, wrapper).getRecords().forEach(log -> {
      KvmSwitchLogVo vo = Optional.ofNullable(log).map(KvmSwitchLogVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addSwitchLogInfo);
      vos.add(vo);
    });
    return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(),
        page.getPages(), vos);
  }

  public Collection<KvmSwitchLogVo> allBetweenStartAndEndTime(long startDate, long endDate) {
    LambdaQueryWrapper<KvmSwitchLog> wrapper = Wrappers.lambdaQuery(KvmSwitchLog.class)
            .between(KvmSwitchLog::getSwitchTime, startDate, endDate);
    List<KvmSwitchLogVo> vos = new ArrayList<>();
    list(wrapper).forEach(log -> {
      KvmSwitchLogVo vo = Optional.ofNullable(log).map(KvmSwitchLogVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addSwitchLogInfo);
      vos.add(vo);
    });
    return vos;
  }

  private void addSwitchLogInfo(KvmSwitchLogVo vo) {
    KvmMaster master = masterService.getById(vo.getMasterId());
    Optional.ofNullable(master).ifPresent(m -> vo.setMasterName(master.getName()));
  }
}
