package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.Permission;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PermissionMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class PermissionService extends SkyLinkServiceImpl<PermissionMapper, Permission> {

  public Collection<Permission> allByRoleId(Integer role) {
    return baseMapper.findPermissionsByRoleId(role);
  }

  /**
   * 获取指定父级Id的子级权限.
   *
   * @param pid 父级权限Id.
   * @return .
   */
  public Collection<Permission> allByPid(Integer pid) {
    LambdaQueryWrapper<Permission> wrapper = Wrappers.lambdaQuery(Permission.class)
        .eq(Permission::getPid, pid);
    return list(wrapper);
  }

  public boolean delBatchPermissionByRoleId(Integer roleId) {
    return baseMapper.delFromMerge(roleId);
  }

  public boolean saveBatchPermissionByRoleId(Integer roleId, Collection<Integer> permissionIds) {
    return baseMapper.insertToMerge(roleId, permissionIds);
  }
}
