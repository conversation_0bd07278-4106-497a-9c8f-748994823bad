package com.mediacomm.system.service;

import com.mediacomm.entity.dao.PictureMaterial;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PictureMaterialMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class PictureMaterialService
    extends SkyLinkServiceImpl<PictureMaterialMapper, PictureMaterial> {

  public Collection<Long> allImageIds(Long imageId) {
    return baseMapper.findAllImageId(imageId);
  }
}
