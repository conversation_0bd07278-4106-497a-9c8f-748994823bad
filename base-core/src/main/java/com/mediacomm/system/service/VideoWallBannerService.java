package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.VideoWallBanner;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.VideoWallBannerMapper;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class VideoWallBannerService extends SkyLinkServiceImpl<VideoWallBannerMapper, VideoWallBanner> {

  /**
   * 查找电视墙的横幅.
   */
  public Collection<VideoWallBanner> listByVideoWallId(@NotNull Integer videoWallId) {
    LambdaQueryWrapper<VideoWallBanner> wrapper = Wrappers.lambdaQuery(VideoWallBanner.class)
            .eq(VideoWallBanner::getWallId, videoWallId);
    return list(wrapper);
  }
}
