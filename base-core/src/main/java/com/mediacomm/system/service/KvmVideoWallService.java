package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmVideoWallMapper;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
@Slf4j
public class KvmVideoWallService extends SkyLinkServiceImpl<KvmVideoWallMapper, KvmVideoWall> {
  @Autowired
  VisualizationLayoutService layoutService;

  /**
   * 通过房间Id筛选电视墙或获取所有电视墙.
   *
   * @param positionId 房间Id.
   * @return Collection.
   */
  public Collection<KvmVideoWall> allByPositionId(Integer positionId) {
    if (positionId != null) {
      return baseMapper.findByRoomId(positionId);
    } else {
      return baseMapper.findNotInRoom();
    }
  }

  /**
   * 通过主机Id及大屏实际Id查询.
   *
   * @param masterId 主机Id.
   * @param deviceId 大屏实际Id.
   * @return KvmVideoWall.
   */
  public KvmVideoWall oneByMasterIdAndDeviceId(String masterId, Integer deviceId) {
    LambdaQueryWrapper<KvmVideoWall> wrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getMasterId, masterId)
            .and(wrap -> wrap.eq(KvmVideoWall::getDeviceId, deviceId));
    return getOne(wrapper);
  }

  /**
   * 根据唯一搜索键值查询KvmVideoWall实体.
   *
   * @param uniqueSearchKey 唯一搜索键值，用于精确匹配查询KvmVideoWall记录.
   * @return 返回匹配的KvmVideoWall实体，如果存在多条匹配记录，则返回第一条.
   */
  public KvmVideoWall oneByUniqueSearchKey(String uniqueSearchKey) {
    LambdaQueryWrapper<KvmVideoWall> wrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getUniqueSearchKey, uniqueSearchKey);
    return getOne(wrapper);
  }

  /**
   * 通过主机Id及大屏实际Id查询.
   *
   * @param masterId 主机Id.
   * @param deviceId 大屏实际Id.
   * @return KvmVideoWall.
   */
  public Collection<KvmVideoWall> allByMasterIdAndDeviceId(String masterId, Integer deviceId) {
    LambdaQueryWrapper<KvmVideoWall> wrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getMasterId, masterId)
            .and(wrap -> wrap.eq(KvmVideoWall::getDeviceId, deviceId));
    return list(wrapper);
  }

  /**
   * 根据主机Id及大屏型号获取所有大屏.
   *
   * @param masterId 主机Id.
   * @param modelId  大屏型号.
   * @return .
   */
  public Collection<KvmVideoWall> allByMasterIdAndModelId(String masterId, Integer modelId) {
    LambdaQueryWrapper<KvmVideoWall> wrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getMasterId, masterId)
            .and(wrap -> wrap.eq(KvmVideoWall::getDeviceModel, modelId));
    return list(wrapper);
  }

  /**
   * 根据大屏Id批量删除与大屏相关的布局Id.
   *
   * @param wallId    大屏Id.
   * @param layoutIds 布局Id.
   * @return 删除成功返回true.
   */
  public boolean delBatchLayoutById(Integer wallId, Collection<Integer> layoutIds) {
    return baseMapper.delFromMerge(wallId, layoutIds);
  }

  /**
   * 批量保存指定大屏的布局.
   *
   * @param wallId    大屏Id.
   * @param layoutIds 布局Id.
   * @return .
   */
  public boolean saveBatchLayoutById(Integer wallId, Collection<Integer> layoutIds) {
    return baseMapper.insertToMerge(wallId, layoutIds);
  }

  /**
   * 获取指定大屏所选用的布局.
   *
   * @param wallId 大屏Id.
   * @return .
   */
  public Collection<VisualizationLayout> allLayoutsByWallId(Integer wallId) {
    return baseMapper.findLayoutByWallId(wallId);
  }

  /**
   * 更新或保存新的大屏.
   *
   * @param videoWall 实体对象.
   */
  public boolean saveOrUpdate(KvmVideoWall videoWall) {
    LambdaQueryWrapper<KvmVideoWall> lambdaQueryWrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getMasterId, videoWall.getMasterId())
            .and(wrap -> wrap.eq(KvmVideoWall::getDeviceId, videoWall.getDeviceId()));
    KvmVideoWall oldWall = getOne(lambdaQueryWrapper);
    if (oldWall == null) {
      return saveKvmVideoWall(videoWall, lambdaQueryWrapper);
    } else {
      return updateKvmVideoWall(videoWall, oldWall);
    }
  }

  public boolean saveKvmVideoWall(KvmVideoWall videoWall,
                                  LambdaQueryWrapper<KvmVideoWall> lambdaQueryWrapper) {
    save(videoWall);
    KvmVideoWall wall = getOne(lambdaQueryWrapper);
    Collection<VisualizationLayout> l = layoutService.list();
    Collection<Integer> layoutIds = l.stream().map(VisualizationLayout::getLayoutId).toList();
    return saveBatchLayoutById(wall.getWallId(), layoutIds);
  }

  public boolean updateKvmVideoWall(KvmVideoWall newWall, KvmVideoWall oldWall) {
    newWall.setRoomId(oldWall.getRoomId());
    newWall.setPollingScenesEnable(oldWall.isPollingScenesEnable());
    newWall.setDeviceModel(oldWall.getDeviceModel());
    newWall.setPollingIntervalTime(oldWall.getPollingIntervalTime());
    newWall.setCollectorProperties(oldWall.getCollectorProperties());
    newWall.setWallId(oldWall.getWallId());
    return updateById(newWall);
  }

  public void saveOrUpdateByUniqueSearchKey(KvmVideoWall newWall) {
    LambdaQueryWrapper<KvmVideoWall> lambdaQueryWrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getUniqueSearchKey, newWall.getUniqueSearchKey());
    KvmVideoWall oldWall = getOne(lambdaQueryWrapper);
    if (oldWall != null) {
      updateKvmVideoWall(newWall, oldWall);
    } else {
      saveKvmVideoWall(newWall, lambdaQueryWrapper);
    }
  }

  public void setPollingScenesValue(Integer videoWallId, boolean enable) {
    baseMapper.updatePollingStatusValue(videoWallId, enable);
  }

}
