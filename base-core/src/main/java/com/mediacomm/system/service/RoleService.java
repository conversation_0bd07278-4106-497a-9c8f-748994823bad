package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.Role;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.RoleMapper;
import java.util.Collection;
import java.util.HashSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class RoleService extends SkyLinkServiceImpl<RoleMapper, Role> {

  @Autowired
  PermissionService permissionService;

  public boolean save(Role role) {
    return baseMapper.insertToRole(role.getName());
  }

  /**
   * 根据accountId获取所有角色.
   *
   * @param accountId - 账户id.
   * @return Collection<Role/> - 所有与accountId相关联的角色集合.
   */
  public Collection<Role> allByAccountId(Integer accountId) {
    Collection<Role> roles = baseMapper.findRolesByAccountId(accountId);
    for (Role role : roles) {
      role.setPermissions(new HashSet<>(permissionService.allByRoleId(role.getRoleId())));
    }
    return roles;
  }

  /**
   * 根据accountId和roleIds批量删除角色.
   *
   * @param accountId 用户ID.
   * @param roleIds 角色ID集合.
   * @return 是否成功删除角色.
   */
  public boolean delBatchRoleByAccountId(Integer accountId, Collection<Integer> roleIds) {
    if (roleIds.isEmpty()) {
      return false;
    }
    return baseMapper.delFromMerge(accountId, roleIds);
  }

  /**
   * 根据accountId和roleIds批量保存角色.
   *
   * @param accountId 用户ID.
   * @param roleIds 角色ID集合.
   * @return 是否成功保存角色.
   */
  public boolean saveBatchRoleByAccountId(Integer accountId, Collection<Integer> roleIds) {
    if (roleIds.isEmpty()) {
      return false;
    }
    return baseMapper.insertToMerge(accountId, roleIds);
  }

  /**
   * 通过名称获取角色.
   *
   * @param roleName 角色名称.
   * @return .
   */
  public Role oneByName(String roleName) {
    LambdaQueryWrapper<Role> wrapper = Wrappers.lambdaQuery(Role.class)
        .eq(Role::getName, roleName);
    Role role = getOne(wrapper);
    role.setPermissions(new HashSet<>(permissionService.allByRoleId(role.getRoleId())));
    return role;
  }

}
