package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mediacomm.entity.dao.PeripheralUpgradeTask;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.PeripheralUpgradeTaskMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 外设升级任务服务.
 */
@Service
public class PeripheralUpgradeTaskService extends
  SkyLinkServiceImpl<PeripheralUpgradeTaskMapper, PeripheralUpgradeTask> {

  public PageResult<PeripheralUpgradeTask> allByPage(LambdaQueryWrapper<PeripheralUpgradeTask> wrapper,
                                                     Integer currentPage, Integer pageSize) {
    Page<PeripheralUpgradeTask> taskPage = new Page<>(currentPage, pageSize);
    IPage<PeripheralUpgradeTask> pageData = page(taskPage, wrapper);
    return new PageResult<>(pageData.getCurrent(), pageData.getSize(), pageData.getTotal(),
            pageData.getPages(), pageData.getRecords());
  }

  public PageResult<PeripheralUpgradeTask> allByPage(Integer packageId, String deviceId, DeviceType deviceType,
                                                     Integer currentPage, Integer pageSize) {
    LambdaQueryWrapper<PeripheralUpgradeTask> queryWrapper =
            Wrappers.lambdaQuery(PeripheralUpgradeTask.class).orderByDesc(PeripheralUpgradeTask::getStartTime);
    if (packageId != null) {
      queryWrapper.eq(PeripheralUpgradeTask::getPackageId, packageId);
    }
    if (deviceId != null) {
      queryWrapper.eq(PeripheralUpgradeTask::getPackageId, packageId);
    }
    if (deviceType != null) {
      queryWrapper.eq(PeripheralUpgradeTask::getDeviceType, deviceType);
    }
    return allByPage(queryWrapper, currentPage, pageSize);
  }

  public Collection<PeripheralUpgradeTask> allByDeviceIds(Collection<String> deviceIds) {
    return list(Wrappers.lambdaQuery(PeripheralUpgradeTask.class).in(PeripheralUpgradeTask::getDeviceId, deviceIds));
  }
}
