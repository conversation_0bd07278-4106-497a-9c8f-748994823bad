package com.mediacomm.system.service;

import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.SyncWallGroup;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.SyncWallGroupMapper;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class SyncWallGroupService extends SkyLinkServiceImpl<SyncWallGroupMapper, SyncWallGroup> {

  /**
   * 获取同屏组内的所有电视墙信息.
   *
   * @param groupId 同屏分组ID.
   * @return .
   */
  public Collection<KvmVideoWall> allVideoWallsBySyncGroupId(Integer groupId) {
    return getBaseMapper().findWallsById(groupId);
  }

  /**
   * 批量将多个大屏设置为同一个同屏分组.
   *
   * @param syncGroup 同屏分组ID.
   * @param wallIds 大屏IDs.
   */
  public void saveWallWithSyncGroup(Integer syncGroup, Collection<Integer> wallIds) {
    getBaseMapper().insertToMerge(syncGroup, wallIds);
  }

  /**
   * 从同屏组中将大屏信息删除.
   *
   * @param syncGroup 同屏分组ID.
   * @param wallId 大屏IDs.
   */
  public void removeWallFromSyncGroup(Integer syncGroup, Integer wallId) {
    getBaseMapper().delFromMerge(syncGroup, wallId);
  }

  /**
   * 读取所有同屏分组的信息.
   *
   * @return .
   */
  public Collection<SyncWallGroup> allSyncWallGroups() {
    Collection<SyncWallGroup> syncWallGroups = list();
    for (SyncWallGroup syncWallGroup : syncWallGroups) {
      syncWallGroup.setVideoWalls(allVideoWallsBySyncGroupId(syncWallGroup.getId()));
    }
    return syncWallGroups;
  }
}
