package com.mediacomm.system.service;

import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.PeripheralUpgradePackage;
import com.mediacomm.entity.dao.PeripheralUpgradeTask;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.PeripheralUpgradeIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.PeripheralUpgradeTaskBody;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.UpgradeTaskStatus;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import com.mediacomm.util.mq.RabbitSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 外设升级服务.
 */
@Slf4j
@Service
public class PeripheralUpgradeService {

  @Autowired
  private PeripheralUpgradePackageService packageService;

  @Autowired
  private PeripheralUpgradeTaskService taskService;

  @Autowired
  private KvmAssetService assetService;

  @Autowired
  private KvmMasterService masterService;

  @Autowired
  private RabbitSender sender;

  /**
   * 创建升级任务并发送升级指令.
   *
   * @param deviceIds 设备ID列表
   * @param packageId 升级包ID
   * @param downloadUrl 下载URL
   * @return 成功创建的任务列表
   */
  public boolean createUpgradeTasks(List<String> deviceIds, Integer packageId,
                                    String downloadUrl, String auth) {
    PeripheralUpgradePackage upgradePackage = packageService.getById(packageId);
    if (upgradePackage == null) {
      return Boolean.FALSE;
    }

    // 检查文件是否存在
    File packageFile = new File(upgradePackage.getFilePath());
    if (!packageFile.exists()) {
      return Boolean.FALSE;
    }

    // 为每个设备创建升级任务
    // key -> masterId
    Map<String, Collection<KvmAsset>> devices = new HashMap<>();
    // 防止创建多个任务
    Collection<PeripheralUpgradeTask> tasks = taskService.allByDeviceIds(deviceIds);
    tasks.removeIf(task -> task.getStatus() != 0 && task.getStatus() != 1);
    Collection<String> isUpgrading = tasks.stream().map(PeripheralUpgradeTask::getDeviceId).collect(Collectors.toSet());
    for (String deviceId : deviceIds) {
      if (isUpgrading.contains(deviceId)) {
        continue;
      }
      KvmAsset device = assetService.getById(deviceId);
      if (device == null || !Objects.equals(
              upgradePackage.getDeviceType().getDeviceTypeId(), device.getDeviceModel())) {
        continue;
      }
      if (upgradePackage.isLatestVersion(device.getVersion())) {
        log.info("The device {} {} is already upgraded to the latest version.", device.getName(), device.getDeviceIp());
        continue;
      }
      devices.computeIfAbsent(device.getMasterId(), k -> new ArrayList<>()).add(device);
    }
    devices.forEach((masterId, assets) -> {
      PeripheralUpgradeTaskBody requestBody = new PeripheralUpgradeTaskBody();
      requestBody.setPackageUrl(downloadUrl);
      requestBody.setAuth(auth);
      for (KvmAsset asset : assets) {
        PeripheralUpgradeTask task = new PeripheralUpgradeTask();
        task.setPackageId(packageId);
        task.setPackageVersion(upgradePackage.getVersion());
        task.setDeviceId(asset.getAssetId());
        task.setDeviceName(asset.getName());
        task.setDeviceType(upgradePackage.getDeviceType());
        task.setStatus(UpgradeTaskStatus.WAITING.getCode());
        task.setProgress(0);
        task.setStartTime(System.currentTimeMillis());
        taskService.save(task);
        requestBody.getAssetIdsAndTaskIds().put(asset.getAssetId(), task.getId());
      }
      sendRpc(masterId, RoutingOperation.START_UPGRADE_PACKAGE, requestBody);
    });

    return Boolean.TRUE;
  }

  /**
   * 取消升级任务.
   *
   * @param taskId 任务ID
   * @return 是否成功
   */
  public boolean cancelUpgradeTask(Integer taskId) {
    PeripheralUpgradeTask task = taskService.getById(taskId);
    if (Objects.equals(task.getStatus(), UpgradeTaskStatus.FAILED.getCode())) {
      return false;
    }
    KvmAsset device = assetService.getById(task.getDeviceId());
    if (device == null) {
      return false;
    }
    // 发送取消升级指令到设备
    PeripheralUpgradeIdRequestBody cancelRequest = PeripheralUpgradeIdRequestBody.builder()
        .deviceId(task.getDeviceId())
        .taskId(task.getId())
        .build();

    // 发送取消消息
    sendRpc(device.getMasterId(), RoutingOperation.STOP_UPGRADE_PACKAGE, cancelRequest);

    return true;
  }

  private <T> void sendRpc(String masterId, String route, T request) {
    KvmMasterVo master = masterService.oneById(masterId);
    MqRequest<T> msg = new MqRequest<>();
    msg.setMasterId(master.getMasterId());
    msg.setBody(request);
    sender.syncSend(master.getDeviceType().getRootRoute() + route, msg);
  }
}
