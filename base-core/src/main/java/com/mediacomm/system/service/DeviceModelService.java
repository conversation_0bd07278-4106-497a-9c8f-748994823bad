package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.DeviceModelMapper;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class DeviceModelService extends SkyLinkServiceImpl<DeviceModelMapper, DeviceModel> {

  public PageResult<DeviceModel> allByPage(Integer currentPage, Integer pageSize) {
    Page<DeviceModel> page = new Page<>(currentPage, pageSize);
    return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(),
        page.getPages(), page(page).getRecords());
  }

  public Collection<DeviceModel> allBySubSystem(SubSystemType type) {
    LambdaQueryWrapper<DeviceModel> wrapper = Wrappers.lambdaQuery(DeviceModel.class)
        .eq(DeviceModel::getSubSystem, type);
    return list(wrapper);
  }

  /**
   * 根据设备类型获取单个设备模型.
   *
   * @param type 设备类型.
   * @param modelKey 模型键.
   * @return 设备模型对象.
   */
  public DeviceModel oneByDeviceType(String type) {
    LambdaQueryWrapper<DeviceModel> wrapper = Wrappers.lambdaQuery(DeviceModel.class)
       .eq(DeviceModel::getDeviceType, type);
    return getOne(wrapper);
  }
}
