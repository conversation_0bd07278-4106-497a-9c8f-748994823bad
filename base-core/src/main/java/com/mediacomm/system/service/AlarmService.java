package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.AlarmMapper;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class AlarmService extends SkyLinkServiceImpl<AlarmMapper, Alarm> {

  /**
   * 根据给定的当前页码和每页大小，返回对应页码的数据结果.
   *
   * @param currentPage 当前页码.
   * @param pageSize 每页大小.
   * @return 包含当前页码、每页大小、总记录数、总页数和记录结果的PageResult对象.
   */
  public PageResult<Alarm> allByPageAndDate(Integer currentPage, Integer pageSize, long startDate,
                                     long endDate, String masterId,
                                     SubSystemType.AlarmLevel level) {
    LambdaQueryWrapper<Alarm> wrapper = Wrappers.lambdaQuery(Alarm.class)
        .between(Alarm::getBeginTime, startDate, endDate);
    if (masterId != null) {
      wrapper.and(w -> w.eq(Alarm::getMasterId, masterId));
    }
    if (level != null) {
      wrapper.and(w -> w.eq(Alarm::getAlarmLevel, level));
    }
    return allByPage(currentPage, pageSize, wrapper);
  }

  /**
   * 根据结束状态查询所有的报警信息.
   *
   * @param isEnd 结束状态，true表示已结束，false表示未结束.
   * @return 返回一个报警信息的集合.
   */
  public Collection<Alarm> allAlarmByEnd(Boolean isEnd) {
    LambdaQueryWrapper<Alarm> wrapper = Wrappers.lambdaQuery(Alarm.class)
        .eq(Alarm::isEnded, isEnd);
    return list(wrapper);
  }

  /**
   * 根据结束状态查询所有的报警信息.
   *
   * @param isEnd 结束状态，true表示已结束，false表示未结束.
   * @return 返回一个报警信息的集合.
   */
  public PageResult<Alarm> allByPageAndEnd(Integer currentPage, Integer pageSize, Boolean isEnd) {
    LambdaQueryWrapper<Alarm> wrapper = Wrappers.lambdaQuery(Alarm.class)
        .eq(Alarm::isEnded, isEnd).orderByDesc(Alarm::getBeginTime).orderByDesc(Alarm::getAlarmId);
    return allByPage(currentPage, pageSize, wrapper);
  }

  /**
   * 根据给定的当前页码和每页大小，返回对应页码的数据结果.
   *
   * @param currentPage 当前页码.
   * @param pageSize 每页大小.
   * @return 包含当前页码、每页大小、总记录数、总页数和记录结果的PageResult对象.
   */
  public PageResult<Alarm> allByPage(Integer currentPage, Integer pageSize,
                                     Wrapper<Alarm> wrapper) {
    Page<Alarm> page = new Page<>(currentPage, pageSize);
    IPage<Alarm> pageData = page(page, wrapper);
    return new PageResult<>(pageData.getCurrent(), pageData.getSize(), pageData.getTotal(),
        pageData.getPages(), pageData.getRecords());
  }

  public int getMaxId() {
    return baseMapper.getMaxAlarmId() != null ? baseMapper.getMaxAlarmId() : 0;
  }

  /**
   * 根据给定的主键、设备ID和信号ID，返回一个报警信息对象.
   *
   * @param masterId 主键.
   * @param deviceId 设备ID.
   * @param signalId 信号ID.
   * @return 返回一个报警信息对象.
   */
  public Alarm oneByMasterIdAndDeviceIdAndSignalIdAndNoEnd(String masterId, String deviceId, String signalId) {
    LambdaQueryWrapper<Alarm> wrapper = Wrappers.lambdaQuery(Alarm.class)
         .eq(Alarm::getMasterId, masterId)
         .and(w -> w.eq(Alarm::getDeviceId, deviceId))
         .and(w -> w.eq(Alarm::getSignalId, signalId))
         .and(w -> w.eq(Alarm::isEnded, false));
    return getOne(wrapper);
  }

  /**
   * 设置报警静音值.
   * 该方法用于更新报警信息中的静音时间及静音操作人员信息.
   *
   * @param muteTime 静音时间，单位通常为毫秒.
   * @param personnelName 操作人员姓名，即谁执行了静音操作.
   * @param alarmId 报警的唯一标识符.
   */
  public void setAlarmMuteValue(long muteTime, String personnelName, Integer alarmId) {
    LambdaUpdateWrapper<Alarm> wrapper = Wrappers.lambdaUpdate(Alarm.class)
        .set(Alarm::getMuteTime, muteTime)
        .set(Alarm::getMuteByPerson, personnelName)
        .eq(Alarm::getAlarmId, alarmId);
    update(wrapper);
  }

  /**
   * 设置报警确认值.
   * 该方法用于更新报警信息中的确认时间及确认操作人员信息.
   *
   * @param ackTime 确认时间，单位通常为毫秒.
   * @param personnelName 操作人员姓名，即谁执行了确认操作.
   * @param alarmId 报警的唯一标识符.
   */
  public void setAlarmAckValue(long ackTime, String personnelName, Integer alarmId) {
    LambdaUpdateWrapper<Alarm> wrapper = Wrappers.lambdaUpdate(Alarm.class)
       .set(Alarm::getAckTime, ackTime)
       .set(Alarm::getAckByPerson, personnelName)
       .set(Alarm::isAcked, true)
       .eq(Alarm::getAlarmId, alarmId);
    update(wrapper);
  }

  /**
   * 设置报警结束值.
   * 该方法用于更新报警信息中的结束时间、确认时间、确认操作人员信息、确认描述、静音时间、静音操作人员信息等字段.
   *
   * @param endTime 结束时间，单位通常为毫秒.
   * @param personnelName 操作人员姓名，即谁执行了结束操作.
   * @param alarmId 报警的唯一标识符.
   * @param ackDesc 确认描述，即结束操作时的描述信息.
   */
  public void setAlarmEndValue(long endTime, String personnelName, Integer alarmId,
                               String ackDesc) {
    LambdaUpdateWrapper<Alarm> wrapper = Wrappers.lambdaUpdate(Alarm.class)
        .set(Alarm::getEndTime, endTime)
        .set(Alarm::isEnded, true)
        .set(Alarm::getAckTime, endTime)
        .set(Alarm::getAckDesc, ackDesc)
        .set(Alarm::getAckByPerson, personnelName)
        .set(Alarm::isAcked, true)
        .set(Alarm::getMuteTime, endTime)
        .set(Alarm::getMuteByPerson, personnelName)
        .eq(Alarm::getAlarmId, alarmId);
    update(wrapper);
  }

  /**
   * 根据给定的主机ID、设备ID和信号ID，删除报警信息.
   *
   */
  public void delByMasterIdAndDeviceIdAndSignalId(Alarm alarm) {
    LambdaQueryWrapper<Alarm> wrapper = Wrappers.lambdaQuery(Alarm.class)
            .eq(Alarm::getMasterId, alarm.getMasterId())
            .and(w -> w.eq(Alarm::getDeviceId, alarm.getDeviceId()))
            .and(w -> w.eq(Alarm::getSignalId, alarm.getSignalId()));
    remove(wrapper);
  }
}
