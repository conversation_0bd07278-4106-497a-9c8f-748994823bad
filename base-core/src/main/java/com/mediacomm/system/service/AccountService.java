package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.Account;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.entity.vo.PersonnelVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.AccountMapper;
import com.mediacomm.util.JsonUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class AccountService extends SkyLinkServiceImpl<AccountMapper, Account> {

  @Autowired
  PersonnelService personnelService;
  @Autowired
  RoleService roleService;


  /**
   * 联表查询所有账号及关联人员.
   *
   * @return Collection.
   */
  public Collection<AccountVo> all() {
    Collection<Account> accounts = super.list();
    Collection<AccountVo> accountVos = new ArrayList<>();
    accounts.forEach(account -> {
      AccountVo vo = Optional.ofNullable(account).map(AccountVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addPersonnelInfo);
      accountVos.add(vo);
    });
    return accountVos;
  }

  /**
   * 联表查询单个账号及人员.
   *
   * @param accountId 账号Id.
   * @return AccountVo.
   */
  public AccountVo oneById(Integer accountId) {
    Account account = getById(accountId);
    AccountVo accountVo = Optional.ofNullable(account).map(AccountVo::new).orElse(null);
    Optional.ofNullable(accountVo).ifPresent(this::addPersonnelInfo);
    return accountVo;
  }

  /**
   * 通过账号名称联表查询账号信息.
   *
   * @param name 账号名称.
   * @return Account.
   */
  public AccountVo oneByName(String name) {
    LambdaQueryWrapper<Account> wrapper = Wrappers.lambdaQuery(Account.class)
        .eq(Account::getAccountName, name);
    Account account = getOne(wrapper);
    AccountVo accountVo = Optional.ofNullable(account).map(AccountVo::new).orElse(null);
    Optional.ofNullable(accountVo).ifPresent(this::addPersonnelInfo);
    return accountVo;
  }

  public void setAccountEnableValue(Integer accountId, boolean enable) {
    LambdaUpdateWrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
        .set(Account::isAccountEnable, enable)
       .eq(Account::getAccountId, accountId);
    this.update(wrapper);
  }

  public void setAccountBaseInfo(Account account) {
    LambdaUpdateWrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
        .set(Account::getAccountDesc, account.getAccountDesc())
        .set(Account::getAccountHomePage, account.getAccountHomePage())
        .set(Account::getBusinessAuth, JsonUtils.encode(account.getBusinessAuth()))
        .eq(Account::getAccountId, account.getAccountId());
    this.update(wrapper);
  }

  private void addPersonnelInfo(AccountVo vo) {
    PersonnelVo personnel = personnelService.oneById(vo.getPersonnel());
    Optional.ofNullable(personnel).ifPresent(p -> {
      vo.setPersonnelName(personnel.getPersonnelName());
      vo.setJobNumber(personnel.getJobNumber());
      vo.setRoles(roleService.allByAccountId(vo.getAccountId()));
      vo.setDepartmentName(personnel.getDepartmentName());
      vo.setDepartmentId(personnel.getDepartment());
      vo.setPersonnelEnable(personnel.isPersonnelEnable());
    });
  }

}
