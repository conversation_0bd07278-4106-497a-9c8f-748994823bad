package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Strings;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.vo.KvmSlotVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmSlotMapper;
import java.util.Collection;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class KvmSlotService extends SkyLinkServiceImpl<KvmSlotMapper, KvmSlot> {
  @Autowired
  KvmMasterService ks;
  @Autowired
  DeviceModelService dm;

  /**
   * 通过主机Id查找关联板卡信息.
   *
   * @param masterId 主机Id.
   * @return Collection.
   */
  public Collection<KvmSlotVo> allByMaster(String masterId) {
    return Strings.isNullOrEmpty(masterId)
            ? baseMapper.findAll() : baseMapper.findByMasterId(masterId);
  }

  /**
   * 通过设备ID查找板卡.
   *
   * @param deviceId 设备ID.
   * @return KvmAsset.
   */
  public KvmSlotVo oneByDeviceId(Integer deviceId, String masterId) {
    LambdaQueryWrapper<KvmSlot> wrapper =
            Wrappers.lambdaQuery(KvmSlot.class).eq(KvmSlot::getDeviceId, deviceId)
                    .and(item -> item.eq(KvmSlot::getMasterId, masterId));
    KvmSlotVo vo = Optional.ofNullable(getOne(wrapper)).map(KvmSlotVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addSlotInfo);
    return vo;
  }

  public void saveOrUpdateBatchByDeviceIdAndMasterId(Collection<KvmSlot> slots) {
    for (KvmSlot slot : slots) {
      LambdaQueryWrapper<KvmSlot> queryWrapper = Wrappers.lambdaQuery(KvmSlot.class)
              .eq(KvmSlot::getDeviceId, slot.getDeviceId())
              .and(wrapper -> wrapper.eq(KvmSlot::getMasterId, slot.getMasterId()));
      KvmSlot old = getOne(queryWrapper);
      if (old != null) {
        slot.setSlotId(old.getSlotId());
        updateById(slot);
      } else {
        save(slot);
      }
    }
  }

  private void addSlotInfo(KvmSlotVo vo) {
    KvmMaster master = ks.getById(vo.getMasterId());
    DeviceModel model = dm.getById(vo.getDeviceModel());
    Optional.ofNullable(master).ifPresent(m -> vo.setMasterName(master.getName()));
    Optional.ofNullable(model).ifPresent(m -> {
      vo.setModelName(model.getModelName());
      vo.setDeviceType(model.getDeviceType());
      vo.setSubSystemType(model.getSubSystem());
    });
  }
}
