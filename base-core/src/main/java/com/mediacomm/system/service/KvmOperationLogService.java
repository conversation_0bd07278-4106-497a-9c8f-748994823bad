package com.mediacomm.system.service;

import com.google.common.base.Strings;
import com.mediacomm.entity.dao.KvmOperationLog;
import com.mediacomm.entity.vo.KvmOperationLogVo;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.KvmOperationLogMapper;
import java.util.ArrayList;
import java.util.Collection;
import org.springframework.stereotype.Service;

/**
 * KvmOperationLogService.
 */
@Service
public class KvmOperationLogService
    extends SkyLinkServiceImpl<KvmOperationLogMapper, KvmOperationLog> {

  public int countBetweenStartAndEndTime(long startTime, long endTime,
                                         String masterId) {
    if (startTime > endTime) {
      return 0;
    }
    if (Strings.isNullOrEmpty(masterId)) {
      return baseMapper.countBetweenStartAndEndTime(startTime, endTime);
    }
    return baseMapper.countBetweenStartAndEndTimeByMasterId(startTime, endTime, masterId);
  }

  /**
   * 获取日志.
   */
  public Collection<KvmOperationLogVo> getBetweenStartAndEndTime(long startTime, long endTime,
                                                                 String masterId) {
    if (startTime > endTime) {
      return new ArrayList<>();
    }
    if (Strings.isNullOrEmpty(masterId)) {
      return baseMapper.allBetweenStartAndEndTime(startTime, endTime);
    }
    return baseMapper.findBetweenStartAndEndTimeByMasterId(startTime, endTime, masterId);
  }

  /**
   * 获取日志.
   */
  public Collection<KvmOperationLogVo> getBetweenStartAndEndTime(int offset, int pageSize,
                                                                 long startTime, long endTime,
                                                                 String masterId) {
    if (startTime > endTime) {
      return new ArrayList<>();
    }
    if (Strings.isNullOrEmpty(masterId)) {
      return baseMapper.pageBetweenStartAndEndTime(offset, pageSize, startTime, endTime);
    }
    return baseMapper.pageBetweenStartAndEndTimeByMasterId(offset, pageSize, startTime, endTime,
        masterId);
  }
}
