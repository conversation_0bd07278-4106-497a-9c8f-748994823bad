package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mediacomm.entity.dao.PositionGroup;
import com.mediacomm.entity.dao.VisualizationScene;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.VisualizationSceneMapper;
import com.mediacomm.system.variable.sysenum.VisualizationSceneType;
import java.util.Collection;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
public class VisualizationSceneService extends
    SkyLinkServiceImpl<VisualizationSceneMapper, VisualizationScene> {

  @Autowired
  PositionGroupService groupService;

  /**
   * 根据房间Id和设备型号Id查询所有可视化场景.
   *
   * @param roomId 房间Id.
   * @param deviceModel 设备型号Id.
   * @return Collection.
   */
  public Collection<VisualizationScene> allByRoomIdAndModel(Integer roomId, Integer deviceModel) {
    // 查询所有非引导页场景
    LambdaQueryWrapper<VisualizationScene> wrapper = Wrappers.lambdaQuery(VisualizationScene.class)
        .eq(VisualizationScene::getSceneType, VisualizationSceneType.OPERATION_PAGE);
    if (roomId == null && deviceModel == null) {
      return list(wrapper).stream().peek(scene -> {
        PositionGroup room = groupService.getById(scene.getRoomId());
        scene.setRoomName(room != null ? room.getName() : "");
      }).collect(Collectors.toSet());
    }
    // 查询所有房间内的场景
    if (roomId != null) {
      wrapper.and(w -> w.eq(VisualizationScene::getRoomId, roomId));
    }
    if (deviceModel!= null) {
      wrapper.and(w -> w.eq(VisualizationScene::getDeviceModel, deviceModel));
    }
    PositionGroup room = groupService.getById(roomId);
    return list(wrapper).stream().peek(scene ->
            scene.setRoomName(room != null ? room.getName() : ""))
        .collect(Collectors.toList());
  }

  /**
   * 获取指定设备类型的引导页.
   *
   * @param deviceModel 设备类型.
   * @return .
   */
  public Collection<VisualizationScene> allGuidePageByModel(Integer deviceModel) {
    LambdaQueryWrapper<VisualizationScene> wrapper = Wrappers.lambdaQuery(VisualizationScene.class)
        .eq(VisualizationScene::getDeviceModel, deviceModel)
        .eq(VisualizationScene::getSceneType, VisualizationSceneType.GUIDE_PAGE);
    return list(wrapper);
  }
}
