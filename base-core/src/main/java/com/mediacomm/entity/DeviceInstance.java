package com.mediacomm.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * 服务所关注的设备信息.
 *
 * @author: WuZeJie.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DeviceInstance {
  private String deviceId; // 主机Id
  private String deviceType; // 主机类型
  private List<String> function; // 主机提供的功能

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeviceInstance that = (DeviceInstance) o;
    return Objects.equals(deviceId, that.deviceId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deviceId);
  }
}
