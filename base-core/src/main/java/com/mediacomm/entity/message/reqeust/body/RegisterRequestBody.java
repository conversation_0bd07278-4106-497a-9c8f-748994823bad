package com.mediacomm.entity.message.reqeust.body;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mediacomm.entity.message.reqeust.SwitcherDevice;
import java.util.List;
import lombok.Data;

/**
 * RegisterBody.
 */
@Data
public class RegisterRequestBody {
  @JsonProperty("server_ip")
  private String serverIp;
  @JsonProperty("server_port")
  private Integer serverPort;
  private List<SwitcherDevice> devices;
}
