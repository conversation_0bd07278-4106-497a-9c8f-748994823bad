package com.mediacomm.entity.message.reqeust.body;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VwSceneRequestBody.
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VwSceneRequestBody {
  @Schema(hidden = true)
  private Integer id;
  private String scene;
}
