package com.mediacomm.entity.message;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LayoutRect.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LayoutRect implements Serializable {
  protected int seq;
  protected int xpos;
  protected int ypos;
  protected int width;
  protected int height;
}
