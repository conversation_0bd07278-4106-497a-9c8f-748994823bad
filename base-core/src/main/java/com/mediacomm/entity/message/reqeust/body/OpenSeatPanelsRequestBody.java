package com.mediacomm.entity.message.reqeust.body;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelRect;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * OpenSeatPanelsRequestBody.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenSeatPanelsRequestBody {
  private int decoderId;
  private int channelId;
  private List<PanelRect> panels = new ArrayList<>();
  private LayoutData layoutData;
}
