package com.mediacomm.entity.message.reqeust.body;

import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OpenVwPanelsRequestBody.
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OpenVwPanelsRequestBody {
  @Schema(hidden = true)
  private Integer id;
  private PanelData panelData;
  private LayoutData layoutData;
}
