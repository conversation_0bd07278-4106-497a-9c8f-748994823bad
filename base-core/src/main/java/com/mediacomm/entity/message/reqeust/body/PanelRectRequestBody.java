package com.mediacomm.entity.message.reqeust.body;

import com.mediacomm.entity.message.PanelRect;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PanelRectRequestBody.
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PanelRectRequestBody {
  @Schema(hidden = true)
  private Integer id;
  private PanelRect panelRect;
}
