package com.mediacomm.entity.message.reqeust;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * SwitcherDevice.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SwitcherDevice {
  private String name;
  private String sn;
  private String ip;
  private String mask;
  private String gateway;
  private String mac;
  @JsonProperty("device_type")
  private String deviceType;
  @JsonProperty("device_model")
  private String deviceModel;
  private String version;

  // 设备在云视上的id
  private String id;
  // 0为配置成功，1为配置失败，2为回复超时
  private int ret;
}
