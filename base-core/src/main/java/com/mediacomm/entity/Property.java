package com.mediacomm.entity;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 扩展属性.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Property implements Serializable {
  private String propertyKey;
  private String propertyValue;

  /**
   * 通过key从list中查找对应的Property.
   *
   * @param propertyKey key.
   * @param properties list.
   * @return Property.
   */
  public static Property findFromArray(String propertyKey, List<Property> properties) {
    for (Property p : properties) {
      if (propertyKey.equalsIgnoreCase(p.getPropertyKey())) {
        return p;
      }
    }
    return null;
  }

  /**
   * 查找指定数组中是否包含目标Property.
   *
   * @param property 目标Property.
   * @param properties 指定数组.
   * @return 目标Property.
   */
  public static Property findFromArray(Property property, List<Property> properties) {
    for (Property p : properties) {
      if (property.getPropertyKey().equalsIgnoreCase(p.getPropertyKey())) {
        return p;
      }
    }
    return null;
  }

  /**
   * 通过key从list中查找对应的value.
   *
   * @param key key.
   * @param defaultValue 找不到对应key的值时，返回指定的默认值.
   * @param properties list.
   * @return value.
   */
  public static String findValueByKey(List<Property> properties, String key,
                                      String defaultValue) {
    for (Property property : properties) {
      if (property.propertyKey.equalsIgnoreCase(key)) {
        if (property.propertyValue == null) {
          break;
        }
        return property.propertyValue;
      }
    }
    return defaultValue;
  }

  /**
   * 通过key从list中查找对应的数字value.
   *
   * @param key key.
   * @param defaultValue 找不到对应key的值时，返回指定的默认值.
   * @param properties list.
   * @param clazz 数字类型.
   * @return value.
   */
  public static <T extends Number> T findValueByKey(List<Property> properties, String key, T defaultValue, Class<T> clazz) {
    for (Property property : properties) {
      if (property.propertyKey.equalsIgnoreCase(key)) {
        if (property.propertyValue == null) {
          break;
        }
        if (clazz == Integer.class) {
          return (T) Integer.valueOf(property.propertyValue);
        } else if (clazz == Long.class) {
          return (T) Long.valueOf(property.propertyValue);
        } else if (clazz == Double.class) {
          return (T) Double.valueOf(property.propertyValue);
        } else if (clazz == Float.class) {
          return (T) Float.valueOf(property.propertyValue);
        } else if (clazz == Short.class) {
          return (T) Short.valueOf(property.propertyValue);
        } else if (clazz == Byte.class) {
          return (T) Byte.valueOf(property.propertyValue);
        } else {
          assert false : "Unsupported number type";
          break;
        }
      }
    }
    return defaultValue;
  }

  /**
   * 通过key从list中查找对应的枚举value.
   *
   * @param key key.
   * @param defaultValue 找不到对应key的值时，返回指定的默认值.
   * @param properties list.
   * @param clazz 枚举类型.
   * @return value.
   */
  public static <T extends Enum<T>> T findValueByKey(List<Property> properties, String key, T defaultValue, Class<T> clazz) {
    for (Property property : properties) {
      if (property.propertyKey.equalsIgnoreCase(key)) {
        if (property.propertyValue == null) {
          break;
        }
        return Enum.valueOf(clazz, property.propertyValue);
      }
    }
    return defaultValue;
  }
}
