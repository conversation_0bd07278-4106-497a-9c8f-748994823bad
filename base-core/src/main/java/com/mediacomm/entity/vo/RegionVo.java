package com.mediacomm.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 行政区划响应VO.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "RegionVo", description = "行政区划信息")
public class RegionVo {
  @Schema(description = "行政区划代码")
  private String code;
  
  @Schema(description = "行政区划名称")
  private String name;
}
