package com.mediacomm.entity.vo;

import com.mediacomm.entity.dao.EncoderAsso;
import lombok.Data;

/**
 * .
 */
@Data
public class EncoderAssoVo extends EncoderAsso {
  private String rxName;

  public EncoderAssoVo(EncoderAsso encoderAsso) {
    setIp(encoderAsso.getIp());
    setPort(encoderAsso.getPort());
    setId(encoderAsso.getId());
    setName(encoderAsso.getName());
    setMasterId(encoderAsso.getMasterId());
    setRtspUrl(encoderAsso.getRtspUrl());
    setRxId(encoderAsso.getRxId());
  }
}
