package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.system.variable.sysenum.DeviceType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class KvmMasterVo extends KvmMaster {
  private String groupName;
  private String modelName;
  private DeviceType deviceType;

  /**
   * KvmMaster to KvmMasterVo.
   *
   * @param master KvmMaster.
   */
  public KvmMasterVo(KvmMaster master) {
    setMasterId(master.getMasterId());
    setAlias(master.getAlias());
    setName(master.getName());
    setCollectorProperties(master.getCollectorProperties());
    setDeviceGroup(master.getDeviceGroup());
    setDeviceIp(master.getDeviceIp());
    setDeviceModel(master.getDeviceModel());
    setProperties(master.getProperties());
    setVersion(master.getVersion());
  }
}
