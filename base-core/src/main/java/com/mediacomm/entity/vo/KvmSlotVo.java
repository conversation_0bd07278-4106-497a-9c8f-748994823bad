package com.mediacomm.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class KvmSlotVo extends KvmSlot {
  private String masterName;
  private String modelName;
  private String deviceType;
  private SubSystemType subSystemType;

  public KvmSlotVo(KvmSlot kvmSlot) {
    setSlotId(kvmSlot.getSlotId());
    setDeviceId(kvmSlot.getDeviceId());
    setName(kvmSlot.getName());
    setCollectorProperties(kvmSlot.getCollectorProperties());
    setProperties(kvmSlot.getProperties());
    setDeviceModel(kvmSlot.getDeviceModel());
    setVersion(kvmSlot.getVersion());
    setMasterId(kvmSlot.getMasterId());
  }
}
