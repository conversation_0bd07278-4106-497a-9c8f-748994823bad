package com.mediacomm.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 国标设备分页响应VO.
 */
@Data
@Schema(name = "GbDevicePageVo", description = "国标设备分页响应")
public class GbDevicePageVo {
  @Schema(description = "当前页码")
  private int pageNum;
  
  @Schema(description = "每页数量")
  private int pageSize;
  
  @Schema(description = "总记录数")
  private long totalSize;
  
  @Schema(description = "总页数")
  private int totalPages;
  
  @Schema(description = "设备列表")
  private List<GbDeviceVo> list;
}
