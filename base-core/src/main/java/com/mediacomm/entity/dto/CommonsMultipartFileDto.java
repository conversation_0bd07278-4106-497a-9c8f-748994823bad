package com.mediacomm.entity.dto;

import jakarta.validation.constraints.NotNull;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import org.springframework.web.multipart.MultipartFile;

/**
 * .
 */
public class CommonsMultipartFileDto implements MultipartFile {

  private final File file;

  public CommonsMultipartFileDto(@NotNull File file) {
    this.file = file;
  }

  @Override
  public String getName() {
    return file.getName();
  }

  @Override
  public String getOriginalFilename() {
    return file.getAbsolutePath();
  }

  @Override
  public String getContentType() {
    return "image/" + getName().substring(getName().lastIndexOf('.') + 1);
  }

  @Override
  public boolean isEmpty() {
    return file.exists() && file.length() > 0;
  }

  @Override
  public long getSize() {
    return file.length();
  }

  @Override
  public byte[] getBytes() throws IOException {
    return Files.readAllBytes(file.toPath());
  }

  @Override
  public InputStream getInputStream() throws IOException {
    return new FileInputStream(file);
  }

  @Override
  public void transferTo(File dest) throws IOException, IllegalStateException {
    Files.copy(file.toPath(), dest.toPath());
  }
}
