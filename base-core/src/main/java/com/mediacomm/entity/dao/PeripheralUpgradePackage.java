package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.config.db.ListVersionJsonTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.DeviceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 外设升级包.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "peripheral_upgrade_package", description = "外设升级包")
public class PeripheralUpgradePackage extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String packageName; // 升级包名称
  private String fileName; // 文件名
  private String filePath; // 文件路径
  private String md5; // 文件MD5
  private DeviceType deviceType; // 设备类型
  @TableField(typeHandler = ListVersionJsonTypeHandler.class)
  private List<Version> version; // 版本号
  private String description; // 描述信息
  private Long fileSize; // 文件大小
  private Long uploadTime; // 上传时间

  /**
   * 判断当前升级包的版本是不是已经对应设备的版本.
   *
   * @param deviceVersion 设备版本
   * @return 设备是否已经是升级包的版本
   */
  public boolean isLatestVersion(List<Version> deviceVersion) {
    if (deviceVersion == null || deviceVersion.isEmpty() || version == null || version.isEmpty()) {
      return false;
    }
    return version.stream()
            .anyMatch(pv -> deviceVersion.stream()
                    .anyMatch(dv -> pv.getName().equals(dv.getName())
                            && pv.getVersion().equals(dv.getVersion())
                            && pv.getDate().equals(dv.getDate())));
  }
}
