package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 默认图.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true)
public class DefaultPicture extends SkyLinkDbEntity {
  @TableId(type = IdType.ASSIGN_ID)
  private String name;
  private Boolean stretch;
  private Long picHash;
  @TableField(exist = false)
  private byte[] content;
}
