package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.HashSet;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "role", description = "角色")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Role extends SkyLinkDbEntity {
  @TableId(value = "role_id")
  private Integer roleId;
  private String name;
  @TableField(exist = false)
  private Set<Permission> permissions = new HashSet<>();
}
