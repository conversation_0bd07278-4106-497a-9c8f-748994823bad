package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
public class Picture extends SkyLinkDbEntity {
  @TableId(value = "id")
  private Long id;
  private byte[] content;
}
