package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mediacomm.config.db.ListKvmVideoWallDecoderJsonTypeHandler;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_video_wall", description = "kvm电视墙")
@JsonIgnoreProperties(ignoreUnknown = true)
public class KvmVideoWall extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "wall_id", type = IdType.AUTO)
  private Integer wallId;
  private String masterId;
  private Integer deviceId;
  @TableField(updateStrategy = FieldStrategy.IGNORED) // 默认为NULL不更新，设置为忽略判断NULL
  private Integer roomId;
  private String name;
  private Integer seqInRoom = 1; // 房间内序号
  private Integer rowCount;
  private Integer colCount;
  private Integer singleW; // 分辨率宽
  private Integer singleH; // 分辨率高
  @TableField(typeHandler = ListKvmVideoWallDecoderJsonTypeHandler.class)
  private List<KvmVideoWallDecoder> decoders; // 屏幕信息
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean pollingScenesEnable; // 轮询预案
  private Integer pollingIntervalTime; // 轮询预案时间间隔
  private Integer deviceModel;
  private BannerType bannerType = BannerType.NON_SUPPORT; // 支持横幅的类型
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>();
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> collectorProperties = new ArrayList<>(); // 型号扩展属性
  private String uniqueSearchKey; // 电视墙无法通过deviceId、masterId唯一检索，通过电视墙的可唯一表示的组合数据进行唯一检索

  @TableField(exist = false)
  private String masterName;
  @TableField(exist = false)
  private String roomName;
  @TableField(exist = false)
  private DeviceType deviceType;
  @TableField(exist = false)
  private String modelName;
}
