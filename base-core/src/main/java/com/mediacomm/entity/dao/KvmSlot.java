package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmSlot extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "slot_id", type = IdType.ASSIGN_UUID)
  private String slotId; // 设备Id
  private Integer deviceId; // 设备的实际Id
  private String name; // 设备名称
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> collectorProperties = new ArrayList<>(); // 采集扩展属性
  private Integer deviceModel; // 设备类型Id
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>(); // 设备扩展属性
  private String version; // 设备版本
  private String masterId; // 设备的主机Id
}
