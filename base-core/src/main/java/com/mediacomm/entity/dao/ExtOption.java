package com.mediacomm.entity.dao;

import java.io.Serializable;
import java.util.Collection;
import lombok.Data;

/**
 * 设备型号的配置选项.
 */
@Data
public class ExtOption implements Serializable {
  private String name;
  private String unit;
  private Boolean required;
  private String paramType;
  private String displayName;
  private Collection<EnumOption> enumOptions;

  @Data
  public static class EnumOption implements Serializable {
    private String title;
    private String value;
  }
}
