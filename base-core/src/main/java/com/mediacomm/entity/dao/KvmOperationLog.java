package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.OperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * KvmOperationLog.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "kvm_operation_log", description = "kvm操作日志")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmOperationLog extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String masterId;
  private Long operationTime;
  private OperationType operationType;
  private String operationEvent;
  private String operationSrcAddress;
  private String operationSrcUser;
  private String operationSrcDevice;
  private String operationTargetDevice;
}
