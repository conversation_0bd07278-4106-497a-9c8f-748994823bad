package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true)
public class VideoWallBanner extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private Integer wallId;
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean status; // 启用状态，在云视上启用该配置时改变为true，否则为false
  @TableField(typeHandler = JacksonTypeHandler.class)
  private BannerContent content;

  @Data
  public static class BannerContent implements Serializable {
    private int id;
    private String bgColor;
    private boolean enable; // 设备上启用横幅时改变为true
    private boolean bgColorEnable; // 设备上启用横幅背景色时改变为true
    private Location location;
    private Collection<Subtitle> subtitleInfoArray = new ArrayList<>();
    private Collection<Logo> logoInfoArray = new ArrayList<>();
    private Collection<Clock> clockInfoArray = new ArrayList<>();
    private Collection<Weather> weatherInfoArray = new ArrayList<>();
    private Collection<DutySchedule> dutyScheduleInfoArray = new ArrayList<>();
  }

  @Data
  public static class Subtitle implements Serializable {
    private int id; // 字幕唯一ID，由前端提供
    private boolean enable;
    private String content;
    private Font font;
    private Rolling rolling;
    private Location location;
  }

  @Data
  public static class Logo implements Serializable {
    private int id; // logo唯一ID，由前端提供
    private boolean enable;
    private String logo; // 图片hash
    private Rolling rolling;
    private Location location;
  }

  @Data
  public static class Clock implements Serializable {
    private int id; // 时钟唯一ID，由前端提供
    private boolean enable;
    private String source; // ntp地址和端口
    private String timeZone; // 设置时区
    private boolean autoTimeZone; // 默认设备的时区
    private String clockStyle; // 时钟风格
    private String sortStyle; // 排序方式
    private String dateFormat;
    private Location location;
    private Font font;
  }

  @Data
  public static class Weather implements Serializable {
    private int id; // 天气唯一ID，由前端提供
    private boolean enable;
    private boolean autoSync; // 是否自动同步天气
    private String city;
    private String province;
    private String sortStyle; // 排序方式
    private String weatherStyle; // 天气格式，C表示城市，T表示温度，W表示风力，N表示空，如C-T-W，表示城市-温度-风力
    private Location location;
  }

  @Data
  public static class DutySchedule implements Serializable {
    private int id; // 值班表唯一ID，由前端提供
    private boolean enable;
    private int rows;
    private int columns;
    private Collection<Collection<String>> schedules;
    private Location location;
    private Font font;
  }

  @Data
  public static class Font implements Serializable {
    private String fontFamily; // 字体
    private String fontAlign; // 字体对齐方式
    private int fontSize; // 字体大小
    private String fontStyle; // normal italic
    private String fontWeight; // normal bold
    private String fontColor;
    private String fontBgColor; // 字体背景色
    private String fontEffect; // 文本效果
    private double letterSpacing;  // 字符间距
    private boolean fontBgColorEnable; // 字体背景色是否启用
  }

  @Data
  public static class Rolling implements Serializable {
    private int direction; // 滚动方向 0-none、1-lr、2-rl
    private int speed;
  }

  @Data
  public static class Location implements Serializable {
    private int x;
    private int y;
    private int width;
    private int height;
  }
}
