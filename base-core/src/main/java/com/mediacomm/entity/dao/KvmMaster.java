package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.annotation.IpValidation;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "kvm_master", autoResultMap = true)
@Data
@Schema(name = "kvm_master", description = "kvm主机")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KvmMaster extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "master_id", type = IdType.ASSIGN_UUID)
  private String masterId;
  private String alias;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String name;
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> collectorProperties; // 型号扩展属性
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private Integer deviceGroup;
  @IpValidation
  @NotNull(message = "Ip address must be not null.")
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String deviceIp;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private Integer deviceModel; // 设备类型Id
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties; // 设备扩展属性
  private String version;
}
