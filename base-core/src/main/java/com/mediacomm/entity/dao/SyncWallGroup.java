package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.Collection;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "sync_wall_group", description = "同屏分组")
public class SyncWallGroup extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String name;
  @TableField(exist = false)
  private Collection<KvmVideoWall> videoWalls = new ArrayList<>();
}
