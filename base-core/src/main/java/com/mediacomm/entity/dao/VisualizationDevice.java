package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mediacomm.config.db.ListPropertyJsonTypeHandler;
import com.mediacomm.entity.Property;
import com.mediacomm.system.annotation.IpValidation;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 可视化终端设备.
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class VisualizationDevice extends SkyLinkDbEntity {
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  private String name;
  @IpValidation
  private String deviceIp;
  private String hardcode;
  private Integer deviceModel;
  private Integer roomId;
  private Integer defaultScene;
  private String version;
  @TableField(typeHandler = ListPropertyJsonTypeHandler.class)
  private List<Property> properties = new ArrayList<>();
}
