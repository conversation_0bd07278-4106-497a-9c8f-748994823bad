package com.mediacomm.entity.dao;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SignalValue implements Serializable {
  private SignalType signalType;
  private Boolean bitValue;
  private Integer statusValue;
  private String descValue;
  private Double floatValue;
  private String valueDescr;

  public SignalValue(Boolean bitValue) {
    setValue(bitValue);
  }

  public SignalValue(Integer statusValue) {
    setValue(statusValue);
  }

  public SignalValue(Double floatValue) {
    setValue(floatValue);
  }

  public SignalValue(String descValue) {
    setValue(descValue);
  }

  public <T> void setValue(T value) {
    switch (value) {
      case Boolean b -> setValue(b);
      case Integer i -> setValue(i);
      case Double v -> setValue(v);
      case String string -> setValue(string);
      default ->
          throw new IllegalArgumentException("Unsupported type: " + value.getClass().getName());
    }
  }

  @JsonIgnore
  public void setValue(Boolean bitValue) {
    signalType = SignalType.BOOL;
    this.bitValue = bitValue;
  }

  @JsonIgnore
  public void setValue(Integer statusValue) {
    signalType = SignalType.STATUS;
    this.statusValue = statusValue;
  }

  @JsonIgnore
  public void setValue(Double floatValue) {
    signalType = SignalType.ANALOG;
    this.floatValue = floatValue;
  }

  @JsonIgnore
  public void setValue(String descValue) {
    signalType = SignalType.STRING;
    this.descValue = descValue;
  }
}
