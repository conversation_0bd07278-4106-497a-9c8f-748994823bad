package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.TinyIntToBooleanTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "personnel", description = "人员")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Personnel extends SkyLinkDbEntity {
  @Schema(hidden = true)
  @TableId(value = "personnel_id", type = IdType.AUTO)
  private Integer personnelId;
  private String jobNumber; // 工号
  private Long personnelCreateTime; // 人员创建时间
  private String personnelDesc; // 人员描述
  @TableField(typeHandler = TinyIntToBooleanTypeHandler.class, jdbcType = JdbcType.TINYINT)
  private boolean personnelEnable; // 是否启动
  private String personnelMail; // 邮箱
  private String personnelName; // 人员名称
  private String personnelTel; // 手机号
  private Integer department; // 所属部门Id
}
