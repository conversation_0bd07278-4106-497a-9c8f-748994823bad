package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 中国行政区划表.
 */
@EqualsAndHashCode(callSuper = true)
@TableName("china_administrative_divisions")
@Data
@Schema(name = "china_administrative_divisions", description = "中国行政区划")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChinaAdministrativeDivision extends SkyLinkDbEntity {
  @Schema(description = "行政区划代码 (GB/T 2260)")
  @TableId("code")
  private String code;
  
  @Schema(description = "名称")
  private String name;
  
  @Schema(description = "父级代码")
  private String parentCode;
  
  @Schema(description = "层级 (1:省/直辖市, 2:市, 3:区/县)")
  private Integer level;
}
