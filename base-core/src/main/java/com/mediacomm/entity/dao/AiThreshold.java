package com.mediacomm.entity.dao;

import com.mediacomm.system.variable.sysenum.SubSystemType.AlarmLevel;
import java.io.Serializable;
import lombok.Data;

/**
 * 监控信息.
 */
@Data
public class AiThreshold implements Serializable {
  private Integer thresholdSeq;
  private String thresholdName;
  private Double lowerThreshold;
  private Double upperThreshold;
  private AlarmLevel alarmLevel;
  private String thresholdDesc;
}
