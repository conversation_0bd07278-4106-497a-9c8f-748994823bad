package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.config.db.ListSignalTypeHandler;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Schema(name = "device_model_signal", description = "设备监控信号")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceModelSignal extends SkyLinkDbEntity {
  @TableId(value = "device_model_id")
  private Integer deviceModelId;
  @TableField(typeHandler = ListSignalTypeHandler.class)
  private List<Signal> signals;
}
