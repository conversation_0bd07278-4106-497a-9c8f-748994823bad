package com.mediacomm.entity.dao;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.system.base.entity.SkyLinkDbEntity;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DisabledAlarm extends SkyLinkDbEntity {
  @TableId(value = "id")
  private Integer id;
  private String deviceId;
  private String masterId; // deviceId的父级Id
  private String deviceName; // 告警设备名称
  private String signalId; // 告警信号ID
  private String signalName;
  private SubSystemType subSystem; // 所属系统级别
  private String disableDesc;
  private String operator;
  private Long operateTime;
}
