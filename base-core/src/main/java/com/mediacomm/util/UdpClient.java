package com.mediacomm.util;

import com.mediacomm.entity.message.UdpMsg;
import jakarta.validation.constraints.NotNull;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class UdpClient {
  private final Executor executor = Executors.newSingleThreadExecutor();

  public UdpClient() {
  }

  public void send(@NotNull UdpMsg msg) {
    try {
      if (msg != null && msg.getAddress() != null && msg.getPort() > 0) {
        executor.execute(() -> {
          try (DatagramSocket socket = new DatagramSocket()) {
            DatagramPacket sendPacket
                    = new DatagramPacket(msg.getData(), msg.getData().length,
                    InetAddress.getByName(msg.getAddress()), msg.getPort());
            socket.send(sendPacket);
          } catch (Exception e) {
            log.error(e.getMessage(), e);
          }
        });
      } else {
        log.error("Error udp message format {}!", msg);
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }
}
