package com.mediacomm.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.SerializerFactory;
import com.mediacomm.config.db.MyBeanSerializerModifier;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * Jackson序列化工具类.
 *
 * @author: WuZeJie
 */
@Slf4j
public class JsonUtils {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  static {
    // 反序列化：JSON字段中有Java对象中没有的字段时不报错
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    SerializerFactory serializerFactory = objectMapper.getSerializerFactory()
            .withSerializerModifier(new MyBeanSerializerModifier());
    objectMapper.setSerializerFactory(serializerFactory);

    // 序列化：Java对象为空的字段不拼接JSON
    //objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
  }

  /**
   * 对象序列化成json字符串.
   *
   * @param obj 对象.
   * @param <T> .
   * @return json.
   */
  public static <T> String encode(T obj) {
    if (obj == null) {
      return "";
    }
    if (obj.getClass() == String.class) {
      return (String) obj;
    }
    try {
      return objectMapper.writeValueAsString(obj);
    } catch (JsonProcessingException je) {
      recordLog(obj.toString(), je);
    }
    return "";
  }

  /**
   * .
   */
  public static <T> T decode(String json, Class<T> cls) {
    try {
      return objectMapper.readValue(json, cls);
    } catch (IOException ex) {
      recordLog(json, ex);
    }
    return null;
  }

  /**
   * .
   */
  public static <T> T decode(String json) {
    try {
      return objectMapper.readValue(json, new TypeReference<>() {
      });
    } catch (IOException ex) {
      recordLog(json, ex);
    }
    return null;
  }

  /**
   * .
   */
  public static <T> T decode(String json, TypeReference<T> typeReference) {
    if (json == null) {
      return null;
    }
    try {
      return objectMapper.readValue(json, typeReference);
    } catch (Exception ex) {
      recordLog(json, ex);
    }
    return null;
  }

  /**
   * .
   */
  public static <T> String encodeCollection(Collection<T> objs) {
    try {
      return objectMapper.writeValueAsString(objs);
    } catch (JsonProcessingException je) {
      recordLog(objs.toString(), je);
    }
    return "";
  }

  /**
   * .
   */
  public static <T> List<T> decodeList(String json, Class<T> cls) {
    if (json == null) {
      return Collections.emptyList();
    }
    try {
      JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, cls);
      return objectMapper.readValue(json, javaType);
    } catch (JsonProcessingException ex) {
      recordLog(json, ex);
    }
    return Collections.emptyList();
  }

  /**
   * .
   */
  public static <K, T> String encodeMap(Map<K, T> map) {
    try {
      return objectMapper.writeValueAsString(map);
    } catch (JsonProcessingException je) {
      recordLog(map.toString(), je);
    }
    return "";
  }

  /**
   * .
   */
  public static <K, T> Map<K, T> decodeMap(String json, Class<K> ktCls, Class<T> ttCls) {
    try {
      JavaType javaType =
          objectMapper.getTypeFactory().constructParametricType(HashMap.class, ktCls, ttCls);
      return objectMapper.readValue(json, javaType);
    } catch (JsonProcessingException je) {
      recordLog(json, je);
    }
    return null;
  }

  /**
   * 对象转Map.
   *
   * @param obj 对象.
   * @param <T> .
   */
  public static <T> Map<String, String> objToMap(T obj) {
    if (obj == null) {
      return null;
    }
    return objectMapper.convertValue(obj, new TypeReference<>() {
    });
  }

  /**
   * 记录JSON序列化错误日志
   * @param src 需要序列化的原始数据
   * @param e 导致错误的异常实例
   */
  private static void recordLog(String src, Exception e) {
    log.error("JSON serialization error: {}", src, e);
  }
}
