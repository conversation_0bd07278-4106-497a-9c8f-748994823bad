package com.mediacomm.util;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * .
 */
public class KeyValueToJsonConverter {

  /**
   * 将 key-value 键值对格式的配置文件内容转换为 Map.
   *
   * @param fileContent 配置文件内容.
   * @return map.
   */
  public static Map<String, String> parseToMap(String fileContent) {
    if (fileContent.isBlank()) {
      return Map.of();
    }
    return fileContent.lines()
            .map(String::trim)
            .filter(line -> line.contains("="))
            .map(line -> line.split("=", 2))
            .collect(Collectors.toMap(
                    parts -> parts[0], // 数组第一个元素作为 key
                    parts -> parts[1]  // 数组第二个元素作为 value
            ));
  }

  /**
   * 将 key-value 键值对格式的配置文件内容转换为 JSON 对象.
   *
   * @param fileContent 配置文件内容.
   * @return JSON 对象.
   */
  public static String parseToJsonObject(String fileContent) {
    Map<String, String> map = parseToMap(fileContent);
    return JsonUtils.encode(map);
  }
}
