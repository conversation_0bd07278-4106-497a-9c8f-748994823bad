package com.mediacomm.config.db;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.mediacomm.system.annotation.JacksonFill;
import com.mediacomm.system.variable.sysenum.FillType;
import java.io.IOException;
import java.util.List;

/**
 * 主要作用是处理带有@JacksonFill注解的字段在序列化为null时的特殊格式转换.
 * 字段为null且有注解@FillType,输出{}.
 * 后续可根据FillType扩展更多的格式转换.
 */
public class MyBeanSerializerModifier extends BeanSerializerModifier {

  // 通过修改Bean属性的序列化器实现行为定制
  @Override
  public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc,
                                                   List<BeanPropertyWriter> beanProperties) {
    for (BeanPropertyWriter writer : beanProperties) {
      JacksonFill jacksonFill = writer.getAnnotation(JacksonFill.class);
      if (jacksonFill != null) {
        writer.assignNullSerializer(new NullObjectJsonSerializer(jacksonFill.value()));
      }
    }
    return beanProperties;
  }

  // 自定义null值序列化器
  static class NullObjectJsonSerializer extends JsonSerializer<Object> {
    private final FillType fillType;

    public NullObjectJsonSerializer(FillType fillType) {
      this.fillType = fillType;
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
      if (FillType.BRACE == fillType && value == null) {
        gen.writeStartObject();
        gen.writeEndObject();
      }
    }
  }
}


