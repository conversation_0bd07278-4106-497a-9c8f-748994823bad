package com.mediacomm.config.db;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.util.JsonUtils;
import java.util.List;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;


/**
 * .
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ListKvmVideoWallDecoderJsonTypeHandler
        extends AbstractJsonTypeHandler<List<KvmVideoWallDecoder>> {

  @Override
  protected List<KvmVideoWallDecoder> parse(String json) {
    return JsonUtils.decode(json, new TypeReference<>() {});
  }

  @Override
  protected String toJson(List<KvmVideoWallDecoder> obj) {
    return JsonUtils.encode(obj);
  }
}
