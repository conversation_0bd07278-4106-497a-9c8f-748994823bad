<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
  <contextName>skylink</contextName>

  <property name="log.path" value="./${LOG_PATH:-.}/logs/server" />
  <property name="log.pattern" value="%d{HH:mm:ss.SSS} %contextName [%thread][${PID:-}] %-5level %logger{36} - %msg%n" />
  <!-- off > fatal > error > warn > info > debug > trace > all -->
  <!--输出到控制台-->
  <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <pattern>[%level] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %X{logId} %c.java - %msg%n</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>

  <!--输出到文件-->
  <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 最大保存天数 -->
      <maxHistory>30</maxHistory>
      <!-- 日志总保存量为1GB -->
      <totalSizeCap>1GB</totalSizeCap>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <!-- 文件达到最大64MB时会被压缩和切割-->
        <maxFileSize>64 MB</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>INFO</level>
    </filter>
    <encoder>
      <pattern>[%level] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %X{logId} %c.java - %msg%n</pattern>
    </encoder>
  </appender>
  <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 最大保存天数 -->
      <maxHistory>30</maxHistory>
      <!-- 日志总保存量为1GB -->
      <totalSizeCap>1GB</totalSizeCap>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <!-- 文件达到最大64MB时会被压缩和切割-->
        <maxFileSize>64 MB</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>ERROR</level>
    </filter>
    <encoder>
      <pattern>[%level] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %X{logId} %c.java - %msg%n</pattern>
    </encoder>
  </appender>
  <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.path}/warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 最大保存天数 -->
      <maxHistory>30</maxHistory>
      <!-- 日志总保存量为1GB -->
      <totalSizeCap>1GB</totalSizeCap>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <!-- 文件达到最大64MB时会被压缩和切割-->
        <maxFileSize>64 MB</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>WARN</level>
    </filter>
    <encoder>
      <pattern>[%level] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %X{logId} %c.java - %msg%n</pattern>
    </encoder>
  </appender>

  <root level="info">
    <appender-ref ref="console" />
    <appender-ref ref="info" />
    <appender-ref ref="error" />
    <appender-ref ref="warn" />
  </root>

  <!-- 减少启动日志，在获取注册中心的配置前的部分日志 -->
  <!--<logger name="com.alibaba.nacos" level="WARN"/>
  <logger name="com.alibaba.cloud.nacos" level="WARN"/>
  <logger name="com.mediacomm.system.mapper" level="DEBUG"/>-->
</configuration>
