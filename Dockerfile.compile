FROM maven:3.9.5-eclipse-temurin-21
WORKDIR /backend-ccms-platform
RUN set -ex; \
    sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list; \
    apt-get update; \
    apt-get install --no-install-recommends -y curl zip unzip; \
    apt-get clean; \
    rm -rf /var/lib/apt/lists/*;
COPY base-core /backend-ccms-platform/base-core
COPY kvm-collectors /backend-ccms-platform/kvm-collectors
COPY system-api /backend-ccms-platform/system-api
COPY system-monitor /backend-ccms-platform/system-monitor
COPY system-protocol /backend-ccms-platform/system-protocol
COPY docker /backend-ccms-platform/docker
COPY dist /dist
COPY spotbugs-exclude.xml /backend-ccms-platform/spotbugs-exclude.xml
COPY pom.xml /backend-ccms-platform
COPY docker/conf/settings.xml /usr/share/maven/conf/
