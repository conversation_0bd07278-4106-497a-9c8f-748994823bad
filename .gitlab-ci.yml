stages:
  - test
  - pre_build
  - build

variables:
  PLATFORMS: "linux/amd64,linux/arm64"
  MAVEN_OPTS: >-
    -Dhttps.protocols=TLSv1.2
    -Dmaven.repo.local=.m2/repository
    -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
  Maven_CLI_OPTS: >-
    --batch-mode
    --show-version
    -DinstallAtEnd=true
    -DdeployAtEnd=true  

image: *************/maven:3.9.5-eclipse-temurin-21

cache:
  paths:
    - .m2/repository

test:
  stage: test
  script:
    - cp docker/conf/settings.xml /usr/share/maven/conf/
    - echo "==================== Begin Test ========================"
    - chmod a+x ./gitlab-ci-test.sh
    - ./gitlab-ci-test.sh
  coverage: '/([0-9.]*) % covered/'
  artifacts:
    paths:
      - ./system-api/target/site/*
    expire_in: 1 week
    reports:
      junit: [ ./**/target/surefire-reports/TEST-*.xml ]
  rules:
    - if: $CI_PIPELINE_SOURCE  == "push"

download_artifacts:
  stage: pre_build
  image: ${DOCKER_IMAGE_SERVER}/skylink-ci/ubuntu:focal-20241011
  tags:
    - shared
  script:
    - git tag -l --format='%(contents)' $CI_COMMIT_TAG > msg_${CI_COMMIT_TAG}.txt
    - cat msg_${CI_COMMIT_TAG}.txt
    - ./download.sh $CI_API_V4_URL $PRIVATE_TOKEN $(pwd)/msg_${CI_COMMIT_TAG}.txt
    - rm msg_${CI_COMMIT_TAG}.txt
  artifacts:
    when: on_success
    paths:
      - dist/
  rules:
    - if: $CI_COMMIT_TAG

build_docker:
  stage: build
  dependencies:
    - download_artifacts
  image: *************/docker:24-dind
  services:
    - *************/docker:24-dind
  tags:
    - shared
  before_script:
    - |
      if ! docker buildx inspect | grep -q "linux/amd64"; then
        echo "ERROR: amd64 platform not supported"
        exit 1
      fi
      if ! docker buildx inspect | grep -q "linux/arm64"; then
        echo "ERROR: arm64 platform not supported"
        exit 1
      fi
    - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD $DOCKER_IMAGE_SERVER
    - docker buildx version
    - docker context create builder-context || true
    # docs: https://docs.docker.com/build/buildkit/configure/
    - docker buildx create --help
    - docker buildx create --name multiarchbuilder --driver docker-container --buildkitd-config docker/conf/buildkitd.toml --bootstrap --use builder-context
    - docker buildx inspect multiarchbuilder
  script:
    - set -eo pipefail
    - echo "Building and pushing multi-arch image ${DOCKER_IMAGE_SERVER}/mediacomm/skylink-server:${CI_COMMIT_TAG} for platforms ${PLATFORMS}..."
    - docker buildx build --platform "${PLATFORMS}" --build-arg VERSION=${CI_COMMIT_TAG} --tag "${DOCKER_IMAGE_SERVER}/mediacomm/skylink-server:${CI_COMMIT_TAG}" --push .
  rules:
    - if: $CI_COMMIT_TAG
