package com.mediacomm.snmp;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.Snmp;
import org.snmp4j.TransportMapping;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.transport.DefaultUdpTransportMapping;

@Slf4j
public abstract class SnmpRunnable implements Runnable {
  protected List<VariableBinding> variables = new ArrayList<>();
  private SnmpSyncCallback cbk;

  public SnmpRunnable(SnmpSyncCallback cbk) {
    this.cbk = cbk;
  }

  public List<VariableBinding> getVariables() {
    return variables;
  }

  protected abstract void doRequest(Snmp protocal) throws IOException;

  @Override
  public void run() {
    TransportMapping<?> transport = null;
    Snmp protocal = null;
    try {
      transport = new DefaultUdpTransportMapping();
      transport.listen();
      protocal = new Snmp(transport);
      doRequest(protocal);
      cbk.call();
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      cbk.call();
    } finally {
      try {
        if (protocal != null) {
          protocal.close();
        }
        if (transport!= null) {
          transport.close();
        }
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
  }
}
