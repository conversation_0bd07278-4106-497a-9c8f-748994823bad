#!/bin/bash

export LANG=en_US.UTF-8
PRG=$(readlink -f $0)
PRG_DIR=`dirname "$PRG"`

package=$PRG_DIR/..
version="$1"

rm -rf $package/target/$version
mkdir $package/$version
mkdir $package/target/$version

mvn versions:set -DnewVersion=$version
mvn versions:commit
mvn install:install-file -Dfile=./base-core/libs/spring-data-redis-time-series-1.2.0.jar -DgroupId=org.springframework.data -DartifactId=spring-data-redis-time-series -Dversion=1.2.0 -Dpackaging=jar; \
mvn install:install-file -Dfile=./kvm-collectors/skylink-hikvision/libs/artemis-http-client-1.1.7.jar -DgroupId=com.hikvision.ga -DartifactId=artemis-http-client -Dversion=1.1.7 -Dpackaging=jar; \
mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase -DartifactId=kingbase8 -Dversion=8.6.0 -Dpackaging=jar; \
mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase8 -DartifactId=pgjdbc-core-parent -Dversion=1.1.6 -Dpackaging=jar; \
mvn clean package -Dmaven.test.skip=true
if [ $? -ne "0" ];then
    exit 1 ;
fi
cp system-api/target/system-api-$version.jar $package/$version/skylink/skylink-server.jar
