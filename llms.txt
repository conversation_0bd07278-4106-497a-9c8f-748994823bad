# gbGateway

## API Docs
- devices [获取摄像头设备列表](https://4x6bdop376.apifox.cn/337571979e0.md): 根据平台ID获取该平台下的所有真实摄像头设备信息（通过递归目录查询，过滤掉行政区划、业务分组等虚拟节点），支持分页查询
- stream [请求视频流](https://4x6bdop376.apifox.cn/339485461e0.md): 向指定设备请求视频流，返回SSRC和会话ID
- stream [停止视频流](https://4x6bdop376.apifox.cn/339820935e0.md): 停止指定会话的视频流
- control [云台控制](https://4x6bdop376.apifox.cn/339485459e0.md): 控制指定设备的云台运动，支持上下左右移动、缩放和停止
- health [健康检查](https://4x6bdop376.apifox.cn/339485460e0.md): 检查服务器运行状态

## Schemas
- Schemas [models.APIResponse](https://4x6bdop376.apifox.cn/195096653d0.md):
- Schemas [models.StopRequest](https://4x6bdop376.apifox.cn/195320195d0.md):
- Schemas [models.Device](https://4x6bdop376.apifox.cn/195096654d0.md):
- Schemas [models.HealthResponse](https://4x6bdop376.apifox.cn/195096655d0.md):
- Schemas [models.DeviceType](https://4x6bdop376.apifox.cn/195341176d0.md):
- Schemas [models.DeviceListResponse](https://4x6bdop376.apifox.cn/195836947d0.md):
- Schemas [models.PTZRequest](https://4x6bdop376.apifox.cn/195096656d0.md):
- Schemas [models.StreamRequest](https://4x6bdop376.apifox.cn/195096657d0.md):
- Schemas [models.StreamResponse](https://4x6bdop376.apifox.cn/195096658d0.md):
- Schemas [models.PtzCmd](https://4x6bdop376.apifox.cn/195341177d0.md):
- Schemas [models.PaginationResponse](https://4x6bdop376.apifox.cn/195836948d0.md):