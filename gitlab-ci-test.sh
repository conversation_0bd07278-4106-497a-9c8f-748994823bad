#!/bin/bash

function coverage() {
    if [ -f ./system-api/target/site/jacoco-aggregate/index.html ]; then
      awk -F"," '{ instructions += $4 + $5; covered += $5 } END { print 100*covered/instructions, "% covered" }' ./system-api/target/site/jacoco-aggregate/jacoco.csv
    else
      echo "no coverage report"
      exit 1
    fi
}

mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase -DartifactId=kingbase8 -Dversion=8.6.0 -Dpackaging=jar
mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase8 -DartifactId=pgjdbc-core-parent -Dversion=1.1.6 -Dpackaging=jar
mvn install:install-file -Dfile=./base-core/libs/spring-data-redis-time-series-1.2.0.jar -DgroupId=org.springframework.data -DartifactId=spring-data-redis-time-series -Dversion=1.2.0 -Dpackaging=jar
mvn install:install-file -Dfile=./kvm-collectors/skylink-hikvision/libs/artemis-http-client-1.1.7.jar -DgroupId=com.hikvision.ga -DartifactId=artemis-http-client -Dversion=1.1.7 -Dpackaging=jar
mvn verify -Dspring.profiles.active=dev
RET=$?
if [ $RET -ne 0 ]; then
  echo "test failed"
  coverage
  exit $RET
else
  echo "build success"
  coverage
fi
