package com.mediacomm.util;

/**
 * 云台协议.

 * <AUTHOR>
 */
public class InspectValue {
  // 云台协议
  public static final String PELCO_D = "PELCO-D";
  public static final String PELCO_P = "PELCO-P";
  public static final String VISCA = "VISCA";
  public static final String PELCO_HIK = "PELCO-HIK";

  /**
   * 判断是否已对该协议处理方式进行定义.

   * @param protocol 控制对象的协议.
   * @return 已对接该协议则返回true.
   */
  public static boolean isDefinedProtocol(String protocol) {
    return protocol.equals(PELCO_D) || protocol.equals(PELCO_P)
        || protocol.equals(VISCA) || protocol.equals(PELCO_HIK);
  }
}
