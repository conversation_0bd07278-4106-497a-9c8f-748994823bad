package com.mediacomm.util;

import com.mediacomm.domain.Ptz;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;

/**
 * TransferFormat.

 * <AUTHOR>
 */
public class TransferFormat {
  private static final String NONE = "NONE";

  /**
   * 将KvmAsset转换为Ptz对象.
   */
  public static Ptz assetToPtz(KvmAsset asset) {
    Ptz ptz = new Ptz();
    ptz.setProtocol(Property.findValueByKey(asset.getCollectorProperties(), "protocol", NONE));
    if (ptz.getProtocol().equals(NONE)) {
      return null;
    }
    if (InspectValue.PELCO_HIK.equals(ptz.getProtocol())) {
      ptz.setAppKey(Property.findValueByKey(asset.getCollectorProperties(), "appKey", ""));
      ptz.setAppSecret(Property.findValueByKey(asset.getCollectorProperties(), "appSecret", ""));
      if (ptz.getAppKey().isEmpty() || ptz.getAppSecret().isEmpty()) {
        return null;
      }
    }
    ptz.setIp(Property.findValueByKey(asset.getCollectorProperties(), "ptzIp", asset.getDeviceIp()));
    ptz.setPort(Integer.parseInt(Property.findValueByKey(
            asset.getCollectorProperties(), "ptzPort", "80")));
    ptz.setAddress(Integer.parseInt(Property.findValueByKey(
            asset.getCollectorProperties(), "ptzAddress", "1")));
    ptz.setId(asset.getAssetId());
    ptz.setCameraIndexCode(asset.getHardcode());
    return ptz;
  }
}
