package com.mediacomm.domain;

import lombok.Data;

/**
 * .
 */
@Data
public class PtzZoomInfo {

  /**
   * 开始放大的X坐标，范围：0-255。由于设备比例限制，以及实际场景屏幕比例大小不同，请按照如下坐标位计算方式计算入参：屏幕X坐标/屏幕宽 *
   * 255，即该坐标位X坐标占总屏幕宽的比例*255。监控点会对startX、startY、endX 、endY四点围成的区域进行放大.
   */
  private Integer startX;

  /**
   * 开始放大的Y坐标，范围：0-255，由于设备比例限制，以及实际场景屏幕比例大小不同，请按照如下坐标位计算方式计算入参：屏幕Y坐标/屏幕高 *
   * 255，即该坐标位Y坐标占总屏幕高的比例*255。监控点会对startX、startY、endX 、endY四点围成的区域进行放大.
   */
  private Integer startY;

  /**
   * 结束放大的X坐标，范围以及计算方式同startX.
   */
  private Integer endX;

  /**
   * 结束放大的Y坐标，范围以及计算方式同startY.
   */
  private Integer endY;
}
