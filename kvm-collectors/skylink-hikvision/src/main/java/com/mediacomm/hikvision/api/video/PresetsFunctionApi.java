package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.presets.AdditionRequest;
import com.mediacomm.hikvision.entity.video.presets.DeletionRequest;
import com.mediacomm.hikvision.entity.video.presets.GetRequest;
import com.mediacomm.hikvision.entity.video.presets.SearchesRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;

/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class PresetsFunctionApi {

  /**
   * 设置预置点信息<br/>
   * 该接口用于设置监控点的预置点信息，若参数传已经存在的预置点编号，则可修改预置点信息.
   *
   * @param additionRequest 请求Body
   * @return 返回码
   * @throws Exception Exception
   */
  public static String addition(ArtemisConfig config, AdditionRequest additionRequest)
      throws Exception {
    String additionDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/presets/addition";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, additionDataApi);
    String body = JsonUtils.encode(additionRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 查询预置点信息<br/>
   * 该接口用于查询监控点的预置点信息.
   *
   * @param searchesRequest 请求Body
   * @return 预置点
   * @throws Exception Exception
   */
  public static String searches(ArtemisConfig config, SearchesRequest searchesRequest)
      throws Exception {
    String searchesDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/presets/searches";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchesDataApi);
    String body = JsonUtils.encode(searchesRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 删除预置点信息.
   *
   * @param deletionRequest 请求Body
   * @return 返回码
   * @throws Exception Exception
   */
  public static String deletion(ArtemisConfig config, DeletionRequest deletionRequest)
      throws Exception {
    String deletionDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/presets/deletion";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, deletionDataApi);
    String body = JsonUtils.encode(deletionRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 批量获取监控点的预置点信息.
   *
   * @param getRequest 请求Body
   * @return 返回码
   * @throws Exception Exception
   */
  public static String get(ArtemisConfig config, GetRequest getRequest) throws Exception {
    String getDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/presets/get";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, getDataApi);
    String body = JsonUtils.encode(getRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
