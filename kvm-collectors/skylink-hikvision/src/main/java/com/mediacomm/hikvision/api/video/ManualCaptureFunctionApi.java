package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.manualcapture.ManualCaptureRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class ManualCaptureFunctionApi {


  /**
   * 手动抓图<br/>
   * 该接口用于手动触发设备抓图，返回图片的地址，<big>抓图前请确保平台上已配置图片存储信息。</big>
   * 抓图时间为触发手动抓图命令的时间.
   *
   * @param manualCaptureRequest 请求Body
   * @return 返回图片的地址
   * @throws Exception Exception
   */
  public static String manualCapture(ArtemisConfig config,
                                     ManualCaptureRequest manualCaptureRequest) throws Exception {
    String manualCaptureDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/manualCapture";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, manualCaptureDataApi);
    String body = JsonUtils.encode(manualCaptureRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }


}
