package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.manualrecord.SearchRequest;
import com.mediacomm.hikvision.entity.video.manualrecord.StartRequest;
import com.mediacomm.hikvision.entity.video.manualrecord.StatusRequest;
import com.mediacomm.hikvision.entity.video.manualrecord.StopRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class ManualRecordFunctionApi {


  /**
   * 开始手动录像.
   *
   * @param config       ArtemisConfig
   * @param startRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String start(ArtemisConfig config, StartRequest startRequest) throws Exception {
    String startDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/manualRecord/start";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, startDataApi);
    String body = JsonUtils.encode(startRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 停止手动录像.
   *
   * @param config      ArtemisConfig
   * @param stopRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String stop(ArtemisConfig config, StopRequest stopRequest) throws Exception {
    String stopDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/manualRecord/stop";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, stopDataApi);
    String body = JsonUtils.encode(stopRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 获取手动录像状态.
   *
   * @param config        ArtemisConfig
   * @param statusRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String status(ArtemisConfig config, StatusRequest statusRequest) throws Exception {
    String statusDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/manualRecord/status";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, statusDataApi);
    String body = JsonUtils.encode(statusRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  //

  /**
   * 查询手动录像编号.
   *
   * @param config        ArtemisConfig
   * @param searchRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String search(ArtemisConfig config, SearchRequest searchRequest) throws Exception {
    String searchDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/manualRecord/taskId/search";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchDataApi);
    String body = JsonUtils.encode(searchRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }


}
