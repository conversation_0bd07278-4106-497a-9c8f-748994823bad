package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.pictureinfos.PictureInfosRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class PictureInfosFunctionApi {

  /**
   * 视频图片查询<br/>
   * 支持根据输入的监控点编号集合，分页获取通过平台配置抓图计划抓取的图片信息；或根据图片唯一标识获取单张图片信息。
   * 若监控点编号集合和图片唯一标识均不传入，则分页查询出所有配置了抓图计划的监控点图片信息。
   * 同时按照调用方指定的排序方式对图片进行排序。
   * 进行图片查询操作的用户需要配置相应监控点的图片查询权限，该接口会根据传入的userId对数据进行权限过滤。
   * 建议：使用本接口前请先进行校时，确保本地时间与当前系统时间保持一致。
   * 全量查询时，结束时间请在当前时间的基础上至少提前一天，确保数据分页的准确性.
   *
   * @param pictureInfosRequest 请求Body
   * @return json
   * @throws Exception Exception
   */
  public static String pictureInfos(ArtemisConfig config, PictureInfosRequest pictureInfosRequest)
      throws Exception {
    String pictureInfosDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/pictureInfos";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, pictureInfosDataApi);
    String body = JsonUtils.encode(pictureInfosRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
