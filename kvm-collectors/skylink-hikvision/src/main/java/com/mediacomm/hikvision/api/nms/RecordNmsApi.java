package com.mediacomm.hikvision.api.nms;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.nms.record.ListRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:46:08.
 *
 * <AUTHOR>
 */
public class RecordNmsApi {

  /**
   * 根据监控点列表查询录像完整性结果.
   *
   * @param config      ArtemisConfig
   * @param listRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String list(ArtemisConfig config, ListRequest listRequest) throws Exception {
    String listDataApi = Constants.ARTEMIS_PATH + "/api/nms/v1/record/list";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, listDataApi);
    String body = JsonUtils.encode(listRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
