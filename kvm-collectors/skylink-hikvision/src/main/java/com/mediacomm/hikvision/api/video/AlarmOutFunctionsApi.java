package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.alarmout.GetRequest;
import com.mediacomm.hikvision.entity.video.alarmout.SetRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;

/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class AlarmOutFunctionsApi {


  /**
   * 获取报警输出通道状态.
   *
   * @param config     ArtemisConfig
   * @param getRequest Request
   * @return 报警输出通道状态
   * @throws Exception Exception
   */
  public static String get(ArtemisConfig config, GetRequest getRequest) throws Exception {
    String getDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/alarmOut/status/get";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, getDataApi);
    String body = JsonUtils.encode(getRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 配置报警输出通道状态.
   *
   * @param config     ArtemisConfig
   * @param setRequest Request
   * @return 返回值
   * @throws Exception Exception
   */
  public static String set(ArtemisConfig config, SetRequest setRequest) throws Exception {
    String setDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/alarmOut/status/set";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, setDataApi);
    String body = JsonUtils.encode(setRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
