package com.mediacomm.hikvision.api.nms;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.nms.camera.GetRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:46:08.
 *
 * <AUTHOR>
 */
public class EncodeDeviceNmsApi {

  /**
   * 获取编码设备在线状态.
   *
   * @param config     ArtemisConfig
   * @param getRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String get(ArtemisConfig config, GetRequest getRequest) throws Exception {
    String getDataApi = Constants.ARTEMIS_PATH + "/api/nms/v1/online/encode_device/get";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, getDataApi);
    String body = JsonUtils.encode(getRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
