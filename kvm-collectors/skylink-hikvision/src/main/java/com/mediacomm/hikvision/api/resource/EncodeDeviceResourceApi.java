package com.mediacomm.hikvision.api.resource;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.resource.encodedevice.SearchRequest;
import com.mediacomm.hikvision.entity.resource.encodedevice.TimeRangeRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 14:02:46.
 *
 * <AUTHOR>
 */
public class EncodeDeviceResourceApi {

  /**
   * 查询编码设备列表v2.
   *
   * @param config        ArtemisConfig
   * @param searchRequest Request
   * @return 编码设备列表
   * @throws Exception Exception
   */
  public String search(ArtemisConfig config, SearchRequest searchRequest) throws Exception {
    String searchDataApi = Constants.ARTEMIS_PATH + "/api/resource/v2/encodeDevice/search";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchDataApi);
    String body = JsonUtils.encode(searchRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }


  /**
   * 增量获取编码设备数据.
   *
   * @param config           ArtemisConfig
   * @param timeRangeRequest Request
   * @return 编码设备数据
   * @throws Exception Exception
   */
  public String timeRange(ArtemisConfig config, TimeRangeRequest timeRangeRequest)
      throws Exception {
    String timeRangeDataApi = Constants.ARTEMIS_PATH + "/api/resource/v1/encodeDevice/timeRange";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, timeRangeDataApi);
    String body = JsonUtils.encode(timeRangeRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }
}
