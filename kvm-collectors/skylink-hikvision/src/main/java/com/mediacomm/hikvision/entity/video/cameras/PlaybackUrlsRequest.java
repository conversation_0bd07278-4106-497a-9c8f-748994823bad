package com.mediacomm.hikvision.entity.video.cameras;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlaybackUrlsRequest {
  private String cameraIndexCode;
  private String recordLocation;
  private String protocol;
  private Integer transmode;
  private String beginTime;
  private String endTime;
  private String uuid;
  private String expand;
  private String streamform;
  private Integer lockType;
}
