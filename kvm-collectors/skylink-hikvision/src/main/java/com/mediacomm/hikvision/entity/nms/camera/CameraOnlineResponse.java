package com.mediacomm.hikvision.entity.nms.camera;

import java.util.List;
import lombok.Data;

/**
 * CameraOnlineResponce.
 */
@Data
public class CameraOnlineResponse {

  private String code;
  private CamData data;
  private String msg;

  /**
   * .
   */
  @Data
  public static class CamData {
    private List<CamList> list;
    private long pageNo;
    private long pageSize;
    private long total;
    private long totalPage;
  }

  /**
   * .
   */
  @Data
  public static class CamList {
    private String cn;
    private String collectTime;
    private Object deviceIndexCode;
    private Object deviceType;
    private String indexCode;
    private Object ip;
    private Object manufacturer;
    private long online;
    private Object port;
    private String regionIndexCode;
    private String regionName;
    private String treatyType;
  }
}
