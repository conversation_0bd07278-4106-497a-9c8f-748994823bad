package com.mediacomm.hikvision.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.hikvision.api.nms.CameraNmsApi;
import com.mediacomm.hikvision.api.resource.CamerasResourceApi;
import com.mediacomm.hikvision.api.video.CamerasFunctionsApi;
import com.mediacomm.hikvision.entity.nms.camera.CameraOnlineResponse;
import com.mediacomm.hikvision.entity.nms.camera.GetRequest;
import com.mediacomm.hikvision.entity.resource.camera.HikvisionCameraInfo;
import com.mediacomm.hikvision.entity.resource.camera.SearchCameraResponse;
import com.mediacomm.hikvision.entity.resource.camera.SearchRequest;
import com.mediacomm.hikvision.entity.video.cameras.PreviewUrlsRequest;
import com.mediacomm.hikvision.entity.video.cameras.PreviewUrlsResponse;
import com.mediacomm.hikvision.util.mapper.HikvisionEntityMapper;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * HikvisionCmdServer.
 */
@Slf4j
@Controller
public class HikvisionCmdServer extends CmdServer {

  private static final String HLS_PROTOCOL = "hls";
  @Autowired
  private RedisUtil redisUtil;

  @Autowired
  private HikvisionEntityMapper hikvisionEntityMapper;

  private ArtemisConfig artemisConfig(KvmMaster kvmMaster) {
    String port = Property.findValueByKey(kvmMaster.getCollectorProperties(), "port", "8443");
    String key = Property.findValueByKey(kvmMaster.getCollectorProperties(), "appKey", "");
    String secret = Property.findValueByKey(kvmMaster.getCollectorProperties(), "appSecret", "");
    return new ArtemisConfig(kvmMaster.getDeviceIp() + ":" + port, key, secret);
  }

  /**
   * 刷新IPC状态.
   */
  public void refreshIpcStatus() {
    Collection<KvmMaster> kvmMasters =
        kvmMasterService.allByDeviceModel(DeviceType.HIKVISION_SECURE_HOST.getDeviceTypeId());
    for (KvmMaster kvmMaster : kvmMasters) {
      Collection<KvmAssetVo> allIpc =
          kvmAssetService.allByDeviceModelId(DeviceType.HIKVISION_SECURE_IPC.getDeviceTypeId(),
              kvmMaster.getMasterId());
      Map<String, String> collectMap = allIpc.stream()
          .collect(Collectors.toMap(KvmAssetVo::getHardcode, KvmAssetVo::getAssetId));
      List<String> collect =
          allIpc.stream().map(KvmAssetVo::getHardcode).collect(Collectors.toList());
      List<List<String>> partition = Lists.partition(collect, 500);
      for (List<String> part : partition) {
        GetRequest getRequest = new GetRequest();
        getRequest.setIndexCodes(part);
        getRequest.setPageNo(1);
        getRequest.setPageSize(500);
        try {
          CameraOnlineResponse cameraOnlineResponse =
              CameraNmsApi.get(artemisConfig(kvmMaster), getRequest);
          if (cameraOnlineResponse != null) {
            for (CameraOnlineResponse.CamList camList : cameraOnlineResponse.getData().getList()) {
              if (collectMap.containsKey(camList.getIndexCode())) {
                redisUtil.set(RedisUtil.redisKey(RedisKey.EXT, RedisSignalKey.LINK_STATUS,
                        collectMap.get(camList.getIndexCode())),
                    String.valueOf(camList.getOnline() == 0));
              }
            }
          }
        } catch (Exception e) {
          log.error("Can not get All Cameras.", e);
        }
      }
    }

  }

  /**
   * refreshConfig.
   */
  public String refreshConfig(String msg) {
    MqRequest request = JsonUtils.decode(msg, MqRequest.class);
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    return refreshConfig(kvmMaster);
  }

  /**
   * refreshConfig.
   */
  public String refreshConfig(KvmMaster kvmMaster) {
    try {
      List<HikvisionCameraInfo> allCameras = getAllCameras(kvmMaster);
      allCameras.forEach(item -> saveHikvisionCamera(item, kvmMaster));
    } catch (Exception ex) {
      log.error("Can not get All Cameras.", ex);
      return Result.failureStr("Can not get All Cameras.", ResponseCode.EX_FAILURE_500);
    }
    return Result.okStr();
  }

  /**
   * get All Cameras.
   */
  public List<HikvisionCameraInfo> getAllCameras(KvmMaster kvmMaster) throws Exception {
    ArtemisConfig artemisConfig = getArtemisConfig(kvmMaster);
    List<HikvisionCameraInfo> res = Lists.newArrayList();
    int pageSize = 1000;
    SearchCameraResponse cameraResponse = CamerasResourceApi.search(artemisConfig,
        SearchRequest.builder().pageNo(1).pageSize(pageSize).build());
    if (cameraResponse.getData() == null) {
      throw new Exception(cameraResponse.getMsg());
    }
    res.addAll(cameraResponse.getData().getList());
    int total = cameraResponse.getData().getTotal();
    int pageNo = cameraResponse.getData().getPageNo();
    int totalPageNo = (int) Math.ceil(total / 1000.0);
    if (totalPageNo > 1) {
      for (int i = pageNo + 1; i <= totalPageNo; i++) {
        SearchCameraResponse cameraRes = CamerasResourceApi.search(artemisConfig,
            SearchRequest.builder().pageNo(i).pageSize(pageSize).build());
        res.addAll(cameraRes.getData().getList());
      }
    }
    return res;
  }

  private ArtemisConfig getArtemisConfig(KvmMaster kvmMaster) {
    ArtemisConfig artemisConfig = new ArtemisConfig();
    String artemisPort = Property.findValueByKey(kvmMaster.getCollectorProperties(), "port", "8443");
    artemisConfig.setHost(kvmMaster.getDeviceIp() + ":" + artemisPort);
    String appKey = Property.findValueByKey(kvmMaster.getCollectorProperties(), "appKey", "");
    artemisConfig.setAppKey(appKey);
    String appSecret = Property.findValueByKey(kvmMaster.getCollectorProperties(), "appSecret", "");
    artemisConfig.setAppSecret(appSecret);
    return artemisConfig;
  }

  private void saveHikvisionCamera(HikvisionCameraInfo cameraInfo, KvmMaster kvmMaster) {
    KvmAsset kvmAsset = hikvisionEntityMapper.toKvmAsset(cameraInfo, kvmMaster);
    kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
  }

  /**
   * getTxSnapshot.
   */
  public String getTxSnapshot(String msg) {
    MqRequest<ObjectIds> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    List<SnapshotStatus> statuses = Lists.newArrayList();
    for (String id : request.getBody().getIds()) {
      KvmAsset tx = kvmAssetService.getById(id);
      if (DeviceType.HIKVISION_SECURE_IPC.getDeviceTypeId().equals(tx.getDeviceModel())) {
        String previewUrls = newPreviewUrl(kvmMaster, tx.getHardcode());
        if (!Strings.isNullOrEmpty(previewUrls)) {
          statuses.add(SnapshotStatus.builder()
              .id(id).masterId(tx.getMasterId()).type(HLS_PROTOCOL).path(previewUrls).build());
        }
      }
    }
    return Result.okStr(statuses);
  }

  /**
   * 获取新的IPC预览地址.
   */
  private String newPreviewUrl(KvmMaster kvmMaster, String cameraIndexCode) {
    ArtemisConfig artemisConfig = getArtemisConfig(kvmMaster);
    PreviewUrlsRequest previewUrlsRequest = PreviewUrlsRequest.builder()
        .cameraIndexCode(cameraIndexCode).protocol(HLS_PROTOCOL).build();
    PreviewUrlsResponse response;
    try {
      response = CamerasFunctionsApi.previewUrls(artemisConfig, previewUrlsRequest);
    } catch (Exception e) {
      log.error(cameraIndexCode + " can not get previewUrls", e);
      return null;
    }
    if (!"success".equals(response.getMsg())) {
      log.error(cameraIndexCode + " can not get previewUrls", response);
    }
    return response.getData().getUrl();
  }

  @Override
  public String openVwPanels(String msg) {
    return null;
  }
}
