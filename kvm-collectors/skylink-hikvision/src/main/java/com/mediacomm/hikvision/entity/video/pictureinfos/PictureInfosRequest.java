package com.mediacomm.hikvision.entity.video.pictureinfos;

import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PictureInfosRequest {
  private Integer pageNo;
  private Integer pageSize;
  private ArrayList<String> cameraIndexCodes;
  private String captureId;
  private String startTime;
  private String endTime;
  private String netProtocol;
  private Integer sort;
  private Integer compressRatio;
}
