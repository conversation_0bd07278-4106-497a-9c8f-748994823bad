package com.mediacomm.hikvision.api.video;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.video.events.PictureRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * Auto Create on 2022-08-24 17:49:35.
 *
 * <AUTHOR>
 */
public class EventsFunctionApi {

  /**
   * 获取视频事件的图片.
   *
   * @param pictureRequest Request
   * @return json
   * @throws Exception Exception
   */
  public static String picture(ArtemisConfig config, PictureRequest pictureRequest)
      throws Exception {
    String pictureDataApi = Constants.ARTEMIS_PATH + "/api/video/v1/events/picture";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, pictureDataApi);
    String body = JsonUtils.encode(pictureRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

}
