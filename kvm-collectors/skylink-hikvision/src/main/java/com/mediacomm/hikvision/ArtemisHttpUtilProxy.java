package com.mediacomm.hikvision;

import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;

/**
 * ArtemisHttpUtil代理.
 */
public class ArtemisHttpUtilProxy {
  public static final String PROXY_KEY = "skylink_secure_test_key";

  public static String doGetArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                    Map<String, String> querys, String accept, String contentType,
                                    Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doGetArtemis(artemisConfig, path, querys, accept, contentType, header);
  }

  public static String doGetArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                    Map<String, String> querys, String accept, String contentType)
      throws Exception {
    return ArtemisHttpUtil.doGetArtemis(artemisConfig, path, querys, accept, contentType);
  }

  public static HttpResponse doGetResponse(ArtemisConfig artemisConfig, Map<String, String> path,
                                           Map<String, String> querys, String accept,
                                           String contentType, Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doGetResponse(artemisConfig, path, querys, accept, contentType, header);
  }

  public static String doPostFormArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                         Map<String, String> paramMap, Map<String, String> querys,
                                         String accept, String contentType,
                                         Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doPostFormArtemis(artemisConfig, path, paramMap, querys, accept,
        contentType, header);
  }

  public static String doPostFormArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                         Map<String, Object> paramMap, Map<String, String> querys,
                                         String accept, String contentType)
      throws Exception {
    return ArtemisHttpUtil.doPostFormArtemis(artemisConfig, path, paramMap, querys, accept,
        contentType);
  }

  public static HttpResponse doPostFormImgArtemis(ArtemisConfig artemisConfig,
                                                  Map<String, String> path,
                                                  Map<String, String> paramMap,
                                                  Map<String, String> querys, String accept,
                                                  String contentType, Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doPostFormImgArtemis(artemisConfig, path, paramMap, querys, accept,
        contentType, header);
  }

  /**
   * .
   */
  public static String doPostStringArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                           String body, Map<String, String> querys, String accept,
                                           String contentType, Map<String, String> header)
      throws Exception {
    if (artemisConfig.getAppKey().equalsIgnoreCase(PROXY_KEY)) {
      return doPostString(artemisConfig, body, path, header, accept, contentType);
    } else {
      return ArtemisHttpUtil.doPostStringArtemis(artemisConfig, path, body, querys, accept,
          contentType, header);
    }
  }

  /**
   * .
   */
  public static String doPostStringArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                           String body, Map<String, String> querys, String accept,
                                           String contentType)
      throws Exception {
    if (artemisConfig.getAppKey().equalsIgnoreCase(PROXY_KEY)) {
      return doPostString(artemisConfig, body, path, new HashMap<>(), accept, contentType);
    } else {
      return ArtemisHttpUtil.doPostStringArtemis(artemisConfig, path, body, querys, accept,
          contentType);
    }
  }

  public static HttpResponse doPostStringImgArtemis(ArtemisConfig artemisConfig,
                                                    Map<String, String> path, String body,
                                                    Map<String, String> querys, String accept,
                                                    String contentType, Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doPostStringImgArtemis(artemisConfig, path, body, querys, accept,
        contentType, header);
  }

  public static String doPostBytesArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                          byte[] bytesBody, Map<String, String> querys,
                                          String accept, String contentType,
                                          Map<String, String> header)
      throws Exception {
    return ArtemisHttpUtil.doPostBytesArtemis(artemisConfig, path, bytesBody, querys, accept,
        contentType, header);
  }

  public static String doPostBytesArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                          byte[] bytesBody, Map<String, String> querys,
                                          String accept, String contentType)
      throws Exception {
    return ArtemisHttpUtil.doPostBytesArtemis(artemisConfig, path, bytesBody, querys, accept,
        contentType);
  }

  public static String doPostFileFormArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                             Map<String, Object> paramMap,
                                             Map<String, String> querys, String accept,
                                             String contentType)
      throws Exception {
    return ArtemisHttpUtil.doPostFileFormArtemis(artemisConfig, path, paramMap, querys, accept,
        contentType);
  }

  public static String doPutStringArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                          String body, String accept, String contentType)
      throws Exception {
    return ArtemisHttpUtil.doPutStringArtemis(artemisConfig, path, body, accept, contentType);
  }

  public static String doPutBytesArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                         byte[] bytesBody, String accept, String contentType)
      throws Exception {
    return ArtemisHttpUtil.doPutBytesArtemis(artemisConfig, path, bytesBody, accept, contentType);
  }

  public static String doDeleteArtemis(ArtemisConfig artemisConfig, Map<String, String> path,
                                       Map<String, String> querys, String accept,
                                       String contentType)
      throws Exception {
    return ArtemisHttpUtil.doDeleteArtemis(artemisConfig, path, querys, accept, contentType);
  }

  private static String doPostString(ArtemisConfig artemisConfig, String body,
                                     Map<String, String> path,
                                     Map<String, String> headers,
                                     String accept, String contentType)
      throws IOException {
    String httpSchema = (String) path.keySet().toArray()[0];
    if (httpSchema != null && !StringUtils.isEmpty(httpSchema)) {
      if (StringUtils.isNotBlank(accept)) {
        headers.put("Accept", accept);
      } else {
        headers.put("Accept", "*/*");
      }

      if (StringUtils.isNotBlank(contentType)) {
        headers.put("Content-Type", contentType);
      } else {
        headers.put("Content-Type", "application/text;charset=UTF-8");
      }
      // 固定为http
      String url = String.format("%s%s%s", "http://", artemisConfig.host, path.get(httpSchema));

      URL obj = URI.create(url).toURL();
      HttpURLConnection con = (HttpURLConnection) obj.openConnection();
      con.setRequestMethod("POST");

      // Set request headers
      for (Map.Entry<String, String> header : headers.entrySet()) {
        con.setRequestProperty(header.getKey(), header.getValue());
      }

      // Set request body
      String requestBody = body;
      con.setDoOutput(true);
      byte[] requestBodyBytes = requestBody.getBytes(StandardCharsets.UTF_8);
      con.getOutputStream().write(requestBodyBytes);

      // Send the request
      int responseCode = con.getResponseCode();
      System.out.println(responseCode);

      // Read the response
      BufferedReader in =
          new BufferedReader(new InputStreamReader(con.getInputStream(), StandardCharsets.UTF_8));
      String inputLine;
      StringBuilder response = new StringBuilder();
      while ((inputLine = in.readLine()) != null) {
        response.append(inputLine);
      }
      in.close();
      return response.toString();
    } else {
      throw new RuntimeException("http和https参数错误httpSchema: " + httpSchema);
    }
  }
}
