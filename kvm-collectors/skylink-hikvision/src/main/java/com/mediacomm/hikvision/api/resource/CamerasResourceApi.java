package com.mediacomm.hikvision.api.resource;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.ContentType;
import com.hikvision.artemis.sdk.constant.HttpSchema;
import com.mediacomm.hikvision.ArtemisHttpUtilProxy;
import com.mediacomm.hikvision.Constants;
import com.mediacomm.hikvision.entity.resource.camera.CamerasRequest;
import com.mediacomm.hikvision.entity.resource.camera.SearchCameraResponse;
import com.mediacomm.hikvision.entity.resource.camera.SearchRequest;
import com.mediacomm.hikvision.entity.resource.camera.TimeRangeRequest;
import com.mediacomm.util.JsonUtils;
import java.util.Map;


/**
 * .
 *
 * <AUTHOR>
 */
public class CamerasResourceApi {

  /**
   * 查询监控点列表v2.
   *
   * @param config        ArtemisConfig
   * @param searchRequest 请求Body
   * @return 监控点列表
   * @throws Exception Exception
   */
  public static SearchCameraResponse search(ArtemisConfig config, SearchRequest searchRequest)
      throws Exception {
    String searchDataApi = Constants.ARTEMIS_PATH + "/api/resource/v2/camera/search";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, searchDataApi);
    String body = JsonUtils.encode(searchRequest);
    String res = ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
    SearchCameraResponse response = JsonUtils.decode(res, SearchCameraResponse.class);
    return response;
  }


  /**
   * 分页获取监控点资源
   * 获取监控点列表接口可用来全量同步监控点信息，返回结果分页展示.
   *
   * @param config         ArtemisConfig
   * @param camerasRequest 请求Body
   * @return 监控点资源
   * @throws Exception Exception
   */
  public static String cameras(ArtemisConfig config, CamerasRequest camerasRequest)
      throws Exception {
    String camerasDataApi = Constants.ARTEMIS_PATH + "/api/resource/v1/cameras";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, camerasDataApi);
    String body = JsonUtils.encode(camerasRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }

  /**
   * 增量获取监控点数据
   * 根据资源类型、时间段增量获取资源，包含已删除数据。资源密码以*代替，其中开始日期与结束日期的时间差必须在1-48小时内.
   *
   * @param config           ArtemisConfig
   * @param timeRangeRequest 请求Body
   * @return 监控点数据
   * @throws Exception Exception
   */
  public static String timeRange(ArtemisConfig config, TimeRangeRequest timeRangeRequest)
      throws Exception {
    String timeRangeDataApi = Constants.ARTEMIS_PATH + "/api/resource/v1/camera/timeRange";
    Map<String, String> path = Map.of(HttpSchema.HTTPS, timeRangeDataApi);
    String body = JsonUtils.encode(timeRangeRequest);
    return ArtemisHttpUtilProxy.doPostStringArtemis(config, path, body, null, null,
        ContentType.CONTENT_TYPE_JSON);
  }


}
