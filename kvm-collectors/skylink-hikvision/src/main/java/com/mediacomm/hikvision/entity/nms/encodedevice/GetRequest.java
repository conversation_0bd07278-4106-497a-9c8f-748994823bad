package com.mediacomm.hikvision.entity.nms.encodedevice;

import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetRequest {
  private String regionId;
  private String ip;
  private ArrayList<String> indexCodes;
  private String status;
  private Integer pageNo;
  private Integer pageSize;
  private String includeSubNode;
}
