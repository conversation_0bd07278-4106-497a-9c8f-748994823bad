package com.mediacomm.hikvision.entity.resource.camera;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * .
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class HikvisionCameraInfo  {
  private String indexCode;
  private String regionIndexCode;
  private String name;
  private String parentIndexCode;
  private Integer cameraType;
  private Integer chanNum;
  private String dacIndexCode;
  private String capability;
  private String channelType;
  private String decodeTag;
  private String resourceType;
  private String createTime;
  private String updateTime;
  private Integer sort;
  private Integer disOrder;
  private String cameraRelateTalk;
  private Integer transType;
  private String treatyType;
  private Integer cascadeType;
  /**
   * 所属区域路径，由唯一标示组成，最大10级，格式： @根节点@子区域1@子区域2@.
   */
  private String regionName;
  private String regionPath;
  /**
   * 区域路径名称，"/"分隔.
   */
  private String regionPathName;
  private String longitude;
  private String latitude;
  private String installLocation;

}
