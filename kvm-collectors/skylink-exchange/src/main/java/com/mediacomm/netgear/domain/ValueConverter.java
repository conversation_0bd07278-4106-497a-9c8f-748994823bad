package com.mediacomm.netgear.domain;

import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.SignalValue;

public class ValueConverter {
  public static final String DEVICE_VERSION = "1.3.6.1.4.1.4526.10.1.1.1.13";
  public static final String DEVICE_DESCR = "1.3.6.1.4.1.4526.10.1.1.1.1";
  public static final String DEVICE_MODEL = "1.3.6.1.4.1.4526.10.1.1.1.3";
  public static final String DEVICE_SERIAL_NUMBER = "1.3.6.1.4.1.4526.10.1.1.1.4";
  public static final String DEVICE_CPU = "1.3.6.1.4.1.4526.10.1.1.4.9";
  public static final String DEVICE_MEM_FREE = "1.3.6.1.4.1.4526.10.1.1.4.1";
  public static final String DEVICE_MEM_TOTAL = "1.3.6.1.4.1.4526.10.1.1.4.2";

  public static final String DEVICE_PORT_DESCR = "1.3.6.1.2.1.2.2.1.2";
  public static final String DEVICE_PORT_TYPE = "1.3.6.1.2.1.2.2.1.3";
  public static final String DEVICE_PORT_STATUS = "1.3.6.1.2.1.2.2.1.7";
  public static final String DEVICE_PORT_LINE_STATUS = "1.3.6.1.2.1.2.2.1.8";
  public static final String DEVICE_PORT_HIGH_SPEED = "1.3.6.1.2.1.31.1.1.1.15";
  public static final String DEVICE_PORT_PRESENT = "1.3.6.1.2.1.31.1.1.1.17";
  public static final String DEVICE_PORT_INERROR = "1.3.6.1.2.1.2.2.1.14";
  public static final String DEVICE_PORT_OUTERROR = "1.3.6.1.2.1.2.2.1.14";

  public static final String FAN_TYPE = "1.3.6.1.4.1.4526.10.43.1.6.1.2"; // 1.0~2 {fixed(1), removable(2), fixedAC(3),
                                        // removableDC(4),
  // fixedDC(5),removableAC(6) }
  public static final String FAN_STATUS = "1.3.6.1.4.1.4526.10.43.1.6.1.3"; // 1.0~2
  // {notpresent(1),operational(2),failed(3),powering(4),nopower(5),notpowering(6),
  // incompatible(7) }
  public static final String FAN_SPEED = "1.3.6.1.4.1.4526.10.43.1.6.1.4"; // 1.0~2
  public static final String FAN_DUTY_LEVEL = "1.3.6.1.4.1.4526.10.43.1.6.1.5"; // 1.0~2
  public static final String POWER_TYPE = "1.3.6.1.4.1.4526.10.43.1.7.1.2"; // 1.0~1 {fixed(1),removable(2), fixedAC(3),removableDC(4),
                                        // fixedDC(5),
  // removableAC(6) }
  public static final String POWER_STATUS = "1.3.6.1.4.1.4526.10.43.1.7.1.3"; // 1.0~1 {notpresent(1), operational(2), failed(3),
                                        // powering(4),
  // nopower(5),notpowering(6), incompatible(7) }
  public static final String TEMP_SENSOR_TYPE = "1.3.6.1.4.1.4526.10.43.1.8.1.3"; // 1.0~1 fixed(1), removable(2), fixedAC(3), removableDC(4),
  // fixedDC(5), removableAC(6) }
  public static final String TEMP_SENSOR_STATUS = "1.3.6.1.4.1.4526.10.43.1.8.1.4"; // 1.0~1 {low(0), normal(1), warning(2), critical(3),
  // shutdown(4), notpresent(5), notoperational(6)
  public static final String TEMP_SENSOR_VALUE = "1.3.6.1.4.1.4526.10.43.1.8.1.5"; // 1.0~1
  public static final String PORT_COUNT = "ports";
  public static final String MASTER_PORT = "master.port";
  public static final String MASTER_PORT_STATUS = "port.status.0";
  public static final String MASTER_PORT_LINK_STATUS = "port.line.status.0";

  public Integer getPortNum(EnvDevice device) {
    return Integer.valueOf(Property.findValueByKey(device.getProperties(),
        "ports", PORT_COUNT));
  }

  public SignalValue convert(String oid, String value) {
    SignalValue signalValue = new SignalValue();
    if (oid.startsWith(DEVICE_VERSION)) {
      signalValue.setValue(value);
    } else if (oid.startsWith(DEVICE_DESCR)) {
      signalValue.setValue(value);
    } else if (oid.startsWith(DEVICE_MODEL)) {
      signalValue.setValue(value);
    } else if (oid.startsWith(DEVICE_PORT_DESCR)) {
      signalValue.setValue(value);
    } else if (oid.startsWith(DEVICE_PORT_TYPE)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(DEVICE_PORT_HIGH_SPEED)) {
      signalValue.setValue(Double.valueOf(value));
    } else if (oid.startsWith(DEVICE_PORT_STATUS)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(DEVICE_PORT_LINE_STATUS)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(DEVICE_PORT_PRESENT)) {
      signalValue.setValue(Double.valueOf(value));
    } else if (oid.startsWith(DEVICE_CPU)) {
      signalValue.setValue(Double.valueOf(value.substring(value.indexOf('(') + 1, value.indexOf('%')).trim()));
    } else if (oid.startsWith(DEVICE_MEM_FREE)) {
      signalValue.setValue(Double.parseDouble(value) / 1000);
    } else if (oid.startsWith(DEVICE_MEM_TOTAL)) {
      signalValue.setValue(Double.parseDouble(value) / 1000);
    } else if (oid.startsWith(FAN_TYPE)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(FAN_STATUS)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(FAN_SPEED)) {
      signalValue.setValue(Double.valueOf(value));
    } else if (oid.startsWith(FAN_DUTY_LEVEL)) {
      signalValue.setValue(Double.valueOf(value));
    } else if (oid.startsWith(POWER_TYPE)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(POWER_STATUS)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(TEMP_SENSOR_TYPE)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(TEMP_SENSOR_STATUS)) {
      signalValue.setValue(Integer.valueOf(value));
    } else if (oid.startsWith(TEMP_SENSOR_VALUE)) {
      signalValue.setValue(Double.valueOf(value));
    }
    return signalValue;
  }

  public String convertSignalId(String oid) {
    if (oid.startsWith(DEVICE_VERSION)) {
      return "device.version";
    } else if (oid.startsWith(DEVICE_DESCR)) {
      return "device.desc";
    } else if (oid.startsWith(DEVICE_CPU)) {
      return "device.cpu";
    } else if (oid.startsWith(DEVICE_MEM_FREE)) {
      return "device.mem.free";
    } else if (oid.startsWith(DEVICE_MEM_TOTAL)) {
      return "device.mem.total";
    } else if (oid.startsWith(DEVICE_MODEL)) {
      return "device.model";
    } else if (oid.startsWith(DEVICE_PORT_DESCR)) {
      return "port.desc." + getIndex(oid);
    } else if (oid.startsWith(DEVICE_PORT_TYPE)) {
      return "port.type." + getIndex(oid);
    } else if (oid.startsWith(DEVICE_PORT_HIGH_SPEED)) {
      return "port.speed." + getIndex(oid);
    } else if (oid.startsWith(DEVICE_PORT_STATUS)) {
      return "port.status." + getIndex(oid);
    } else if (oid.startsWith(DEVICE_PORT_LINE_STATUS)) {
      return "port.line.status." + getIndex(oid);
    } else if (oid.startsWith(DEVICE_PORT_PRESENT)) {
      return "port.present." + getIndex(oid);
    } else if (oid.startsWith(FAN_TYPE)) {
      return "fan.type." + getIndex(oid);
    } else if (oid.startsWith(FAN_STATUS)) {
      return "fan.status." + getIndex(oid);
    } else if (oid.startsWith(FAN_SPEED)) {
      return "fan.speed." + getIndex(oid);
    } else if (oid.startsWith(FAN_DUTY_LEVEL)) {
      return "fan.duty.level." + getIndex(oid);
    } else if (oid.startsWith(POWER_TYPE)) {
      return "power.type." + getIndex(oid);
    } else if (oid.startsWith(POWER_STATUS)) {
      return "power.status." + getIndex(oid);
    } else if (oid.startsWith(TEMP_SENSOR_TYPE)) {
      return "temp.sensor.type." + getIndex(oid);
    } else if (oid.startsWith(TEMP_SENSOR_STATUS)) {
      return "temp.sensor.status." + getIndex(oid);
    } else if (oid.startsWith(TEMP_SENSOR_VALUE)) {
      return "temp.sensor.value." + getIndex(oid);
    } else {
      return "";
    }
  }

  public int getIndex(String oid) {
    return Integer.parseInt(oid.substring(oid.lastIndexOf('.') + 1));
  }
}
