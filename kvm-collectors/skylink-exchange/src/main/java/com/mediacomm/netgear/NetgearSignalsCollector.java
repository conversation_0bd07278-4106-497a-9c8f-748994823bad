package com.mediacomm.netgear;

import cn.hutool.core.net.NetUtil;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.dao.NetLinkMerge;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.netgear.domain.ValueConverter;
import com.mediacomm.snmp.SnmpHelper;
import com.mediacomm.system.base.kvm.SignalCollector;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.snmp4j.smi.VariableBinding;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class NetgearSignalsCollector extends SignalCollector {
  @Resource
  private EnvDeviceService envDeviceService;
  private ValueConverter valueConverter = new ValueConverter();

  /**
   * 每5秒刷新一次凯撒主机状态.
   */
  @Scheduled(cron = "*/10 * * * * ?")
  @SchedulerLock(name = "netgearServer", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void reloadNetgearServer() {
    Collection<EnvDeviceVo> envDevices =
            envDeviceService.allByDeviceType(DeviceType.NETGEAR.getDeviceType());
    Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
    for (EnvDeviceVo netgear : envDevices) {
      deviceSignalValueInCache.put(getSignalRedisKey(netgear), new HashMap<>());
      deviceSignalValueInCache.put(getDescRedisKey(netgear), new HashMap<>());
      int portCount =
              Integer.parseInt(Property.findValueByKey(
                      netgear.getCollectorProperties(), "ports", "8"));
      int masterPort =
              Integer.parseInt(Property.findValueByKey(
                      netgear.getProperties(), ValueConverter.MASTER_PORT, "0"));
      SnmpHelper helper = new SnmpHelper(netgear.getDeviceIp());
      refreshConfig(helper, netgear);
      getDeviceStatus(helper, deviceSignalValueInCache, netgear);
      getPortDecr(helper, deviceSignalValueInCache, portCount, masterPort, netgear);
      getPortType(helper, deviceSignalValueInCache, portCount, masterPort, netgear);
      getPortStatus(helper, deviceSignalValueInCache, portCount, masterPort, netgear);
      getPortSpeed(helper, deviceSignalValueInCache, portCount, masterPort, netgear);
      getPortLinkStatus(helper, deviceSignalValueInCache, portCount, masterPort, netgear);
      getFanType(helper, deviceSignalValueInCache, netgear);
      getFanStatus(helper, deviceSignalValueInCache, netgear);
      getFanSpeed(helper, deviceSignalValueInCache, netgear);
      getFanDutyLevel(helper, deviceSignalValueInCache, netgear);
      getPowerType(helper, deviceSignalValueInCache, netgear);
      getPowerStatus(helper, deviceSignalValueInCache, netgear);
      getTempSensorType(helper, deviceSignalValueInCache, netgear);
      getTempSensorStatus(helper, deviceSignalValueInCache, netgear);
      getTempValue(helper, deviceSignalValueInCache, netgear);
      checkLinkStatus(netgear.getDeviceIp(), deviceSignalValueInCache, netgear);
    }
    redisUtil.batchHashSet(deviceSignalValueInCache);
    checkEnvSignalValue(envDevices);
  }

  private void refreshConfig(SnmpHelper helper, EnvDevice device) {
    List<String> oidList = new ArrayList<>();
    oidList.add(ValueConverter.DEVICE_MODEL);
    oidList.add(ValueConverter.DEVICE_VERSION);
    oidList.add(ValueConverter.DEVICE_SERIAL_NUMBER);
    List<VariableBinding> vbs = helper.simpleGetNext(oidList);
    String deviceVersion = null;
    String serialNumber = null;
    Integer masterPort = null;
    for (VariableBinding var : vbs) {
      if (var.getOid().toString().startsWith(ValueConverter.DEVICE_VERSION)) {
        deviceVersion = var.getVariable().toString();
      }
      if (var.getOid().toString().startsWith(ValueConverter.DEVICE_SERIAL_NUMBER)) {
        serialNumber = var.getVariable().toString();
      }
    }
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_DESCR);
    for (VariableBinding var : values) {
      if (var.getVariable().toString().contains("CPU Interface")) {
        masterPort = valueConverter.getIndex(var.getOid().toString());
      }
    }
    if (deviceVersion == null || masterPort == null || serialNumber == null) {
      log.warn(String.format("Refresh netgear %s failed!", device.getId()));
      return;
    }
    boolean needUpdate = false;
    if (device.getVersion() == null || !device.getVersion().equals(deviceVersion)) {
      device.setVersion(deviceVersion);
      needUpdate = true;
    }
    if (device.getVersion() == null || !device.getVersion().equals(deviceVersion)) {
      device.setVersion(deviceVersion);
      needUpdate = true;
    }
    if (device.getHardcode() == null || !device.getHardcode().equals(serialNumber)) {
      device.setHardcode(serialNumber);
      needUpdate = true;
    }
    Property property = Property.findFromArray(ValueConverter.MASTER_PORT, device.getProperties());
    if (property == null) {
      device.getProperties().add(
              new Property(ValueConverter.MASTER_PORT, String.valueOf(masterPort)));
      needUpdate = true;
    }
    if (needUpdate) {
      envDeviceService.updateById(device);
    }
  }

  private void getDeviceStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                               EnvDeviceVo device) {
    List<String> oids = new ArrayList<>();
    oids.add(ValueConverter.DEVICE_DESCR);
    oids.add(ValueConverter.DEVICE_CPU);
    oids.add(ValueConverter.DEVICE_MEM_FREE);
    oids.add(ValueConverter.DEVICE_MEM_TOTAL);
    List<VariableBinding> vbs = helper.simpleGetNext(oids);
    if (vbs == null) {
      return;
    }

    for (VariableBinding var : vbs) {
      if (var.getOid().toString().startsWith(ValueConverter.DEVICE_DESCR)) {
        deviceSignalValueMap.get(getDescRedisKey(device)).put("desc", var.getVariable().toString());
      } else if (var.getOid().toString().startsWith(ValueConverter.DEVICE_CPU)) {
        // cpu
        String key = valueConverter.convertSignalId(ValueConverter.DEVICE_CPU);
        Signal cpuStausSignal = getSignal(device.getDeviceModel(), key);
        DeviceSignalValue cpuSignalValue = setValue(cpuStausSignal,
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString()),
                device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(key, JsonUtils.encode(cpuSignalValue));
      } else if (var.getOid().toString().startsWith(ValueConverter.DEVICE_MEM_FREE)) {
        // mem
        String key = valueConverter.convertSignalId(ValueConverter.DEVICE_MEM_FREE);
        Signal menStausSignal = getSignal(device.getDeviceModel(), key);
        DeviceSignalValue menSignalValue = setValue(menStausSignal,
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString()),
                device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(key, JsonUtils.encode(menSignalValue));
      } else if (var.getOid().toString().startsWith(ValueConverter.DEVICE_MEM_TOTAL)) {
        // mem total
        String key = valueConverter.convertSignalId(ValueConverter.DEVICE_MEM_TOTAL);
        Signal menStausSignal = getSignal(device.getDeviceModel(), key);
        DeviceSignalValue menSignalValue = setValue(menStausSignal,
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString()),
                device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(key, JsonUtils.encode(menSignalValue));
      }
    }
  }

  private void getPortDecr(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                           int portCount, int masterPort, EnvDeviceVo device) {
    Map<Integer, String> portDesc = new HashMap<>();
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_DESCR);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      if (portNumber <= portCount) {
        portDesc.put(portNumber, var.getVariable().toString());
      } else if (portNumber == masterPort) {
        portDesc.put(0, var.getVariable().toString());
      }
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("portDesc", JsonUtils.encode(portDesc));
  }

  private void getPortType(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                           int portCount, int masterPort, EnvDeviceVo device) {
    Map<Integer, Integer> portTypes = new HashMap<>();
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_TYPE);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      if (portNumber <= portCount) {
        portTypes.put(portNumber, var.getVariable().toInt());
      } else if (portNumber == masterPort) {
        portTypes.put(0, var.getVariable().toInt());
      }
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("portTypes", JsonUtils.encode(portTypes));
  }

  private void getPortStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                             int portCount, int masterPort, EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_STATUS);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      if (portNumber <= portCount) {
        Signal portStausSignal = getSignal(device.getDeviceModel(), RedisSignalKey.PORT_STATUS,
                valueConverter.convertSignalId(var.getOid().toString()));
        DeviceSignalValue portSignalValue = setValue(portStausSignal,
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString()),
                device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(valueConverter.convertSignalId(var.getOid().toString()),
                JsonUtils.encode(portSignalValue));
      } else if (portNumber == masterPort) {
        Signal masterPortStausSignal = getSignal(device.getDeviceModel(), ValueConverter.MASTER_PORT_STATUS);
        DeviceSignalValue portSignalValue = setValue(masterPortStausSignal,
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString()),
                device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(ValueConverter.MASTER_PORT_STATUS,
                JsonUtils.encode(portSignalValue));
      }
    }
  }

  private void getPortSpeed(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                            int portCount, int masterPort, EnvDeviceVo device) {
    Map<Integer, Double> portSpeeds = new HashMap<>();
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_HIGH_SPEED);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      if (portNumber <= portCount) {
        portSpeeds.put(portNumber, (double)var.getVariable().toInt());
      } else if (portNumber == masterPort) {
        portSpeeds.put(0, (double)var.getVariable().toInt());
      }
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("portSpeeds", JsonUtils.encode(portSpeeds));
  }

  private void getPortLinkStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap, int portCount,
                                 int masterPort, EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.DEVICE_PORT_LINE_STATUS);
    Collection<NetLinkMerge> netLinkMerges = envDeviceService.allNetLinkAssoByNetId(device.getId());
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      SignalValue value =
              valueConverter.convert(var.getOid().toString(), var.getVariable().toString());
      if (portNumber <= portCount) {
        NetLinkMerge asso = null;
        if (netLinkMerges != null) {
          for (NetLinkMerge netLinkMerge : netLinkMerges) {
            if (netLinkMerge.getNetPort().equals(portNumber)) {
              asso = netLinkMerge;
            }
          }
        }
        if (asso != null) {
          value.setValueDescr(asso.getAssoDeviceName());
          if (!value.getStatusValue().equals(1) && !value.getStatusValue().equals(6)) {
            value.setValue(0);
          }
        }
        Signal portStausSignal = getSignal(device.getDeviceModel(), "port.line.status",
                valueConverter.convertSignalId(var.getOid().toString()));
        DeviceSignalValue portSignalValue = setValue(portStausSignal, value, device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(valueConverter.convertSignalId(var.getOid().toString()),
                JsonUtils.encode(portSignalValue));
      } else if (portNumber == masterPort) {
        Signal masterPortStausSignal = getSignal(device.getDeviceModel(),
                ValueConverter.MASTER_PORT_LINK_STATUS);
        DeviceSignalValue portSignalValue = setValue(masterPortStausSignal, value, device);
        deviceSignalValueMap.get(getSignalRedisKey(device)).put(ValueConverter.MASTER_PORT_LINK_STATUS,
                JsonUtils.encode(portSignalValue));
      }
    }
  }

  private void getFanType(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                          EnvDeviceVo device) {
    Map<Integer, Integer> funTypes = new HashMap<>();
    List<VariableBinding> values = helper.getSubTree(ValueConverter.FAN_TYPE);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      funTypes.put(portNumber, var.getVariable().toInt());
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("fanTypes", JsonUtils.encode(funTypes));
  }

  private void getFanStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                            EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.FAN_STATUS);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void getFanSpeed(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                           EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.FAN_SPEED);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void getFanDutyLevel(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                               EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.FAN_DUTY_LEVEL);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void getPowerType(SnmpHelper helper, Map<String,
          Map<String, String>> deviceSignalValueMap, EnvDeviceVo device) {
    Map<Integer, Integer> types = new HashMap<>();
    List<VariableBinding> values = helper.getSubTree(ValueConverter.POWER_TYPE);
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      types.put(portNumber, var.getVariable().toInt());
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("powerTypes", JsonUtils.encode(types));
  }

  private void getPowerStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                              EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.POWER_STATUS);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void getTempSensorType(SnmpHelper helper, Map<String,
          Map<String, String>> deviceSignalValueMap, EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.TEMP_SENSOR_TYPE);
    Map<Integer, Integer> types = new HashMap<>();
    for (VariableBinding var : values) {
      int portNumber = valueConverter.getIndex(var.getOid().toString());
      types.put(portNumber, var.getVariable().toInt());
    }
    deviceSignalValueMap.get(getDescRedisKey(device)).put("tempSensorTypes", JsonUtils.encode(types));
  }

  private void getTempSensorStatus(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap,
                                   EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.TEMP_SENSOR_STATUS);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void getTempValue(SnmpHelper helper, Map<String, Map<String, String>> deviceSignalValueMap, EnvDeviceVo device) {
    List<VariableBinding> values = helper.getSubTree(ValueConverter.TEMP_SENSOR_VALUE);
    saveSignalValue(values, device, deviceSignalValueMap);
  }

  private void checkLinkStatus(String deviceIp, Map<String, Map<String, String>> deviceSignalValueMap,
                               EnvDeviceVo device) {
    Signal linkStausSignal = getSignal(device.getDeviceModel(), RedisSignalKey.LINK_STATUS);
    DeviceSignalValue linkSignalValue;
    if (NetUtil.ping(deviceIp)) {
      linkSignalValue = setValue(linkStausSignal, new SignalValue(false), device);
    } else {
      linkSignalValue = setValue(linkStausSignal, new SignalValue(true), device);
    }
    deviceSignalValueMap.get(getSignalRedisKey(device))
            .put(RedisSignalKey.LINK_STATUS, JsonUtils.encode(linkSignalValue));
  }

  private void saveSignalValue(List<VariableBinding> values, EnvDeviceVo device,
                               Map<String, Map<String, String>> deviceSignalValueMap) {
    for (VariableBinding var : values) {
      Signal signal = getSignal(device.getDeviceModel(),
              valueConverter.convertSignalId(var.getOid().toString()));
      if (signal != null) {
        SignalValue value =
                valueConverter.convert(var.getOid().toString(), var.getVariable().toString());
        DeviceSignalValue fanSignalValue = setValue(signal, value, device);
        deviceSignalValueMap.get(getSignalRedisKey(device))
                .put(valueConverter.convertSignalId(var.getOid().toString()),
                JsonUtils.encode(fanSignalValue));
      } else {
        log.debug("There's no corresponding signal {}", var);
      }
    }
  }

  private String getSignalRedisKey(EnvDeviceVo netgear) {
    return RedisSignalKey.getDeviceStatusKey(netgear.getDeviceType(), netgear.getId());
  }
  private String getDescRedisKey(EnvDeviceVo netgear) {
    return RedisSignalKey.getDeviceDecKey(netgear.getDeviceType(), netgear.getId());
  }

}
