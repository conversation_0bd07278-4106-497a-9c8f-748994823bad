package com.mediacomm.tsingli.controller;

import static com.mediacomm.system.base.kvm.CmdServer.NO_HOST;
import static com.mediacomm.system.base.kvm.CmdServer.PARAM_ERR;

import cn.hutool.core.net.NetUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.CommandRequestBody;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.TcpClient;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

/**
 * TsingliCmdServer.
 */
@Slf4j
@Controller
public class TsingliCmdServer {
  @Resource
  private KvmMasterService kvmMasterService;

  /**
   * executeCommand.
   */
  public String executeCommand(String msg) {
    MqRequest<CommandRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    String cmd = request.getBody().getCmd();
    if (Strings.isNullOrEmpty(cmd)) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    int port = Integer.parseInt(
        Property.findValueByKey(kvmMaster.getCollectorProperties(), "port", "80"));
    if (!NetUtil.ping(kvmMaster.getDeviceIp())) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    try (TcpClient tcpClient = new TcpClient(kvmMaster.getDeviceIp(), port)) {
      //添加结束符,发送命令
      cmd += "\n";
      tcpClient.write(cmd.getBytes(StandardCharsets.UTF_8));
      //等待结果
      byte[] bytes = new byte[1024];
      int length = tcpClient.read(bytes);
      if (length <= 0) {
        log.warn("Fail to read response for " + cmd + "!");
        return Result.failureStr("Fail to read response for " + cmd + "!",
            ResponseCode.EX_FAILURE_400);
      } else {
        String result = new String(bytes, 0, length, StandardCharsets.UTF_8);
        return Result.okStr(result);
      }
    } catch (IOException e) {
      log.error("Fail to execute command " + cmd + "!", e);
    }
    return Result.okStr();
  }

}
