package com.mediacomm.tsingli;

import com.mediacomm.entity.Result;
import com.mediacomm.system.base.kvm.KvmRunner;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.tsingli.controller.TsingliCmdServer;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * TsingliRunner.
 */
@Slf4j
@Component
public class TsingliRunner extends KvmRunner implements CommandLineRunner {

  @Resource
  private TsingliCmdServer tsingliCmdServer;

  @Override
  public void run(String... args) {
    log.info("TsingliRunner service running!");
  }

  /**
   * 指定消费者的线程数量,一个线程会打开一个Channel，
   * 一个队列上的消息只会被消费一次（不考虑消息重新入队列的情况）,下面的表示至少开启5个线程，最多10个。
   * 线程的数目需要根据你的任务来决定，如果是计算密集型，线程的数目就应该少一些.
   *
   * @param msg        负载信息
   * @param headers    头部信息
   * @param routingKey 路由信息
   */
  @RabbitListener(queues = MessageType.TSINGLI_SERVER, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    System.out.println("TSINGLI_SERVER receiver msg:" + msg);
    System.out.println("TSINGLI_SERVER receiver routingKey:" + routingKey);
    System.out.println("TSINGLI_SERVER receiver headers:" + headers);
    return switch (routingKey) {
      case RoutingKey.TSINGLI_SERVER_EXECUTE_COMMAND -> tsingliCmdServer.executeCommand(msg);
      default -> Result.failureStr("Tsingli no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
