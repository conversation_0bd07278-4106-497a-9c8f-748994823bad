package com.mediacomm.caesar.snapshot;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

import com.mediacomm.caesar.domain.avgm.TxConnectRequest;
import com.mediacomm.caesar.domain.avgm.TxConnectedStatusResponse;
import com.mediacomm.caesar.preview.avgm.AvgmDeviceDataGetter;
import com.mediacomm.caesar.preview.avgm.AvgmPreviewStrategy;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.caesar.util.CaesarTcpClient;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.exception.SkyLinkException;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.PreviewType;
import com.mediacomm.util.SkyLinkStringUtil;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;

/**
 * 整屏回显单元测试.
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AvgmPreviewStrategyTest {
  private CaesarTcpClient cli = mock(CaesarTcpClient.class);

  @Test
  public void testOneClientRequest() throws IllegalAccessException, SkyLinkException {
    Map<String, KvmAssetVo> txMap = new HashMap<>();
    TxConnectRequest request = mockTxConnectRequest(SkyLinkStringUtil.uuid(), 4, txMap);
    AvgmPreviewStrategy strategy = newInstance(2, txMap);
    strategy.resetChannel();
    Collection<TxConnectedStatusResponse> res = strategy.connectReviewAsso(request);
    for (TxConnectedStatusResponse re : res) {
      Assert.assertEquals(1, re.getState());
    }
    System.out.printf("1 client, no more connections than the limit.%n {result: %s}", res);
  }

  @Test
  public void testOneClientOutLimit() throws SkyLinkException, IllegalAccessException {
    Map<String, KvmAssetVo> txMap = new HashMap<>();
    TxConnectRequest req = mockTxConnectRequest(SkyLinkStringUtil.uuid(), 8, txMap);
    AvgmPreviewStrategy strategy = newInstance(1, txMap);
    strategy.resetChannel();
    Collection<TxConnectedStatusResponse> res = strategy.connectReviewAsso(req);
    int needUseChannelCount = 0;
    for (KvmAssetVo value : txMap.values()) {
      if (InspectCaesarUtil.getVideoResolutionType(value) != InspectCaesarUtil.VideoResolutionTypeEnum.RESOLUTION_2K) {
        needUseChannelCount += 2;
      } else {
        needUseChannelCount++;
      }
    }
    int actualUseChannelCount = res.stream().mapToInt(txCon -> txCon.getState() == 1 ? 1 : 0).sum();
    Assert.assertTrue(actualUseChannelCount <= needUseChannelCount);
    int outLimitCount = res.stream().mapToInt(txCon -> txCon.getState() != 1 ? 1 : 0).sum();
    Assert.assertEquals(8 - actualUseChannelCount, outLimitCount);
    Assert.assertEquals(txMap.size(), actualUseChannelCount + outLimitCount);
    System.out.printf("1 client, more connections than the limit.%n {result: %s}", res);
  }

  @Test
  public void testTwoClientRequestSameTx() throws SkyLinkException, IllegalAccessException {
    Map<String, KvmAssetVo> txMap = new HashMap<>();
    TxConnectRequest client1ReqMock = mockTxConnectRequest(SkyLinkStringUtil.uuid(), 8, txMap);
    TxConnectRequest client2ReqMock = new TxConnectRequest();
    client2ReqMock.setClientId(SkyLinkStringUtil.uuid());
    client2ReqMock.setTxIds(txMap.keySet().stream().limit(8).collect(Collectors.toSet()));
    AvgmPreviewStrategy strategy = newInstance(1, txMap);
    strategy.resetChannel();
    Collection<TxConnectedStatusResponse> client1Res = strategy.connectReviewAsso(client1ReqMock);
    Collection<TxConnectedStatusResponse> client2Res = strategy.connectReviewAsso(client2ReqMock);
    for (TxConnectedStatusResponse client1Re : client1Res) {
      for (TxConnectedStatusResponse client2Re : client2Res) {
        if (client1Re.getTxId().equals(client2Re.getTxId())) {
          Assert.assertEquals(client1Re.getState(), client2Re.getState());
          Assert.assertEquals(client1Re.getUrl(), client2Re.getUrl());
        }
      }
    }
  }

  @Test
  public void testClientOutTime() throws SkyLinkException, IllegalAccessException, InterruptedException {
    Map<String, KvmAssetVo> txMap = new HashMap<>();
    TxConnectRequest req = mockTxConnectRequest(SkyLinkStringUtil.uuid(), 99, txMap);
    AvgmPreviewStrategy strategy = newInstance(1, txMap);
    strategy.resetChannel();
    strategy.connectReviewAsso(req);
    Thread.sleep(16000);
    TxConnectRequest req2 = mockTxConnectRequest(SkyLinkStringUtil.uuid(), 4, txMap);
    Collection<TxConnectedStatusResponse> res2 = strategy.connectReviewAsso(req2);
    for (TxConnectedStatusResponse re : res2) {
      Assert.assertEquals(1, re.getState());
    }
  }

  private AvgmPreviewStrategy newInstance(int previewVideoWallCount, Map<String, KvmAssetVo> txMap)
          throws SkyLinkException, IllegalAccessException {
    doNothing().when(cli).openPanel(Mockito.any(), Mockito.any());
    KvmMaster master = new KvmMaster();
    master.setDeviceIp("127.0.0.1");
    master.setMasterId(SkyLinkStringUtil.uuid());
    AvgmDeviceDataGetter deviceDataGetter = new AvgmDeviceDataGetterMock(mock(CaesarDbServiceUtil.class),
            new PreviewTestHelper.TaskRunnerMock(), master.getMasterId(),
            getPreviewVideoWall(previewVideoWallCount), txMap);
    AvgmPreviewStrategy strategy = new AvgmPreviewStrategy(deviceDataGetter, master);
    FieldUtil.setPropertyValue(strategy, "cli", cli);
    return strategy;
  }

  private Collection<KvmVideoWall> getPreviewVideoWall(int count) {
    Collection<KvmVideoWall> walls = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      KvmVideoWall videoWall = new KvmVideoWall();
      videoWall.setWallId(i);
      videoWall.setDecoders(List.of(new KvmVideoWallDecoder(), new KvmVideoWallDecoder(), new KvmVideoWallDecoder(),
              new KvmVideoWallDecoder(), new KvmVideoWallDecoder(), new KvmVideoWallDecoder(),
              new KvmVideoWallDecoder(), new KvmVideoWallDecoder()));
      videoWall.getCollectorProperties().add(new Property(PropertyKeyConst.PREVIEW_ADDRESS, "mock.wall" + i));
      videoWall.getCollectorProperties().add(new Property(PropertyKeyConst.PREVIEW_MODEL, "11"));
      videoWall.getCollectorProperties().add(new Property(PropertyKeyConst.PREVIEW_TYPE, "RTSP"));
      walls.add(videoWall);
    }
    return walls;
  }

  private TxConnectRequest mockTxConnectRequest(String clientId, int count, Map<String, KvmAssetVo> txMap) {
    TxConnectRequest request = new TxConnectRequest();
    request.setClientId(clientId);
    for (int i = 0; i < count; i++) {
      String txId = SkyLinkStringUtil.uuid();
      request.getTxIds().add(txId);
      KvmAssetVo tx = new KvmAssetVo();
      tx.setAssetId(txId);
      tx.setDeviceId(i);
      tx.getProperties().add(new Property(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY, String.valueOf(i % 2)));
      txMap.put(tx.getAssetId(), tx);
    }
    return request;
  }

  static class AvgmDeviceDataGetterMock extends AvgmDeviceDataGetter {
    private Map<Integer, KvmVideoWall> previewWalls;
    private Collection<KvmVideoWall> previewOnlineWalls;
    private Map<Integer, Collection<KvmPreviewAsso>> kvmPreviewAssos = new HashMap<>();
    private Map<String, KvmAssetVo> txMap;


    public AvgmDeviceDataGetterMock(CaesarDbServiceUtil cmdServer,
                                    SkyLinkTaskPool taskPool, String masterId,
                                    Collection<KvmVideoWall> walls, Map<String, KvmAssetVo> txMap) {
      super(cmdServer, taskPool, masterId);
      previewOnlineWalls = walls;
      previewWalls = previewOnlineWalls.stream().collect(
              Collectors.toMap(KvmVideoWall::getWallId, wall -> wall));
      previewOnlineWalls.forEach(wall -> kvmPreviewAssos.put(wall.getWallId(),
              InspectCaesarUtil.buildPreviewChannelsByType(wall, PreviewType.RTSP)));
      this.txMap = txMap;
    }

    @Override
    public KvmAssetVo getKvmAsset(String txId) {
      return txMap.get(txId);
    }

    @Override
    public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels() {
      return kvmPreviewAssos;
    }

    @Override
    public Map<Integer, KvmVideoWall> getKvmPreviewWalls() {
      return previewWalls;
    }

    @Override
    public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k) {
      KvmPreviewAsso previewAsso = null;
      for (KvmVideoWall previewOnlineWall : previewOnlineWalls) {
        Collection<KvmPreviewAsso> assos = kvmPreviewAssos.get(previewOnlineWall.getWallId());
        int count = 0;
        for (KvmPreviewAsso asso : assos) {
          if (asso.isUsed() && asso.isHighResolution()) {
            count += 2;
            continue;
          } else if (asso.isUsed()) {
            count++;
            continue;
          }
          previewAsso = asso;
        }
        int onlineChannels = previewOnlineWall.getDecoders().size();
        int needCnt = is4k ? 2 : 1;
        if (onlineChannels - count >= needCnt) {
          return previewAsso;
        }
      }
      return null;
    }
  }

}
