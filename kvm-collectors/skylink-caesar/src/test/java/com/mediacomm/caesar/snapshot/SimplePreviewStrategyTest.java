package com.mediacomm.caesar.snapshot;

import com.google.common.collect.Sets;
import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewMap;
import com.mediacomm.caesar.preview.strategy.impl.SimplePreviewStrategy;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.system.variable.sysenum.DeviceType;
import org.junit.Assert;
import org.junit.Test;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public class SimplePreviewStrategyTest {

  static class DeviceDataGetterMock implements DeviceDataGetter {

    public DeviceDataGetterMock(int channelCnt) {
      int index = 0;
      while (channelCnt > 0) {
        Integer videoWallId = index;
        int singleCnt = Math.min(channelCnt, CaesarConstants.PREVIEW_CHANNEL_LIMIT);
        Set<KvmPreviewAsso> channels = new HashSet<>();
        for (int i = 0; i < singleCnt; i++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          asso.setWallId(videoWallId);
          asso.setSeq(i);
          channels.add(asso);
        }
        this.channels.put(videoWallId, channels);
        channelCnt -= singleCnt;
        index++;
      }
    }

    @Override
    public boolean is4kTx(String txId) {
      return false;
    }

    @Override
    public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k,
        boolean ignoreIncompleteVideoWall) {
      Optional<Collection<KvmPreviewAsso>> allChannels = channels.entrySet().stream()
          .map((item) -> item.getValue()).reduce((lhs, rhs) -> {
            Set<KvmPreviewAsso> set = new HashSet<>();
            set.addAll(lhs);
            set.addAll(rhs);
            return set;
          });
      for (KvmPreviewAsso channel : allChannels.get()) {
        if (!channel.isUsed()) {
          return channel;
        }
      }
      return null;
    }

    @Override
    public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(
        boolean ignoreIncompleteVideoWall) {
      return channels;
    }

    @Override
    public KvmVideoWall getKvmPreviewAssoWallById(Integer videoWallId) {
      return null;
    }

    @Override
    public Collection<KvmPreviewAsso> getPreviewChannels(Integer videoWallId) {
      if (channels.containsKey(videoWallId)) {
        return channels.get(videoWallId);
      } else {
        return Collections.emptyList();
      }
    }

    @Override
    public KvmAsset getExtendDevice(String id) {
      return null;
    }

    @Override
    public String getMasterIp() {
      return null;
    }

    @Override
    public String makeTxSavePath(String hardCode) {
      return null;
    }

    @Override
    public void addPreviewChannel(Integer previewVideoWallId) {

    }

    @Override
    public void updatePreviewChannel(Integer previewVideoWallId) {

    }

    @Override
    public void updatePreviewChannel(String assetId, int availableNum) {

    }

    @Override
    public void delPreviewChannel(Integer previewVideoWallId) {

    }

    Map<Integer, Collection<KvmPreviewAsso>> channels = new HashMap<>();

    @Override
    public Map<String, Collection<KvmPreviewAsso>> getAllPreviewChannels(DeviceType previewDeviceType) {
      return new HashMap<>();
    }

    @Override
    public Collection<KvmPreviewAsso> getPreviewChannels(String assetId) {
      return new ArrayList<>();
    }
  }

  /**
   * 创建请求信息. TX ID都是数字
   *
   * @param offset TX ID的起始值
   * @param cnt    TX数量
   * @return 请求
   */
  public static ObjectIds makeRequest(boolean important, int offset, int cnt) {
    ObjectIds request = new ObjectIds();
    request.setImportant(important);
    request.setRequestKey(UUID.randomUUID().toString());
    List<String> ids = new ArrayList<>();
    for (int i = offset; i < offset + cnt; i++) {
      ids.add(String.valueOf(i));
    }
    request.setIds(ids);
    return request;
  }

  public static ObjectIds makeRequest(boolean important, Collection<String> ids) {
    ObjectIds request = new ObjectIds();
    request.setImportant(important);
    request.setRequestKey(UUID.randomUUID().toString());
    request.setIds(new ArrayList<>(ids));
    return request;
  }

  @Test
  public void testSimpleAddPreviewImportant() throws IllegalAccessException {
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request = makeRequest(true, 0, cnt);
    strategy.addPreview(request);

    assert previewMap != null;
    Assert.assertEquals(previewMap.getRealTimePreviewTxes(), new HashSet<>(request.getIds()));
    Assert.assertTrue(previewMap.getPollingPreviewChannels().isEmpty());
    for (int i = 0; i < cnt; i++) {
      String txId = request.getIds().get(i);
      Assert.assertTrue(previewMap.isRealTimePreviewing(txId));
      Assert.assertFalse(previewMap.isPollingPreviewing(txId));
      Assert.assertTrue(previewMap.isPreviewing(txId));
      Assert.assertTrue(previewMap.isImportant(txId));
      PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(txId);
      Assert.assertNotNull(info.channel);
      Assert.assertTrue(info.channel.isUsed());
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY, info.status);
      PreviewInfoGetter.PollingPreviewStatus pollingStatus = previewMap.getPollingPreviewStatus(txId);
      Assert.assertNull(pollingStatus);
      // 上次使用时间
      long time = previewMap.getLastPreviewUsedTime(txId);
      Assert.assertTrue(System.currentTimeMillis() - time < 1000);
    }
  }


  @Test
  public void testSimpleAddPreviewImportantNotEnoughChannel() throws IllegalAccessException {
    // 请求预览，但不够通道，部分需要轮询
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request = makeRequest(true, 0, cnt + 4);
    strategy.addPreview(request);

    Collection<String> realTimeTxes = previewMap.getRealTimePreviewTxes();
    Collection<String> pollingTxes = previewMap.getPollingPreviewTxes();
    Assert.assertEquals(cnt, realTimeTxes.size());
    Assert.assertEquals(request.getIds().size() - cnt, pollingTxes.size());

    Set<String> previewingTxes = new HashSet<>(realTimeTxes);
    previewingTxes.addAll(pollingTxes);
    Assert.assertEquals(new HashSet<>(request.getIds()), previewingTxes);
    for (String txId : realTimeTxes) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(txId));
      Assert.assertFalse(previewMap.isPollingPreviewing(txId));
      Assert.assertTrue(previewMap.isPreviewing(txId));
      Assert.assertTrue(previewMap.isImportant(txId));
      PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(txId);
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY, info.status);
      PreviewInfoGetter.PollingPreviewStatus pollingStatus = previewMap.getPollingPreviewStatus(txId);
      Assert.assertNull(pollingStatus);
      // 上次使用时间
      long time = previewMap.getLastPreviewUsedTime(txId);
      Assert.assertTrue(System.currentTimeMillis() - time < 1000);
    }
    for (String txId : pollingTxes) {
      Assert.assertFalse(previewMap.isRealTimePreviewing(txId));
      Assert.assertTrue(previewMap.isPollingPreviewing(txId));
      Assert.assertTrue(previewMap.isPreviewing(txId));
      Assert.assertTrue(previewMap.isImportant(txId));
      PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(txId);
      Assert.assertNull(info);
      PreviewInfoGetter.PollingPreviewStatus pollingStatus = previewMap.getPollingPreviewStatus(txId);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, pollingStatus);
      // 上次使用时间
      long time = previewMap.getLastPreviewUsedTime(txId);
      Assert.assertTrue(System.currentTimeMillis() - time < 1000);
    }
  }

  @Test
  public void testAddPreviewWithLessTx() throws IllegalAccessException {
    // TX不被请求时，删除预览
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request = makeRequest(true, 0, cnt);
    strategy.addPreview(request);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());

    String removedTx = request.getIds().get(0);
    request.getIds().remove(0);
    strategy.addPreview(request);
    // 被删除预览
    Assert.assertEquals(cnt - 1, previewMap.getRealTimePreviewTxes().size());
    Assert.assertFalse(previewMap.isPreviewing(removedTx));
    Assert.assertEquals(0, previewMap.getLastPreviewUsedTime(removedTx));
  }

  @Test
  public void testAddPreviewReplaceWithImportantRequest() throws IllegalAccessException {
    // 重要预览替代不重要预览
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request1 = makeRequest(false, 0, cnt);
    strategy.addPreview(request1);
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());

    ObjectIds request2 = makeRequest(true, cnt, cnt + 1); // 预览不够，其中一个要轮询
    strategy.addPreview(request2);
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());
    Assert.assertEquals(cnt + 1, previewMap.getPollingPreviewTxes().size());
    Assert.assertTrue(
        new HashSet<>(request2.getIds()).containsAll(previewMap.getRealTimePreviewTxes()));
    Assert.assertTrue(
        new HashSet<>(previewMap.getPollingPreviewTxes()).containsAll(request1.getIds()));
  }

  @Test
  public void testAddPreviewImportantPollingRequest() throws IllegalAccessException {
    // 当前在轮询预览，当变成重要预览时，升级为实时预览
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    int pollingCnt = 4;
    ObjectIds request1 = makeRequest(false, 0, cnt + pollingCnt);
    strategy.addPreview(request1);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());
    Assert.assertEquals(pollingCnt, previewMap.getPollingPreviewTxes().size());

    ObjectIds request2 = makeRequest(true, previewMap.getPollingPreviewTxes());
    strategy.addPreview(request2);
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());
    Assert.assertEquals(pollingCnt, previewMap.getPollingPreviewTxes().size());
    // 变为实时轮询
    for (String txId : request2.getIds()) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(txId));
    }
  }

  @Test
  public void testAddPreviewImportantTimeout() throws InterruptedException, IllegalAccessException {
    // important timeout
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request1 = makeRequest(true, 0, cnt);
    strategy.addPreview(request1);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());

    ObjectIds request2 = makeRequest(true, cnt, cnt);
    strategy.addPreview(request2);
    Assert.assertEquals(new HashSet<>(request1.getIds()), previewMap.getRealTimePreviewTxes());
    Assert.assertEquals(new HashSet<>(request2.getIds()), previewMap.getPollingPreviewTxes());

    Thread.currentThread().sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI + 100);
    strategy.addPreview(request2);

    for (String tx : request2.getIds()) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(tx));
      PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(tx);
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY, info.status);
    }
    // 检查超时的预览的状态
    for (String tx : request1.getIds()) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(tx));
      PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(tx);
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_STOPPED, info.status);
    }

    // 重新申请，恢复预览，实时预览通道不够，会转为轮询预览
    strategy.addPreview(request1);
    for (String tx : request1.getIds()) {
      Assert.assertTrue(previewMap.isPollingPreviewing(tx));
      PreviewInfoGetter.PollingPreviewStatus status = previewMap.getPollingPreviewStatus(tx);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, status);
    }
  }

  @Test
  public void testAddPreviewUnimportantTimeout() throws InterruptedException, IllegalAccessException {
    // unimportant timeout
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request1 = makeRequest(false, 0, cnt);
    strategy.addPreview(request1);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());

    ObjectIds request2 = makeRequest(false, cnt, cnt);
    strategy.addPreview(request2);
    Assert.assertEquals(new HashSet<>(request1.getIds()), previewMap.getRealTimePreviewTxes());
    Assert.assertEquals(new HashSet<>(request2.getIds()), previewMap.getPollingPreviewTxes());
    // 轮询超时
    Thread.currentThread().sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI + 100);
    ObjectIds request3 = makeRequest(false, cnt * 2, cnt);
    strategy.addPreview(request1);
    strategy.addPreview(request3);

    Assert.assertEquals(new HashSet<>(request1.getIds()), previewMap.getRealTimePreviewTxes());
    // 检查超时的预览的状态
    for (String tx : request2.getIds()) {
      Assert.assertTrue(previewMap.isPollingPreviewing(tx));
      PreviewInfoGetter.PollingPreviewStatus info = previewMap.getPollingPreviewStatus(tx);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING_STOPPED, info);
    }

    for (String tx : request3.getIds()) {
      Assert.assertTrue(previewMap.isPollingPreviewing(tx));
      PreviewInfoGetter.PollingPreviewStatus info = previewMap.getPollingPreviewStatus(tx);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, info);
    }

    // 重新申请，恢复预览
    strategy.addPreview(request2);
    for (String tx : request2.getIds()) {
      Assert.assertTrue(previewMap.isPollingPreviewing(tx));
      PreviewInfoGetter.PollingPreviewStatus info = previewMap.getPollingPreviewStatus(tx);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, info);
    }
  }

  @Test
  public void testOnTxOnlineOffline() throws IllegalAccessException {
    //
    int cnt = 10;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request1 = makeRequest(true, 0, cnt);
    strategy.addPreview(request1);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());

    ObjectIds request2 = makeRequest(true, cnt, cnt);
    strategy.addPreview(request2);
    Assert.assertEquals(new HashSet<>(request1.getIds()), previewMap.getRealTimePreviewTxes());
    Assert.assertEquals(new HashSet<>(request2.getIds()), previewMap.getPollingPreviewTxes());

    // realtime preview tx offline
    String realtimePreviewTx = request1.getIds().get(0);
    strategy.onTxOffline(realtimePreviewTx);
    Assert.assertTrue(previewMap.isRealTimePreviewing(realtimePreviewTx));
    PreviewInfoGetter.RealTimePreviewInfo info = previewMap.getRealTimePreviewInfo(realtimePreviewTx);
    Assert.assertNull(info.channel);
    Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_STOPPED, info.status);
    // polling preview tx offline
    String pollingPreviewTx = request2.getIds().get(0);
    strategy.onTxOffline(pollingPreviewTx);
    Assert.assertTrue(previewMap.isPollingPreviewing(pollingPreviewTx));
    PreviewInfoGetter.PollingPreviewStatus status = previewMap.getPollingPreviewStatus(pollingPreviewTx);
    Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING_STOPPED, status);
    // unknown tx offline, nothing happen
    strategy.onTxOffline("unknown tx");

    // realtime preview tx online
    strategy.onTxOnline(realtimePreviewTx);
    Assert.assertTrue(previewMap.isRealTimePreviewing(realtimePreviewTx));
    info = previewMap.getRealTimePreviewInfo(realtimePreviewTx);
    Assert.assertNotNull(info.channel);
    Assert.assertTrue(info.channel.isUsed());
    Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY, info.status);
    // polling preview tx online
    strategy.onTxOnline(pollingPreviewTx);
    Assert.assertTrue(previewMap.isPollingPreviewing(pollingPreviewTx));
    status = previewMap.getPollingPreviewStatus(pollingPreviewTx);
    Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, status);
    // unknown tx online, nothing happen
    strategy.onTxOnline("unknown tx");
  }

  @Test
  public void testOnGetSnapshot() throws InterruptedException, IllegalAccessException {
    int cnt = 2;
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");

    ObjectIds request1 = makeRequest(true, 0, cnt);
    strategy.addPreview(request1);
    assert previewMap != null;
    Assert.assertEquals(cnt, previewMap.getRealTimePreviewTxes().size());
    Thread.currentThread().sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI + 100);

    String updatedTx = request1.getIds().get(0);
    String notUpdatedTx = request1.getIds().get(1);
    strategy.onGetSnapshot(updatedTx);
    ObjectIds request2 = makeRequest(true, cnt, cnt);
    strategy.addPreview(request2);

    // 其中一个超时
    Assert.assertTrue(previewMap.isRealTimePreviewing(updatedTx));
    Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY,
        previewMap.getRealTimePreviewInfo(updatedTx).status);
    Assert.assertTrue(previewMap.isRealTimePreviewing(notUpdatedTx));
    Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_STOPPED,
        previewMap.getRealTimePreviewInfo(notUpdatedTx).status);

    // 重新使用，变成轮询
    strategy.onGetSnapshot(notUpdatedTx);
    Assert.assertTrue(previewMap.isPollingPreviewing(notUpdatedTx));
    Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING,
        previewMap.getPollingPreviewStatus(notUpdatedTx));
  }

  @Test
  public void testResetChannelsWithoutTx() throws IllegalAccessException {
    // 充足的通道，1/4作为轮询
    {
      int cnt = 20;
      SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
      PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
      strategy.resetChannels();
      assert previewMap != null;
      Collection<KvmPreviewAsso> channels = previewMap.getPollingPreviewChannels();
      Assert.assertEquals((int) (cnt * SimplePreviewStrategy.POLLING_CHANNEL_PERCENTAGE),
          channels.size());
      for (KvmPreviewAsso item : channels) {
        Assert.assertTrue(item.isUsed());
        Assert.assertFalse(item.isHighResolution());
      }
    }
    // 不够8个，多于2个，用2个作为轮询
    {
      int cnt = 6;
      SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
      PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
      strategy.resetChannels();
      assert previewMap != null;
      Collection<KvmPreviewAsso> channels = previewMap.getPollingPreviewChannels();
      Assert.assertEquals(2, channels.size());
      for (KvmPreviewAsso item : channels) {
        Assert.assertTrue(item.isUsed());
        Assert.assertFalse(item.isHighResolution());
      }
    }
    // 只有1个通道，只能全部作为轮询
    {
      int cnt = 1;
      SimplePreviewStrategy strategy = new SimplePreviewStrategy(new DeviceDataGetterMock(cnt));
      PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
      strategy.resetChannels();
      assert previewMap != null;
      Collection<KvmPreviewAsso> channels = previewMap.getPollingPreviewChannels();
      Assert.assertEquals(1, channels.size());
      for (KvmPreviewAsso item : channels) {
        Assert.assertTrue(item.isUsed());
        Assert.assertFalse(item.isHighResolution());
      }
    }
  }

  @Test
  public void testResetChannelsWithLessChannel() throws IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();

    ObjectIds request = makeRequest(true, 0, cnt);
    strategy.addPreview(request);
    // 2个轮询预览通道
    assert previewMap != null;
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    // 6个实时预览
    Assert.assertEquals(6, previewMap.getRealTimePreviewTxes().size());
    // 2个轮询预览
    Assert.assertEquals(2, previewMap.getPollingPreviewTxes().size());

    // 减少通道
    dataGetter.setInner(new DeviceDataGetterMock(cnt - 2));
    strategy.resetChannels();
    // 2个轮询预览通道
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    // 4个实时预览
    Assert.assertEquals(4, previewMap.getRealTimePreviewTxes().size());
    // 4个轮询预览
    Assert.assertEquals(4, previewMap.getPollingPreviewTxes().size());
  }

  @Test
  public void testResetChannelsWithMoreChannel() throws IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();

    ObjectIds request = makeRequest(true, 0, cnt);
    strategy.addPreview(request);
    // 2个轮询预览通道
    assert previewMap != null;
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    // 6个实时预览
    Assert.assertEquals(6, previewMap.getRealTimePreviewTxes().size());
    // 2个轮询预览
    Assert.assertEquals(2, previewMap.getPollingPreviewTxes().size());

    // 增加通道
    dataGetter.setInner(new DeviceDataGetterMock(cnt + 2));
    strategy.resetChannels();
    // 2个轮询预览通道
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    // 8个实时预览
    Assert.assertEquals(8, previewMap.getRealTimePreviewTxes().size());
    // 0个轮询预览
    Assert.assertEquals(0, previewMap.getPollingPreviewTxes().size());
  }

  @Test
  public void testRefreshCheckTimeout() throws InterruptedException, IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();
    // 2个轮询预览通道
    assert previewMap != null;
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    ObjectIds request = makeRequest(true, 0, 6);
    strategy.addPreview(request);
    // 6个实时预览
    Assert.assertEquals(6, previewMap.getRealTimePreviewTxes().size());
    //
    Thread.sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2);
    ObjectIds request2 = makeRequest(true, 6, 6);
    strategy.addPreview(request2);
    // 6个轮询预览
    Assert.assertEquals(Sets.newHashSet(request2.getIds()), previewMap.getPollingPreviewTxes());
    // 第一次请求超时
    Thread.sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2 + 100);
    strategy.refresh(previewMap);
    // 第一次请求全部变为停止状态，第二次请求全部变为实时预览
    for (String tx : request.getIds()) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(tx));
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_STOPPED,
          previewMap.getRealTimePreviewInfo(tx).status);
    }
    for (String tx : request2.getIds()) {
      Assert.assertTrue(previewMap.isRealTimePreviewing(tx));
      Assert.assertEquals(PreviewInfoGetter.RealTimePreviewStatus.REAL_TIME_READY,
          previewMap.getRealTimePreviewInfo(tx).status);
    }
  }

  @Test
  public void testRefreshPromoteImportantPollingPreview() throws IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();
    // 2个轮询预览通道
    assert previewMap != null;
    Assert.assertEquals(2, previewMap.getPollingPreviewChannels().size());
    // 6个实时预览
    ObjectIds request = makeRequest(true, 0, 6);
    strategy.addPreview(request);
    // 6个轮询预览
    ObjectIds request2 = makeRequest(true, 6, 6);
    strategy.addPreview(request2);
    Assert.assertEquals(Sets.newHashSet(request2.getIds()), previewMap.getPollingPreviewTxes());
    // 清空实时预览
    request.getIds().clear();
    strategy.addPreview(request);
    Assert.assertEquals(0, previewMap.getRealTimePreviewTxes().size());
    // 6个不重要的实时预览
    ObjectIds request3 = makeRequest(false, 12, 6);
    strategy.addPreview(request3);
    Assert.assertEquals(Sets.newHashSet(request3.getIds()), previewMap.getRealTimePreviewTxes());
    // 提升重要轮询预览
    strategy.refresh(previewMap);
    Assert.assertEquals(Sets.newHashSet(request2.getIds()), previewMap.getRealTimePreviewTxes());
    Assert.assertEquals(Sets.newHashSet(request3.getIds()), previewMap.getPollingPreviewTxes());
  }

  @Test
  public void testRefreshUnbalancedPreview() throws IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();
    int pollingChannelCnt = 2;
    int realtimePreviewTxCnt = 6;
    int pollingPreviewTxCnt = 14;
    // 2个轮询预览通道
    assert previewMap != null;
    Assert.assertEquals(pollingChannelCnt, previewMap.getPollingPreviewChannels().size());
    // 6个实时预览
    ObjectIds request = makeRequest(true, 0, realtimePreviewTxCnt);
    strategy.addPreview(request);
    // 14个轮询预览
    ObjectIds request2 = makeRequest(false, realtimePreviewTxCnt, pollingPreviewTxCnt);
    strategy.addPreview(request2);
    Assert.assertEquals(Sets.newHashSet(request2.getIds()), previewMap.getPollingPreviewTxes());
    // 关闭实时预览
    request.getIds().clear();
    strategy.addPreview(request);
    Assert.assertEquals(0, previewMap.getRealTimePreviewTxes().size());
    // 平衡轮询资源
    strategy.refresh(previewMap);
    Assert.assertEquals(SimplePreviewStrategy.MAX_POLLING_ITERATION * 2,
        previewMap.getPollingPreviewTxes().size());
    Assert.assertEquals(pollingPreviewTxCnt - SimplePreviewStrategy.MAX_POLLING_ITERATION * 2,
        previewMap.getRealTimePreviewTxes().size());
  }

  @Test
  public void testRefreshRequestTimeout() throws InterruptedException, IllegalAccessException {
    int cnt = 8;
    DeviceDataGetterDecorator dataGetter = new DeviceDataGetterDecorator(
        new DeviceDataGetterMock(cnt));
    SimplePreviewStrategy strategy = new SimplePreviewStrategy(dataGetter);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.resetChannels();
    // 重要预览
    assert previewMap != null;
    ObjectIds request1 = makeRequest(true, 0, cnt - previewMap.getPollingPreviewChannels().size());
    strategy.addPreview(request1);
    // 重要预览,只能轮询
    ObjectIds request2 = makeRequest(true, request1.getIds().size(), request1.getIds().size());
    strategy.addPreview(request2);
    // 非重要预览，跟request1公用tx
    ObjectIds request3 = makeRequest(false, 0, request1.getIds().size());
    strategy.addPreview(request3);
    strategy.refresh(previewMap);
    Assert.assertEquals(new HashSet<>(request1.getIds()),
        new HashSet<>(previewMap.getRealTimePreviewTxes()));
    Assert.assertEquals(new HashSet<>(request2.getIds()),
        new HashSet<>(previewMap.getPollingPreviewTxes()));
    Assert.assertEquals(new HashSet<>(request1.getIds()),
        new HashSet<>(previewMap.getRealTimePreviewTxes()));
    // 等待request1超时
    int time = 0;
    while (time <= SimplePreviewStrategy.REQUEST_KEY_TIME_OUT_MILLI + 100) {
      Thread.sleep(SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2);
      time += SimplePreviewStrategy.PREVIEW_TIME_OUT_MILLI / 2;
      strategy.addPreview(request2);
      strategy.addPreview(request3);
      strategy.refresh(previewMap);
    }
    // 检查request1被删除
    Assert.assertFalse(
        previewMap.getAllRequestKeyLastUsedTime().containsKey(request1.getRequestKey()));
    // request2的TX变为实时预览
    Assert.assertEquals(new HashSet<>(request2.getIds()),
        new HashSet<>(previewMap.getRealTimePreviewTxes()));
    Assert.assertEquals(new HashSet<>(request3.getIds()),
        new HashSet<>(previewMap.getPollingPreviewTxes()));
  }
}



