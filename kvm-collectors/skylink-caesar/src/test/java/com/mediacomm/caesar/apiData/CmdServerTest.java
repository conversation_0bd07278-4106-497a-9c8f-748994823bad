package com.mediacomm.caesar.apiData;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mediacomm.caesar.controller.CaesarCmdServer;
import com.mediacomm.caesar.controller.CaesarFeignClientApi;
import com.mediacomm.caesar.domain.CaesarMqRequest;
import com.mediacomm.caesar.domain.CaesarSeatPanelRect;
import com.mediacomm.caesar.snapshot.FieldUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxRequestBody;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import java.net.URI;
import java.util.Collections;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

// 测试类示例
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class CmdServerTest {

  private KvmMasterService kvmMasterService = mock(KvmMasterService.class);
  private KvmAssetService kvmAssetService = mock(KvmAssetService.class);
  private CaesarFeignClientApi cli = mock(CaesarFeignClientApi.class);
  private CaesarCmdServer cmdServer = new CaesarCmdServer();

  @Before
  public void init() throws IllegalAccessException {
    FieldUtil.setPropertyValue(cmdServer, "kvmMasterService", kvmMasterService);
    FieldUtil.setPropertyValue(cmdServer, "kvmAssetService", kvmAssetService);
    FieldUtil.setPropertyValue(cmdServer, "cli", cli);
  }

  @Test
  public void testSeatOpenTxMasterNotFound() {
    // 构造正常请求但主机不存在
    CaesarMqRequest<SeatOpenTxRequestBody> request = new CaesarMqRequest<>();
    request.setMasterId("master123");
    String validMsg = JsonUtils.encode(request);

    when(kvmMasterService.getById("master123")).thenReturn(null);

    String result = cmdServer.seatOpenTx(validMsg);

    assertTrue(result.contains(CmdServer.NO_HOST));
  }

  @Test
  public void testSeatOpenTxTxNotOnHost() {
    // 构造正常请求但Tx不在当前主机
    KvmMaster master = new KvmMaster();
    master.setMasterId("master123");
    KvmAsset tx = new KvmAsset();
    tx.setMasterId("otherMaster");
    SeatOpenTxRequestBody body = new SeatOpenTxRequestBody();
    body.setVideoSrcId("tx123");
    CaesarMqRequest<SeatOpenTxRequestBody> request = new CaesarMqRequest<>();
    request.setMasterId("master123");
    request.setBody(body);
    String validMsg = JsonUtils.encode(request);

    when(kvmMasterService.getById("master123")).thenReturn(master);
    when(kvmAssetService.getById("tx123")).thenReturn(tx);

    String result = cmdServer.seatOpenTx(validMsg);

    assertTrue(result.contains(CmdServer.NO_VIDEO_SRC));
  }

  @Test
  public void seatOpenTxRxNotFound() {
    // 构造正常请求但解码器不存在
    KvmMaster master = new KvmMaster();
    master.setMasterId("master123");
    KvmAsset tx = new KvmAsset();
    tx.setMasterId("master123");
    SeatOpenTxRequestBody body = new SeatOpenTxRequestBody();
    body.setVideoSrcId("tx123");
    body.setDecoderId(2001);
    CaesarMqRequest<SeatOpenTxRequestBody> request = new CaesarMqRequest<>();
    request.setMasterId("master123");
    request.setBody(body);
    String validMsg = JsonUtils.encode(request);

    when(kvmMasterService.getById("master123")).thenReturn(master);
    when(kvmAssetService.getById("tx123")).thenReturn(tx);
    when(kvmAssetService.oneByDeviceId(anyInt(), eq("master123"))).thenReturn(null);

    String result = cmdServer.seatOpenTx(validMsg);

    assertTrue(result.contains("No such decoder"));
  }

  @Test
  public void seatOpenTxToFourRx() {
    // 构造正常请求
    KvmMaster master = new KvmMaster();
    master.setMasterId("master123");
    master.setDeviceIp("*************");
    master.setCollectorProperties(Collections.emptyList());
    KvmAsset tx = new KvmAsset();
    tx.setAssetId("tx123");
    tx.setDeviceId(1001);
    tx.setMasterId("master123");
    tx.setProperties(Collections.singletonList(new Property(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY, "2")));
    // 4k60 tx开到四画面rx
    KvmAssetVo rx = new KvmAssetVo();
    rx.setDeviceId(2001);
    rx.setDeviceModel(DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId());
    SeatOpenTxRequestBody body = new SeatOpenTxRequestBody();
    body.setCtrlMode(2);
    body.setPanelId(2);
    body.setChannelId(2);
    body.setVideoSrcId("tx123");
    body.setDecoderId(rx.getDeviceId());
    CaesarMqRequest<SeatOpenTxRequestBody> request = new CaesarMqRequest<>();
    request.setBody(body);
    request.setMasterId("master123");
    String validMsg = JsonUtils.encode(request);
    when(kvmMasterService.getById("master123")).thenReturn(master);
    when(kvmAssetService.getById("tx123")).thenReturn(tx);
    when(kvmAssetService.oneByDeviceId(2001, "master123")).thenReturn(rx);
    String res1 = cmdServer.seatOpenTx(validMsg);
    verify(cli).openFourScreenRxPanel(any(URI.class), anyInt(), any(CaesarSeatPanelRect.class));
    assertTrue(res1.contains("200"));
    // 4k60 tx开到不符合分辨率的rx
    rx.setDeviceModel(DeviceType.CAESAR_RX.getDeviceTypeId());
    rx.setProperties(Collections.singletonList(new Property(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY, "1")));
    String res2 = cmdServer.seatOpenTx(validMsg);
    assertTrue(res2.contains("13002"));
  }
}
