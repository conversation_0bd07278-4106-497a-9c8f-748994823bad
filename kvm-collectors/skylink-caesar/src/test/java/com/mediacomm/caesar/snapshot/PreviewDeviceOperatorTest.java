package com.mediacomm.caesar.snapshot;

import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static com.mediacomm.system.variable.sysenum.PreviewType.SNAPSHOT;

public class PreviewDeviceOperatorTest {

  private PreviewDeviceOperator previewDeviceOperator;
  private DeviceDataGetterDecorator deviceDataGetter;


  class DeviceDataGetterMock implements DeviceDataGetter {

    public Map<String, KvmAsset> tx2kes = new ConcurrentHashMap<>();
    public Map<String, KvmAsset> tx4kes = new ConcurrentHashMap<>();
    public Map<Integer, KvmVideoWall> videoWalls = new ConcurrentHashMap<>();
    public Map<Integer, Collection<KvmPreviewAsso>> channels = new ConcurrentHashMap<>();

    public DeviceDataGetterMock(int channelCnt, int tx2kCnt) {
      createChannels(channelCnt);
      for (int i = 0; i < tx2kCnt; i++) {
        int intId = 1001 + i;
        String id = "skylink." + intId;
        KvmAsset tx = new KvmAsset();
        tx.setDeviceId(intId);
        tx2kes.put(id, tx);
      }
    }

    public DeviceDataGetterMock(int channelCnt, int tx2kCnt, int tx4kCnt) {
      createChannels(channelCnt);
      int intId = 1001;
      for (int i = 0; i < tx2kCnt; i++) {
        final int currentId = intId++;
        String id = "skylink." + currentId;
        KvmAsset tx = new KvmAsset();
        tx.setDeviceId(currentId);
        tx2kes.put(id, tx);
      }
      for (int i = 0; i < tx4kCnt; i++) {
        final int currentId = intId++;
        String id = "skylink." + currentId;
        KvmAsset tx = new KvmAsset();
        tx.setDeviceId(currentId);
        tx4kes.put(id, tx);
      }
    }

    private void createChannels(int channelCnt) {
      int index = 0;
      while (channelCnt > 0) {
        Integer videoWallId = index;

        KvmVideoWall videoWall = new KvmVideoWall();
        videoWall.setWallId(videoWallId);
        videoWall.setDeviceId(index);
        videoWall.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_MODEL, String.valueOf(index * 8)));
        videoWall.getCollectorProperties().add(
                new Property(PropertyKeyConst.PREVIEW_TYPE, String.valueOf(SNAPSHOT)));
        videoWall.setDeviceModel(DeviceType.CAESAR_R1C8_VIDEO_WALL.getDeviceTypeId());
        videoWalls.put(videoWallId, videoWall);

        int singleCnt = Math.min(channelCnt, CaesarConstants.PREVIEW_CHANNEL_LIMIT);
        Set<KvmPreviewAsso> channels = new HashSet<>();
        for (int i = 1; i <= singleCnt; i++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          asso.setWallId(videoWallId);
          asso.setSeq(i);
          asso.setUrl(PreviewTestHelper.getPreviewUrl(videoWallId, i));
          channels.add(asso);
        }
        this.channels.put(videoWallId, channels);
        channelCnt -= singleCnt;
        index++;
      }
    }

    @Override
    public boolean is4kTx(String txId) {
      return tx4kes.containsKey(txId);
    }

    @Override
    public KvmPreviewAsso getAvailablePreviewChannel(boolean is4k,
        boolean ignoreIncompleteVideoWall) {
      Optional<Collection<KvmPreviewAsso>> allChannels = channels.entrySet().stream()
          .map((item) -> item.getValue()).reduce((lhs, rhs) -> {
            Set<KvmPreviewAsso> set = new HashSet<>();
            set.addAll(lhs);
            set.addAll(rhs);
            return set;
          });
      for (KvmPreviewAsso channel : allChannels.get()) {
        if (!channel.isUsed()) {
          return channel;
        }
      }
      return null;
    }

    @Override
    public Map<Integer, Collection<KvmPreviewAsso>> getAllPreviewChannels(
        boolean ignoreIncompleteVideoWall) {
      return channels;
    }

    @Override
    public Collection<KvmPreviewAsso> getPreviewChannels(Integer videoWallId) {
      return channels.getOrDefault(videoWallId, Collections.emptyList());
    }

    @Override
    public KvmVideoWall getKvmPreviewAssoWallById(Integer videoWallId) {
      return videoWalls.get(videoWallId);
    }

    @Override
    public KvmAsset getExtendDevice(String id) {
      KvmAsset asset = tx2kes.get(id);
      if (asset == null) {
        return tx4kes.get(id);
      }
      return asset;
    }

    @Override
    public String getMasterIp() {
      return null;
    }

    @Override
    public String makeTxSavePath(String hardCode) {
      return null;
    }

    @Override
    public void addPreviewChannel(Integer previewVideoWallId) {

    }

    @Override
    public void updatePreviewChannel(Integer previewVideoWallId) {

    }

    @Override
    public void updatePreviewChannel(String assetId, int availableNum) {

    }

    @Override
    public void delPreviewChannel(Integer previewVideoWallId) {

    }

    @Override
    public Map<String, Collection<KvmPreviewAsso>> getAllPreviewChannels(DeviceType previewDeviceType) {
      return new HashMap<>();
    }

    @Override
    public Collection<KvmPreviewAsso> getPreviewChannels(String assetId) {
      return new ArrayList<>();
    }
  }

  @Before
  public void before() throws IOException {
    System.out.println("before test case");
    PreviewTestHelper.createPreviewDirectory();
    deviceDataGetter = new DeviceDataGetterDecorator(new DeviceDataGetterMock(0, 0));
    previewDeviceOperator =
            new PreviewTestHelper.PreviewDeviceOperatorMock(
                    new PreviewTestHelper.TaskRunnerMock(), deviceDataGetter);
  }

  @After
  public void after() throws IOException {
    System.out.println("after test case");
    PreviewTestHelper.deletePreviewDirectory();
  }


  @Test
  public void testOpenPanel() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(100, 100);
    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx2kes.keySet(), channels);

    // 开窗
    long tic = System.currentTimeMillis();
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator.openPanel(txChannels);
    try {
      // 检查结果
      Map<String, Boolean> map = result.get(5, TimeUnit.SECONDS);
      long toc = System.currentTimeMillis();
      System.out.println(String.format("Open panel time :%dms", toc - tic));
      Assert.assertEquals(map.keySet(), txChannels.keySet());
      for (String tx : txChannels.keySet()) {
        boolean value = map.get(tx);
        Assert.assertTrue(value);
      }
      // 检查文件
      for (Map.Entry<String, KvmPreviewAsso> entry : txChannels.entrySet()) {
        FileReader reader = new FileReader(entry.getValue().getUrl());
        BufferedReader bufferedReader = new BufferedReader(reader);
        String msg = bufferedReader.readLine();
        bufferedReader.close();
        reader.close();
        Assert.assertTrue(msg.startsWith(entry.getKey()));
      }
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    // 关窗
    CompletableFuture<Boolean> closeResult = previewDeviceOperator.closePanels(txChannels.values());
    try {
      Boolean ret = closeResult.get(5, TimeUnit.SECONDS);
      Assert.assertTrue(ret);
      // 检查文件
      for (Map.Entry<String, KvmPreviewAsso> entry : txChannels.entrySet()) {
        FileReader reader = new FileReader(entry.getValue().getUrl());
        BufferedReader bufferedReader = new BufferedReader(reader);
        String msg = bufferedReader.readLine();
        bufferedReader.close();
        reader.close();
        Assert.assertTrue(msg.startsWith("0000"));
      }
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

  }

  @Test
  public void testOpenPanelWithoutEnoughChannel() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(8, 8, 8);
    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx4kes.keySet(), channels);
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator.openPanel(txChannels);
    try {
      // 检查结果，应该全部失败
      Map<String, Boolean> map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertFalse(value);
      }
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testOpenPanel4kReplace2k() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(CaesarConstants.PREVIEW_CHANNEL_LIMIT,
        CaesarConstants.PREVIEW_CHANNEL_LIMIT, CaesarConstants.PREVIEW_CHANNEL_LIMIT);
    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx2kes.keySet(), channels);

    try {
      CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator.openPanel(txChannels);
      // 检查结果，应该全部成功
      Map<String, Boolean> map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertTrue(value);
      }
      // 一半的4k开窗
      txChannels = PreviewTestHelper.makeMap(
          mock.tx4kes.keySet().stream().limit(CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2).collect(
              Collectors.toSet()),
          channels.stream().limit(CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2).collect(
              Collectors.toSet()));
      result = previewDeviceOperator.openPanel(txChannels);
      // 检查结果，应该全部成功
      map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertTrue(value);
      }
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testGetPreview() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(100, 100);

    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx2kes.keySet(), channels);

    // 开窗
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator.openPanel(txChannels);
    try {
      result.get(5, TimeUnit.SECONDS);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.assertFalse(true);
    }
    // 取图
    CompletableFuture<Map<String, Boolean>> previewRet = previewDeviceOperator.getPreview(
        txChannels.keySet().stream().collect(Collectors.toMap((e) -> e, PreviewTestHelper::getTxPath)));
    try {
      // 检查结果
      Map<String, Boolean> ret = previewRet.get(5, TimeUnit.SECONDS);
      Assert.assertEquals(ret.keySet(), txChannels.keySet());
      Assert.assertTrue(ret.values().stream().reduce(true, (lhs, rhs) -> lhs && rhs));
      // 检查文件
      PreviewTestHelper.checkTxPreview(txChannels.keySet(), true);
    } catch (Exception e) {
      Assert.fail();
    }
  }


  @Test
  public void testGetPreviewEmptyVideoWall() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(1, 2);

    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(Map.Entry::getValue)
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    List<String> txes = new ArrayList<>(mock.tx2kes.keySet());
    Assert.assertEquals(1, channels.size());
    Assert.assertEquals(2, txes.size());
    // 开窗第一个TX
    Map<String, KvmPreviewAsso> txChannelMap = new HashMap<>();
    txChannelMap.put(txes.get(0), channels.get(0));
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator.openPanel(txChannelMap);
    try {
      result.get(5, TimeUnit.SECONDS);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.assertFalse(true);
    }
    // 同一个通道开窗第二个TX
    txChannelMap.clear();
    txChannelMap.put(txes.get(1), channels.get(0));
    result = previewDeviceOperator.openPanel(txChannelMap);
    try {
      result.get(5, TimeUnit.SECONDS);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
    // 第一个TX取图
    Map<String, String> txSavePath = new HashMap<>();
    txSavePath.put(txes.get(0), PreviewTestHelper.getTxPath(txes.get(0)));
    // 第二次取图时VIDEOWALLID才为空
    for (int i = 0; i < 2; i++) {
      CompletableFuture<Map<String, Boolean>> previewRet = previewDeviceOperator
          .getPreview(txSavePath);
      try {
        // 检查结果
        Map<String, Boolean> ret = previewRet.get(5, TimeUnit.SECONDS);
        Assert.assertEquals(ret.keySet(), txSavePath.keySet());
        Assert.assertFalse(ret.get(txes.get(0)));
      } catch (Exception e) {
        Assert.fail();
      }
    }
  }

  @Test
  public void testOpenPanelAndGetPreview() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(100, 100);

    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx2kes.keySet(), channels);

    Map<String, String> txSavePath = txChannels.keySet().stream()
        .collect(Collectors.toMap(e -> e, PreviewTestHelper::getTxPath));
    // 开窗
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator
        .openPanelAndGetPreview(txChannels, txSavePath, Collections.emptyList());
    try {
      // 检查结果
      Map<String, Boolean> ret = result.get(5, TimeUnit.SECONDS);
      Assert.assertEquals(ret.keySet(), txChannels.keySet());
      for (Boolean item : ret.values()) {
        Assert.assertTrue(item);
      }
      // 检查文件
      PreviewTestHelper.checkTxPreview(txChannels.keySet(), true);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }


  @Test
  public void testOpenPanelAndGetPreview4kReplace2k() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(CaesarConstants.PREVIEW_CHANNEL_LIMIT,
        CaesarConstants.PREVIEW_CHANNEL_LIMIT, CaesarConstants.PREVIEW_CHANNEL_LIMIT);

    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx2kes.keySet(), channels);
    Map<String, String> txSavePath = txChannels.keySet().stream()
        .collect(Collectors.toMap(e -> e, e -> PreviewTestHelper.getTxPath(e)));
    try {
      CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator
          .openPanelAndGetPreview(txChannels, txSavePath, Collections.emptyList());
      // 检查结果，应该全部成功
      Map<String, Boolean> map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertTrue(value);
      }
      // 检查文件
      PreviewTestHelper.checkTxPreview(txChannels.keySet(), true);
      // 一半的4k开窗
      txChannels = PreviewTestHelper.makeMap(
          mock.tx4kes.keySet().stream().limit(CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2).collect(
              Collectors.toSet()),
          channels.stream().limit(CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2).collect(
              Collectors.toSet()));
      txSavePath = txChannels.keySet().stream()
          .collect(Collectors.toMap(e -> e, e -> PreviewTestHelper.getTxPath(e)));
      result = previewDeviceOperator
          .openPanelAndGetPreview(txChannels, txSavePath, Collections.emptyList());
      // 检查结果，应该全部成功
      map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertTrue(value);
      }
      // 检查文件
      PreviewTestHelper.checkTxPreview(txChannels.keySet(), true);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }


  @Test
  public void testOpenPanelAndGetPreviewWithoutEnoughChannel() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(8, 8, 8);

    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper
        .makeMap(mock.tx4kes.keySet(), channels);
    Map<String, String> txSavePath = txChannels.keySet().stream()
        .collect(Collectors.toMap(e -> e, e -> PreviewTestHelper.getTxPath(e)));
    CompletableFuture<Map<String, Boolean>> result = previewDeviceOperator
        .openPanelAndGetPreview(txChannels, txSavePath, Collections.emptyList());
    try {
      // 检查结果，应该全部失败
      Map<String, Boolean> map = result.get(5, TimeUnit.SECONDS);
      for (Boolean value : map.values()) {
        Assert.assertFalse(value);
      }
    } catch (Exception e) {
      e.printStackTrace();
      Assert.assertFalse(true);
    }
  }


  @Test
  public void testMultiThread() {
    DeviceDataGetterMock mock = new DeviceDataGetterMock(100, 100);
    deviceDataGetter.setInner(mock);
    List<KvmPreviewAsso> channels = mock.channels.entrySet().stream()
        .map(e -> e.getValue())
        .flatMap(Collection::stream).collect(
            Collectors.toList());
    Map<String, Long> txPreviewTime = new HashMap<>();
    List<Boolean> stop = Arrays.asList(false);

    Set<String> txSet1 = mock.tx2kes.keySet().stream().limit(mock.tx2kes.size() / 2)
        .collect(Collectors.toSet());
    Set<String> txSet2 = new HashSet<>(mock.tx2kes.keySet());
    txSet2.removeAll(txSet1);
    // openPanel + getPreview
    CompletableFuture<Void> openPanelFuture = CompletableFuture.runAsync(() -> {
      Random random = new Random();
      int cnt = 0;
      Set<String> txSet = txSet1;
      while (!stop.get(0)) {
        try {
          long tic = System.currentTimeMillis();
          int count = random.nextInt(Math.min(channels.size(), txSet.size()));
          Collection<String> subTx = PreviewTestHelper.getRandomElements(txSet, count);
          Collection<KvmPreviewAsso> subChannel = PreviewTestHelper
              .getRandomElements(channels, count);
          Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper.makeMap(subTx, subChannel);
          Map<String, Boolean> result = previewDeviceOperator.openPanel(txChannels).get();
          CompletableFuture<Map<String, Boolean>> getPreviewResult = previewDeviceOperator
              .getPreview(result.keySet().stream()
                  .collect(Collectors.toMap((e) -> e, (e) -> PreviewTestHelper.getTxPath(e))));
          Map<String, Boolean> previewResultMap = getPreviewResult
              .whenComplete((result2, throwable) -> {
                if (result2 != null) {
                  synchronized (txPreviewTime) {
                    for (String tx : result2.keySet()) {
                      if (result2.get(tx)) {
                        txPreviewTime.put(tx, System.currentTimeMillis());
                      }
                    }
                  }
                }
              }).get();
          int previewSuccessCnt = previewResultMap.entrySet().stream()
              .map((item) -> item.getValue() ? 1 : 0).reduce(0, (lhs, rhs) -> lhs + rhs);
          long toc = System.currentTimeMillis();
          System.out.println(String
              .format("openPanel + getPreview one time for %d tx(%d success) : %dms",
                  subChannel.size(), previewSuccessCnt,
                  toc - tic));
          cnt++;
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }
      System.out.println(String.format("openPanel + getPreview %d times.", cnt));
    });
    // openPanelAndGetPreview
    CompletableFuture<Void> openPanelAndGetPreviewFuture = CompletableFuture.runAsync(() -> {
      Random random = new Random();
      int cnt = 0;
      Set<String> txSet = txSet2;
      while (!stop.get(0)) {
        int count = random.nextInt(Math.min(channels.size(), txSet.size()));
        Collection<String> subTx = PreviewTestHelper.getRandomElements(txSet, count);
        Collection<KvmPreviewAsso> subChannel = PreviewTestHelper.getRandomElements(channels, count);
        Map<String, KvmPreviewAsso> txChannels = PreviewTestHelper.makeMap(subTx, subChannel);
        Map<String, String> txSavePath = txChannels.entrySet().stream()
            .collect(
                Collectors.toMap(e -> e.getKey(), e -> PreviewTestHelper.getTxPath(e.getKey())));
        try {
          long tic = System.currentTimeMillis();
          Map<String, Boolean> result = previewDeviceOperator
              .openPanelAndGetPreview(txChannels, txSavePath, Collections.emptyList()).get();
          synchronized (txPreviewTime) {
            for (Map.Entry<String, Boolean> entry : result.entrySet()) {
              Assert.assertTrue(entry.getValue());
              txPreviewTime.put(entry.getKey(), System.currentTimeMillis());
            }
          }
          long toc = System.currentTimeMillis();
          System.out.println(String
              .format("openPanelAndGetPreview one time for %d tx : %dms", subChannel.size(),
                  toc - tic));
          cnt++;
        } catch (Exception e) {
          e.printStackTrace();
          throw new RuntimeException(e);
        }
      }
      System.out.println(String.format("openPanelAndGetPreview %d times.", cnt));

    });

    // 检查文件
    CompletableFuture<Void> checkFileFuture = CompletableFuture.runAsync(() -> {
      while (!stop.get(0)) {
        Map<String, Long> temp;
        synchronized (txPreviewTime) {
          temp = new HashMap<>(txPreviewTime);
        }
        try {
          PreviewTestHelper.checkTxPreview(temp.keySet(), false);
          Thread.sleep(100);
        } catch (Exception e) {
          e.printStackTrace();
          stop.set(0, true);
          System.out.println("Stopped by exception!");
          throw new RuntimeException(e);
        }
      }
    });

    try {
      Thread.sleep(30000);
      stop.set(0, true);
      openPanelFuture.join();
      openPanelAndGetPreviewFuture.join();
      checkFileFuture.join();
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }


  }
}
