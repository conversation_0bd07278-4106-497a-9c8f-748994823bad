package com.mediacomm.caesar.snapshot;

import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.preview.strategy.impl.DeviceDataGetterImpl;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import org.junit.Assert;
import org.junit.Test;

public class DeviceDataGetterImplTest {

  @Test
  public void testGetAvailablePreviewChannel2k() {
    DeviceDataGetterImpl impl = new DeviceDataGetterImpl(new
            PreviewTestHelper.CaesarRunnerForeignMock(1, 8, ""), "");
    impl.getAllPreviewChannels(true);
    for (int i = 0; i < CaesarConstants.PREVIEW_CHANNEL_LIMIT; i++) {
      KvmPreviewAsso asso = impl.getAvailablePreviewChannel(false, true);
      Assert.assertNotNull(asso);
      asso.setUsed(true);
    }
    KvmPreviewAsso asso = impl.getAvailablePreviewChannel(false, true);
    Assert.assertNull(asso);
    asso = impl.getAvailablePreviewChannel(true, true);
    Assert.assertNull(asso);
  }

  @Test
  public void testGetAvailablePreviewChannel4k() {
    DeviceDataGetterImpl impl = new DeviceDataGetterImpl(new
            PreviewTestHelper.CaesarRunnerForeignMock(1, 8, ""), "");
    impl.getAllPreviewChannels(true);
    for (int i = 0; i < CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2; i++) {
      KvmPreviewAsso asso = impl.getAvailablePreviewChannel(true, true);
      Assert.assertNotNull(asso);
      asso.setUsed(true);
      asso.setHighResolution(true);
    }
    KvmPreviewAsso asso = impl.getAvailablePreviewChannel(true, true);
    Assert.assertNull(asso);
    asso = impl.getAvailablePreviewChannel(false, true);
    Assert.assertNull(asso);
  }

  @Test
  public void testGetAvailablePreviewChannelOdd2k4k() {
    DeviceDataGetterImpl impl = new DeviceDataGetterImpl(
            new PreviewTestHelper.CaesarRunnerForeignMock(1, 8, ""), "");
    impl.getAllPreviewChannels(true);
    for (int i = 0; i < CaesarConstants.PREVIEW_CHANNEL_LIMIT / 2 - 1; i++) {
      KvmPreviewAsso asso = impl.getAvailablePreviewChannel(false, true);
      Assert.assertNotNull(asso);
      asso.setUsed(true);
    }
    for (int i = 0; i < CaesarConstants.PREVIEW_CHANNEL_LIMIT / 4; i++) {
      KvmPreviewAsso asso = impl.getAvailablePreviewChannel(true, true);
      Assert.assertNotNull(asso);
      asso.setUsed(true);
      asso.setHighResolution(true);
    }
    KvmPreviewAsso asso = impl.getAvailablePreviewChannel(true, true);
    Assert.assertNull(asso);
  }
}
