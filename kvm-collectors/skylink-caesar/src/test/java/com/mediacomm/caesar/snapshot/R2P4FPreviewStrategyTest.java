package com.mediacomm.caesar.snapshot;

import cn.hutool.core.util.ZipUtil;
import com.mediacomm.caesar.domain.vp7.R2P4FResponse;
import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.device.R2P4FPreviewOperatorImpl;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.caesar.preview.strategy.PreviewMap;
import com.mediacomm.caesar.preview.strategy.impl.DeviceDataGetterImpl;
import com.mediacomm.caesar.preview.strategy.impl.R2P4FPreviewStrategy;
import com.mediacomm.caesar.task.PollingPreviewRunner;
import com.mediacomm.caesar.util.CaesarDbServiceUtil;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.task.SkyLinkTaskPool;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpVersion;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.BasicHttpEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicStatusLine;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * .
 */
public class R2P4FPreviewStrategyTest {
  private final String masterId = UUID.randomUUID().toString();
  private final static String R2P4F_IMAGE_PATH = "/tmp/snapshot/";
  private final static String SKYLINK_IMAGE_PATH = "/tmp/snapshot/caesar/";

  @Before
  public void before() throws IOException {
    Path path = Paths.get(SKYLINK_IMAGE_PATH);
    if (!path.toFile().exists()) {
      Files.createDirectories(path);
    }
  }

  @Test
  public void testAddPreview() throws IllegalAccessException {
    // R2P4预览只有轮询，并且会根据实际的返回channel值调整可用通道数
    DeviceDataGetter dataGetter = mock(DeviceDataGetterImpl.class);
    R2P4FPreviewStrategy strategy = new R2P4FPreviewStrategy(dataGetter);
    int cnt = 10;
    ObjectIds request = SimplePreviewStrategyTest.makeRequest(true, 0, cnt);
    PreviewMap previewMap = (PreviewMap) FieldUtil.getPropertyValue(strategy, "currentPreview");
    strategy.addPreview(request);

    Assert.assertTrue(previewMap.getPollingPreviewChannels().isEmpty());
    for (int i = 0; i < cnt; i++) {
      String txId = request.getIds().get(i);
      Assert.assertTrue(previewMap.isPollingPreviewing(txId));
      Assert.assertTrue(previewMap.isPreviewing(txId));
      PreviewInfoGetter.PollingPreviewStatus pollingStatus = previewMap.getPollingPreviewStatus(txId);
      Assert.assertEquals(PreviewInfoGetter.PollingPreviewStatus.POLLING, pollingStatus);
      // 上次使用时间
      long time = previewMap.getLastPreviewUsedTime(txId);
      Assert.assertTrue(System.currentTimeMillis() - time < 1000);
    }
  }

  @Test
  public void testR2P4FPreviewOperatorSuccess() throws IOException, IllegalAccessException {
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    CaesarDbServiceUtil dbServiceUtil = mock(CaesarDbServiceUtil.class);
    DeviceDataGetter dataGetter = new DeviceDataGetterImpl(dbServiceUtil, masterId);
    R2P4FKvmAssetService assetService = new R2P4FKvmAssetService(26);
    R2P4FPreviewStrategy strategy = new R2P4FPreviewStrategy(dataGetter);
    when(dbServiceUtil.getKvmAssetService()).thenReturn(assetService);
    strategy.resetChannels();
    CloseableHttpClient httpClient = mock(CloseableHttpClient.class);

    R2P4FResponse ops1 = new R2P4FResponse();
    ops1.setCode(0);
    ops1.setMessage("success");
    ops1.setChannel(16);
    ops1.setResult(new ArrayList<>());

    Map<String, KvmPreviewAsso> txChannelMap = new HashMap<>();
    Map<String, String> txSavePathMap = new HashMap<>();
    Collection<KvmPreviewAsso> unusedChannels = new ArrayList<>(); // 无用参数
    Collection<KvmPreviewAsso> availableChannels = dataGetter.getPreviewChannels("b73533dc9aab4735a99cc9a636c100ab");
    Assert.assertEquals(16, availableChannels.size());
    for (int i = 0; i < availableChannels.size(); i++) {
      txChannelMap.put(i + "", availableChannels.iterator().next());
      txSavePathMap.put(i + "", SKYLINK_IMAGE_PATH + i + ".jpg");
    }
    List<File> jpgs = new ArrayList<>();
    txChannelMap.forEach((k, v) -> {
      KvmAsset ka = assetService.getById(k);
      R2P4FResponse.TxStatus ts = new R2P4FResponse.TxStatus();
      ts.setTxid(ka.getDeviceId());
      ts.setStatus(1);
      ops1.getResult().add(ts);
      jpgs.add(new File(R2P4F_IMAGE_PATH + ka.getDeviceId() + ".jpg"));
    });
    // post
    BasicHttpEntity entity = new BasicHttpEntity();
    entity.setContent(new ByteArrayInputStream(JsonUtils.encode(ops1).getBytes()));
    CloseableHttpResponse postFuture = mock(CloseableHttpResponse.class);
    when(postFuture.getStatusLine()).thenReturn(new BasicStatusLine(HttpVersion.HTTP_1_1, 200, "OK"));
    when(postFuture.getEntity()).thenReturn(entity);
    when(httpClient.execute(Mockito.any(HttpPost.class))).thenReturn(postFuture);
    // get
    File imageZip = new File(R2P4F_IMAGE_PATH + System.currentTimeMillis() + ".zip");
    ZipUtil.zip(imageZip, false, jpgs.toArray(new File[]{}));
    BasicHttpEntity entityImage = new BasicHttpEntity();
    entityImage.setContent(new FileInputStream(imageZip));
    CloseableHttpResponse getFuture = mock(CloseableHttpResponse.class);
    when(getFuture.getStatusLine()).thenReturn(new BasicStatusLine(HttpVersion.HTTP_1_1, 200, "OK"));
    when(getFuture.getEntity()).thenReturn(entityImage);
    when(httpClient.execute(Mockito.any(HttpGet.class))).thenReturn(getFuture);
    R2P4FPreviewOperatorImpl operator = new R2P4FPreviewOperatorImpl(taskPool, dataGetter, strategy.getPreviewInfoOpt());
    FieldUtil.setPropertyValue(operator, "httpClient", httpClient);
    try {
      CompletableFuture<Map<String, Boolean>> future = operator.openPanelAndGetPreview(txChannelMap, txSavePathMap, unusedChannels)
              .whenComplete((stringBooleanMap, throwable) -> {
                for (Map.Entry<String, Boolean> entry : stringBooleanMap.entrySet()) {
                  Assert.assertEquals(true, entry.getValue());
                }
              });
      future.get();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      if (imageZip.exists()) {
        FileUtils.delete(imageZip);
      }
      R2P4FKvmAssetService.assetMap.forEach((k, v) -> {
        File jpgFile = new File(SKYLINK_IMAGE_PATH + v.getDeviceId() + ".jpg");
        if (jpgFile.exists()) {
          try {
            Files.delete(jpgFile.toPath());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        }
      });
    }
  }

  @Test
  public void testR2P4FPreviewOperatorUpdateChannel() throws IOException, IllegalAccessException {
    SkyLinkTaskPool taskPool = new PreviewTestHelper.TaskRunnerMock();
    CaesarDbServiceUtil dbServiceUtil = mock(CaesarDbServiceUtil.class);
    DeviceDataGetter dataGetter = new DeviceDataGetterImpl(dbServiceUtil, masterId);
    R2P4FKvmAssetService assetService = new R2P4FKvmAssetService(26);
    R2P4FPreviewStrategy strategy = new R2P4FPreviewStrategy(dataGetter);
    when(dbServiceUtil.getKvmAssetService()).thenReturn(assetService);
    strategy.resetChannels();

    CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
    R2P4FPreviewOperatorImpl operator = new R2P4FPreviewOperatorImpl(taskPool, dataGetter, strategy.getPreviewInfoOpt());
    FieldUtil.setPropertyValue(operator, "httpClient", httpClient);

    R2P4FResponse ops1 = new R2P4FResponse();
    ops1.setCode(0);
    ops1.setMessage("success");
    ops1.setChannel(8);
    ops1.setTimestamp(System.currentTimeMillis());
    ops1.setResult(new ArrayList<>());

    Map<String, KvmPreviewAsso> txChannelMap = new HashMap<>();
    Map<String, String> txSavePathMap = new HashMap<>();
    Collection<KvmPreviewAsso> unusedChannels = new ArrayList<>(); // 无用参数
    Collection<KvmPreviewAsso> availableChannels = dataGetter.getPreviewChannels("b73533dc9aab4735a99cc9a636c100ab");
    Assert.assertEquals(16, availableChannels.size());
    for (int i = 0; i < availableChannels.size(); i++) {
      txChannelMap.put(i + "", availableChannels.iterator().next());
      txSavePathMap.put(i + "", SKYLINK_IMAGE_PATH + i + ".jpg");
    }
    List<File> jpgs = new ArrayList<>();
    int successCount = 0;
    int maxSuccess = 8; // 最多成功数量
    for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
      String k = entry.getKey();
      KvmAsset ka = assetService.getById(k);
      R2P4FResponse.TxStatus ts = new R2P4FResponse.TxStatus();
      ts.setTxid(ka.getDeviceId());
      if (successCount < maxSuccess) {
        ts.setStatus(1); // 成功
        jpgs.add(new File(R2P4F_IMAGE_PATH + ka.getDeviceId() + ".jpg"));
        successCount++;
      } else {
        ts.setStatus(5); // 失败
      }
      ops1.getResult().add(ts);
    }
    // post
    BasicHttpEntity entity = new BasicHttpEntity();
    entity.setContent(new ByteArrayInputStream(JsonUtils.encode(ops1).getBytes()));
    CloseableHttpResponse postFuture = mock(CloseableHttpResponse.class);
    when(postFuture.getStatusLine()).thenReturn(new BasicStatusLine(HttpVersion.HTTP_1_1, 200, "OK"));
    when(postFuture.getEntity()).thenReturn(entity);
    when(httpClient.execute(Mockito.any(HttpPost.class))).thenReturn(postFuture);
    // get
    File imageZip = new File(R2P4F_IMAGE_PATH + System.currentTimeMillis() + ".zip");
    ZipUtil.zip(imageZip, false, jpgs.toArray(new File[]{}));
    BasicHttpEntity entityImage = new BasicHttpEntity();
    FileInputStream fileInputStream = new FileInputStream(imageZip);
    entityImage.setContent(fileInputStream);
    CloseableHttpResponse getFuture = mock(CloseableHttpResponse.class);
    when(getFuture.getStatusLine()).thenReturn(new BasicStatusLine(HttpVersion.HTTP_1_1, 200, "OK"));
    when(getFuture.getEntity()).thenReturn(entityImage);
    when(httpClient.execute(Mockito.any(HttpGet.class))).thenReturn(getFuture);

    try {
      CompletableFuture<Map<String, Boolean>> future = operator.openPanelAndGetPreview(txChannelMap, txSavePathMap, unusedChannels)
              .whenComplete((stringBooleanMap, throwable) -> {
                long sc = stringBooleanMap.values().stream()
                        .filter(b -> b.equals(true))
                        .count();
                Assert.assertEquals(8, sc);
              });
      future.get();
      Assert.assertEquals(8, dataGetter.getPreviewChannels("b73533dc9aab4735a99cc9a636c100ab").size());
      Assert.assertEquals(8, strategy.getPreviewInfoOpt().getPollingPreviewChannels().size());
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      fileInputStream.close();
      if (imageZip.exists()) {
        FileUtils.delete(imageZip);
      }
      R2P4FKvmAssetService.assetMap.forEach((k, v) -> {
        File jpgFile = new File(SKYLINK_IMAGE_PATH + v.getDeviceId() + ".jpg");
        if (jpgFile.exists()) {
          try {
            Files.delete(jpgFile.toPath());
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        }
      });
    }
  }

  @Test
  public void testUpdatePreviewMapPollingChannels() throws IOException {
    PreviewMap previewMap = new PreviewMap();
    R2P4FKvmAssetService assetService = new R2P4FKvmAssetService(0);
    Map<String, Collection<KvmPreviewAsso>> channelMap = InspectCaesarUtil
            .buildPreviewChannelsByType(assetService
                    .allByDeviceModelId(DeviceType.CAESAR_R2P4F.getDeviceTypeId(), masterId));
    Collection<KvmPreviewAsso> channels = new ArrayList<>();
    channelMap.values().forEach(channels::addAll);
    previewMap.setPollingPreviewChannels(channels);
    // 需要显式地操作原始集合，才能局部的更新预览通道
    Collection<KvmPreviewAsso> showChannels = new ArrayList<>(previewMap.getPollingPreviewChannels());
    showChannels.removeIf(item -> item.getRxId().equals("b73533dc9aab4735a99cc9a636c100ab"));
    previewMap.setPollingPreviewChannels(showChannels);
    System.out.println("After remove size: " + previewMap.getPollingPreviewChannels().size());
    // 一个R2P4F在主机上是两个Rx数据，需要过滤重复ip，16个预览通道
    Assert.assertEquals(16, previewMap.getPollingPreviewChannels().size());
  }

  @Test
  public void testEstablishConnectionsSetWallId() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    PreviewInfoOperator opt = mock(PreviewInfoOperator.class);
    DeviceDataGetter dg = mock(DeviceDataGetter.class);
    PreviewDeviceOperator pdo = mock(PreviewDeviceOperator.class);
    PollingPreviewRunner pr = new PollingPreviewRunner(opt, dg, pdo);
    Class<?> clazz = pr.getClass();
    Method establishConnections = clazz.getDeclaredMethod("establishConnections", Set.class, Map.class);
    establishConnections.setAccessible(true);
    Set<String> txIds = new HashSet<>();
    // 15个2K + 1个4K，此时要占用17个channel，要被成功分配开到第二个r2p4f
    for (int i = 0; i < 16; i++) {
      txIds.add(i + "");
      when(dg.is4kTx(i + "")).thenReturn(false);
    }
    txIds.add("16");
    when(dg.is4kTx("16")).thenReturn(true);
    Collection<KvmAssetVo> r2p4fs = new ArrayList<>();
    for (int i = 0; i < 2; i++) {
      KvmAssetVo r1 = new KvmAssetVo();
      r1.setDeviceType(DeviceType.CAESAR_R2P4F.getDeviceType());
      r1.setDeviceId(1001 + i);
      r1.setAssetId(i + "");
      r1.setDeviceIp("127.0.0." + i);
      r1.getProperties().add(new Property(PropertyKeyConst.SPECIAL_EXT_SUB_TYPE, "0"));
      r2p4fs.add(r1);
    }
    Map<String, Collection<KvmPreviewAsso>> r2p4fMap = InspectCaesarUtil.buildPreviewChannelsByType(r2p4fs);
    Collection<KvmPreviewAsso> pollingChannels = r2p4fMap.values()
            .stream().flatMap(Collection::stream).toList();
    Map<String, KvmPreviewAsso> cons = (Map<String, KvmPreviewAsso>)
            establishConnections.invoke(pr, txIds, pr.getKvmVideoWallWithReviewAsso(pollingChannels));
    Map<String, List<KvmPreviewAsso>> cons2 = cons.values().stream()
            .collect(Collectors.groupingBy(KvmPreviewAsso::getRxId));
    Assert.assertEquals(15, cons2.get("0").size());  // 14个2K + 1个4K
    Assert.assertEquals(2, cons2.get("1").size());  // 剩余的2个2K
  }

  class R2P4FKvmAssetService extends KvmAssetService {
    private Collection<KvmAssetVo> r2p4fList = new ArrayList<>();
    public static Map<String, KvmAsset> assetMap = new HashMap<>();

    public R2P4FKvmAssetService(int txNum) throws IOException {
      // 可用
      String str1 = "{\n" +
              "    \"assetId\": \"b73533dc9aab4735a99cc9a636c100ab\",\n" +
              "    \"alias\": \"RX_414666011\",\n" +
              "    \"name\": \"prev2\",\n" +
              "    \"collectorProperties\": [{\"propertyKey\": \"previewIntervalTime\", \"propertyValue\": \"500\"}],\n" +
              "    \"deviceIp\": \"*************\",\n" +
              "    \"deviceModel\": 2305,\n" +
              "    \"properties\": [{\"propertyKey\": \"deviceType\", \"propertyValue\": \"RX\"}, {\"propertyKey\": \"systemVersion\", \"propertyValue\": \"1.0\"}, {\"propertyKey\": \"fpgaVersion\", \"propertyValue\": \"4.2\"}, {\"propertyKey\": \"redundant\", \"propertyValue\": \"true\"}, {\"propertyKey\": \"videoNumber\", \"propertyValue\": \"2\"}, {\"propertyKey\": \"videoResolutionType\", \"propertyValue\": \"2\"}, {\"propertyKey\": \"videoIntfType\", \"propertyValue\": \"4\"}, {\"propertyKey\": \"link1Port\", \"propertyValue\": \"337\"}, {\"propertyKey\": \"link2Port\", \"propertyValue\": \"0\"}, {\"propertyKey\": \"rxType\", \"propertyValue\": \"0\"}, {\"propertyKey\": \"specialExtType\", \"propertyValue\": \"6\"}, {\"propertyKey\": \"specialExtSubType\", \"propertyValue\": \"0\"}],\n" +
              "    \"version\": [ { \"name\": \"app\", \"version\": \"0.1\", \"date\": null }, { \"name\": \"sys\", \"version\": \"1.0\", \"date\": null }, { \"name\": \"fpga\", \"version\": \"1.48\", \"date\": null } ],\n" +
              "    \"hardcode\": \"6eb441bc2171491c8f4d59cd0046cc13.3418666011.3005\",\n" +
              "    \"masterId\": \"6eb441bc2171491c8f4d59cd0046cc13\",\n" +
              "    \"deviceId\": 3005\n" +
              "  }";
      KvmAssetVo r1 = JsonUtils.decode(str1, KvmAssetVo.class);
      r1.setDeviceType(DeviceType.CAESAR_R2P4F.getDeviceType());
      r1.setMasterId(masterId);
      // 不可用
      String str2 = "{\n" +
              "    \"assetId\": \"9522b6fcfa0c4d8ba502b60baf0fb2a6\",\n" +
              "    \"alias\": \"RX_419666011\",\n" +
              "    \"name\": \"RX_419666011\",\n" +
              "    \"collectorProperties\": [],\n" +
              "    \"deviceIp\": \"*************\",\n" +
              "    \"deviceModel\": 2305,\n" +
              "    \"properties\": [{\"propertyKey\": \"deviceType\", \"propertyValue\": \"RX\"}, {\"propertyKey\": \"systemVersion\", \"propertyValue\": \"1.0\"}, {\"propertyKey\": \"fpgaVersion\", \"propertyValue\": \"4.2\"}, {\"propertyKey\": \"redundant\", \"propertyValue\": \"true\"}, {\"propertyKey\": \"videoNumber\", \"propertyValue\": \"2\"}, {\"propertyKey\": \"videoResolutionType\", \"propertyValue\": \"2\"}, {\"propertyKey\": \"videoIntfType\", \"propertyValue\": \"4\"}, {\"propertyKey\": \"link1Port\", \"propertyValue\": \"0\"}, {\"propertyKey\": \"link2Port\", \"propertyValue\": \"345\"}, {\"propertyKey\": \"rxType\", \"propertyValue\": \"0\"}, {\"propertyKey\": \"specialExtType\", \"propertyValue\": \"6\"}, {\"propertyKey\": \"specialExtSubType\", \"propertyValue\": \"1\"}],\n" +
              "    \"version\": [ { \"name\": \"app\", \"version\": \"0.1\", \"date\": null }, { \"name\": \"sys\", \"version\": \"1.0\", \"date\": null }, { \"name\": \"fpga\", \"version\": \"1.48\", \"date\": null } ],\n" +
              "    \"hardcode\": \"6eb441bc2171491c8f4d59cd0046cc13.3419666011.3006\",\n" +
              "    \"masterId\": \"6eb441bc2171491c8f4d59cd0046cc13\",\n" +
              "    \"deviceId\": 3006\n" +
              "  }";
      KvmAssetVo r2 = JsonUtils.decode(str2, KvmAssetVo.class);
      r2.setDeviceType(DeviceType.CAESAR_R2P4F.getDeviceType());
      r2.setMasterId(masterId);
      r2p4fList.add(r1);
      r2p4fList.add(r2);
      assetMap.put(r1.getAssetId(), r1);
      assetMap.put(r2.getAssetId(), r2);
      for (int i = 0; i < txNum; i++) {
        KvmAsset tx = new KvmAsset();
        tx.setAssetId(i + "");
        tx.setDeviceId(i);
        File jpgFile = new File(R2P4F_IMAGE_PATH + tx.getDeviceId() + ".jpg");
        jpgFile.createNewFile();
        tx.setDeviceModel(DeviceType.CAESAR_TX.getDeviceTypeId());
        assetMap.put(tx.getAssetId(), tx);
      }
    }

    @Override
    public KvmAsset getById(Serializable id) {
      return assetMap.get(id);
    }

    @Override
    public Collection<KvmAssetVo> allByDeviceModelId(Integer deviceModelId, String masterId) {
      return r2p4fList;
    }

    @Override
    public KvmAssetVo oneById(String assetId) {
      for (KvmAssetVo kvmAssetVo : r2p4fList) {
        if (kvmAssetVo.getAssetId().equals(assetId)) {
          return kvmAssetVo;
        }
      }
      return null;
    }
  }

  @After
  public void afterTest() {
    R2P4FKvmAssetService.assetMap.forEach((k, v) -> {
      File jpgFile = new File(R2P4F_IMAGE_PATH + v.getDeviceId() + ".jpg");
      if (jpgFile.exists()) {
        try {
          Files.delete(jpgFile.toPath());
        } catch (IOException e) {
          throw new RuntimeException(e);
        }
      }
    });
  }
}
