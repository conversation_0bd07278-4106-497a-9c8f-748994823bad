package com.mediacomm.caesar.apiData;

import com.mediacomm.caesar.domain.CaesarDeviceStatus;
import com.mediacomm.util.JsonUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class SignalCollectorTest {
  @Test
  public void testDeviceStatusSerialization() {
    String newData = "{\"connectStatusList\":[{\"connectStatus\":\"disconnect\",\"connectedId\":0,\"properties\":[]}],\"deviceId\":3001,\"deviceType\":\"Rx\",\"edid1Valid\":false,\"edid2Valid\":false,\"edidValid\":[false],\"inputHeight\":0,\"inputWidth\":0,\"keyboardCount\":0,\"link1Port\":15,\"link2Port\":0,\"linkStatus\":true,\"loginUser\":\"\",\"mouseCount\":0,\"properties\":[],\"touchCount\":0,\"typeBStatus\":false,\"udiskCount\":0,\"video1Input\":true,\"video2Input\":false,\"videoInput\":[true],\"videoLine1Status\":false,\"videoLine2Status\":false,\"videoLineStatus\":[false],\"vpcon\":false}";
    CaesarDeviceStatus newDeviceStatus = JsonUtils.decode(newData, CaesarDeviceStatus.class);
    assert newDeviceStatus != null;
    Assert.assertEquals(1, newDeviceStatus.getVideoInput().size());
    Assert.assertEquals(Boolean.TRUE, newDeviceStatus.getVideoInput().get(0));
    String oldData = "{\"connectStatusList\":[{\"connectStatus\":\"disconnect\",\"connectedId\":0,\"properties\":[]}],\"deviceId\":3001,\"deviceType\":\"Rx\",\"edid1Valid\":false,\"edid2Valid\":false,\"inputHeight\":0,\"inputWidth\":0,\"keyboardCount\":0,\"link1Port\":15,\"link2Port\":0,\"linkStatus\":true,\"loginUser\":\"\",\"mouseCount\":0,\"properties\":[],\"touchCount\":0,\"typeBStatus\":false,\"udiskCount\":0,\"video1Input\":true,\"video2Input\":true,\"videoLine1Status\":false,\"videoLine2Status\":false,\"vpcon\":false}";
    CaesarDeviceStatus oldDeviceStatus = JsonUtils.decode(oldData, CaesarDeviceStatus.class);
    assert oldDeviceStatus != null;
    Assert.assertEquals(2, oldDeviceStatus.getVideoInput().size());
    Assert.assertEquals(Boolean.TRUE, oldDeviceStatus.getVideoInput().get(0));
    Assert.assertEquals(Boolean.TRUE, oldDeviceStatus.getVideoInput().get(1));
  }
}
