package com.mediacomm.caesar.domain.kaito.alpha;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.nio.charset.StandardCharsets;
import lombok.Data;


/**
 * .
 */
@Data
public class KaitoAlphaRequestBody<T> {
  @JsonProperty("pId") // 防止被转成pid
  private String pId;
  private String timeStamp = String.valueOf(System.currentTimeMillis());
  private String sign;
  private T body;

  /**
   * 禁止加密,base64(md5(pid + timeStamp)).
   */
  public void setSign() {
    String md5 = DigestUtil.md5Hex(String.format("%s%s", timeStamp, pId), StandardCharsets.UTF_8);
    this.sign = Base64.encode(md5);
  }
}
