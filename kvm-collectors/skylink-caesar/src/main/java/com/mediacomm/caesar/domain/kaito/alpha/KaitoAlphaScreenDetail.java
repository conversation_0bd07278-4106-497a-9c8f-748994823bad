package com.mediacomm.caesar.domain.kaito.alpha;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mediacomm.caesar.domain.kaito.KaitoScreenDetail;
import lombok.Data;

/**
 * sb alpha.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoAlphaScreenDetail {
  private KaitoScreenDetail.Osd osd;
  private KaitoScreenDetail.Osd osdImage;
  private int brightness;
  private int deviceId;
  private int gamma;
  private KaitoScreenDetail.General general;
  private int mayUse;
  private KaitoScreenDetail.OutputMode outputMode;
  private int screenId;
}
