package com.mediacomm.caesar.task;

import com.google.common.collect.Sets;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class PollingPreviewRunner extends PreviewThread {

  /**
   * .
   */
  public PollingPreviewRunner(PreviewInfoOperator previewInfo,
                              DeviceDataGetter dataGetter, PreviewDeviceOperator operator) {
    this.previewInfo = previewInfo;
    this.dataGetter = dataGetter;
    this.operator = operator;
  }

  @Override
  public String getThreadName() {
    return "PollingPreviewRunner Thread";
  }

  @Override
  protected void execute() {
    Set<String> txIds;
    Collection<KvmPreviewAsso> reviewAssos;
    synchronized (previewInfo) {
      // 深拷贝一份轮询的Tx数据
      txIds = previewInfo.getPollingPreviewTxes().stream()
          .filter(tx -> previewInfo.getPollingPreviewStatus(tx)
              == PreviewInfoGetter.PollingPreviewStatus.POLLING)
          .collect(Collectors.toSet());
      reviewAssos = previewInfo.getPollingPreviewChannels();
    }
    try {
      if (txIds.isEmpty()) {
        Thread.sleep(1000);
        return;
      }
      if (reviewAssos.isEmpty()) {
        log.warn("Polling preview channels are null");
        Thread.sleep(1000);
        return;
      }
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    log.debug("Polling preview channels:" + reviewAssos + " and preview txIds " + txIds);
    // 单次轮询需要将所有待轮询的Tx开完，再继续下一轮
    while (!txIds.isEmpty()) {
      try {
        CompletableFuture<Map<String, Boolean>> future;
        synchronized (previewInfo) {
          txIds.removeIf(tx -> !previewInfo.isPollingPreviewing(tx)
              || previewInfo.getPollingPreviewStatus(tx)
              != PreviewInfoGetter.PollingPreviewStatus.POLLING);
          Map<String, KvmPreviewAsso> txChannelMap;
          // 深拷贝一份轮询的预览通道
          Set<KvmPreviewAsso> channels = Sets.newHashSet(reviewAssos);
          if (operator.getPreviewStrategyMode() == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX) {
            txChannelMap = establishConnections(txIds, channels);
          } else {
            // R1C8 R2P4F
            Map<Integer, Collection<KvmPreviewAsso>> kvmVideoWallWithReviewAsso =
                getKvmVideoWallWithReviewAsso(channels);
            txChannelMap = establishConnections(txIds, kvmVideoWallWithReviewAsso);
          }

          if (txChannelMap.isEmpty()) {
            break;
          }
          txIds.removeAll(txChannelMap.keySet());
          Map<String, String> txSavePathMap = new HashMap<>();
          for (String txId : txChannelMap.keySet()) {
            // key:txId,value:/tmp/snapshot/caesar/<hardCode>.jpg
            txSavePathMap.put(txId, dataGetter.makeTxSavePath(txId));
          }
          channels.removeAll(txChannelMap.values());  // 没有使用的通道
          future = operator.openPanelAndGetPreview(txChannelMap, txSavePathMap, channels)
              .whenComplete((stringBooleanMap, throwable) -> {
                for (Map.Entry<String, Boolean> entry : stringBooleanMap.entrySet()) {
                  String txId = entry.getKey();
                  if (stringBooleanMap.get(txId)) {
                    log.debug(
                        "PollingPreviewThread Current connections:" + txId
                            + " -> " + txChannelMap.get(txId).getUrl());
                  } else {
                    log.error(
                        "PollingPreviewThread open " + txId + " Panel failed!",
                        throwable);
                  }
                }
              });
        }
        future.get();
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
  }

  /**
   * 建立一次轮询Tx与连接通道的关系.
   *
   * @param txIds 需要轮询的所有Tx.
   * @param assos 可用所有预览通道.
   * @return 成功建立连接关系的Map.
   */
  private Map<String, KvmPreviewAsso> establishConnections(Set<String> txIds,
                                                           Map<Integer, Collection<KvmPreviewAsso>>
                                                               assos) {
    Map<String, KvmPreviewAsso> connections = new HashMap<>();
    for (String txId : txIds) {
      boolean unspent = isExistNoEmptyList(assos.values());               // 检查预览的通道是否分配完
      if (!unspent) {
        break;
      }
      if (dataGetter.is4kTx(txId)) {                                      // 判断是不是4k
        for (Map.Entry<Integer, Collection<KvmPreviewAsso>> entry : assos.entrySet()) {
          List<KvmPreviewAsso> list = (List<KvmPreviewAsso>) entry.getValue();
          if (list.size() > 1) {                                      // 需要拿一个有足够预览数的电视墙进行开窗
            log.debug("Try establish 4k connections polling preview channels:"
                + txId + " -> " + list.get(0).getUrl() + " and " + list.get(1).getUrl());
            connections.put(txId, list.remove(0));                // 消耗掉两个通道，避免被别的tx连接
            list.remove(0);
            break;
          }
        }
      } else {
        for (Map.Entry<Integer, Collection<KvmPreviewAsso>> entry : assos.entrySet()) {
          if (!entry.getValue().isEmpty()) {
            List<KvmPreviewAsso> list = (List<KvmPreviewAsso>) entry.getValue();
            log.debug("Try establish 2k connections polling preview channel:"
                + txId + " -> " + list.get(0).getUrl());
            connections.put(txId, list.remove(0));     // 消耗掉1个通道
            break;
          }
        }
      }
    }
    return connections;
  }

  private Map<String, KvmPreviewAsso> establishConnections(Set<String> txIds,
                                                           Collection<KvmPreviewAsso> assos) {
    Map<String, KvmPreviewAsso> connections = new HashMap<>();
    for (KvmPreviewAsso asso : assos) {
      Optional<String> id = txIds.stream().findFirst();
      if (id.isPresent()) {
        connections.put(id.get(), asso);
        txIds.remove(id.get());
      }
    }
    return connections;
  }

  /**
   * 检查是否存在预览通道.
   *
   * @param value 预览通道集合.
   * @return 存在返回true.
   */
  private boolean isExistNoEmptyList(Collection<Collection<KvmPreviewAsso>> value) {
    boolean exist = false;
    for (Collection<KvmPreviewAsso> col : value) {
      if (!col.isEmpty()) {
        exist = true;
        break;
      }
    }
    return exist;
  }

  @Override
  public boolean isStop() {
    return stop;
  }

  @Override
  public void stop() {
    this.stop = true;
    try {
      while(isRunning()) {
        Thread.sleep(100);
      }
      operator.close();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

}
