package com.mediacomm.caesar.domain;

import java.util.List;

public interface CaesarDeviceSameField {
  String getSn();
  String getDeviceType();
  String getSoftVersion();
  String getSystemVersion();
  String getFpgaVersion();
  boolean isRedundant();
  int getId();
  int getVideoNumber();
  int getVideoResolutionType();
  int getVideoIntfType();
  int getLink1Port();
  int getLink2Port();
  List<CaesarProperty> getProperties();
}
