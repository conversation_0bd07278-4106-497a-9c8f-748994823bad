package com.mediacomm.caesar.util;

import com.mediacomm.system.exception.SkyLinkException;
import com.mediacomm.util.TcpClient;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * caesar tcp command.
 */
public class CaesarTcpClient {
  private final String host;
  private int port = 10002;

  /**
   * 传入地址和端口.
   *
   * @param host IP.
   */
  public CaesarTcpClient(String host) {
    this.host = host;
  }

  /**
   * 开窗指令.
   *
   * @param scenario 预览预案Id.
   * @param seq      通道顺序.
   * @param txId     TxId.
   * @throws SkyLinkException .
   */
  public void openPanel(Integer scenario, int seq, long txId) throws SkyLinkException {
    if (scenario == null) {
      throw new SkyLinkException("Scenario is empty!");
    }
    String video = "VIDEO_" + String.format("%03d", seq - 1);
    String command = String.format("switch vppre videosource %d %s %d", scenario, video, txId);
    transferData(command);
  }

  /**
   * .
   */
  public void openPanel(Integer scenario, Map<Integer, Integer> seqTxMap) throws SkyLinkException {
    if (scenario == null) {
      throw new SkyLinkException("Scenario is empty!");
    }
    StringBuilder builder = new StringBuilder();
    builder.append(String.format("switch vppre videosource %d", scenario));
    for (Map.Entry<Integer, Integer> txSeq : seqTxMap.entrySet()) {
      int tx = txSeq.getValue();
      int seq = txSeq.getKey();
      String video = "VIDEO_" + String.format("%03d", seq - 1);
      builder.append(String.format(" %s %d", video, tx));
    }
    transferData(builder.toString());
  }

  /**
   * 关窗指令.
   *
   * @param scenario 预览预案Id.
   * @param seq      通道顺序.
   * @throws SkyLinkException .
   */
  public void closePanel(Integer scenario, int seq) throws SkyLinkException {
    if (scenario == null) {
      throw new SkyLinkException("Scenario is empty!");
    }
    String video = "VIDEO_" + String.format("%03d", seq - 1);
    String command = String.format("close vppre video %d %s 0000", scenario, video);
    transferData(command);
  }

  public void openMultiviewPreview(int rxId, int... txId) throws SkyLinkException {
    List<Integer> txIds = new ArrayList<>(4);
    for (int i = 0; i < txId.length; i++) {
      if (i > 3) {
        break;
      }
      txIds.add(txId[i]);
    }
    // 补0
    if (txIds.size() < 4) {
      for (int i = txIds.size(); i < 4; i++) {
        txIds.add(0);
      }
    }
    StringBuilder builder = new StringBuilder();
    builder.append(String.format("switch multiview preview %d", rxId));
    for (Integer id : txIds) {
      builder.append(String.format(" %d", id));
    }
    transferData(builder.toString());
  }

  /**
   * 实际发送指令的操作.
   *
   * @param command 指令.
   * @throws SkyLinkException .
   */
  public void transferData(String command) throws SkyLinkException {
    try (TcpClient cli = new TcpClient(this.host, this.port)) {
      cli.write(command.getBytes(StandardCharsets.UTF_8));
      byte[] bytes = new byte[16];
      int length = cli.read(bytes, 0, 16);
      if (length > 0) {
        String result = new String(bytes, 0, length, StandardCharsets.UTF_8);
        String succeed = "ok!";
        if (result.startsWith(succeed)) {
          return;
        }
      }
      throw new SkyLinkException("Snapshot open panel failed! Command:" + command);
    } catch (IOException e) {
      throw new SkyLinkException(e);
    }
  }

}
