package com.mediacomm.caesar.domain.kaito;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
public class KaitoVideoWall {
  private Integer id;
  private String name;
  private Collection<Screen> screens;
  private Collection<Layer> layers;


  @Data
  public static class Screen {
    private Integer x;
    private Integer y;
    private Integer width;
    private Integer height;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Layer {
    private Integer id;
    private String name;
    private Integer type; // 0:普通图层,1:凯中Tx,2:凯中Tx信号四宫格
    private Integer status;
    private Integer zorder; // 上下层
    private Window window;
    private Collection<Source> source;
  }

  @Data
  public static class Window {
    private Integer x;
    private Integer y;
    private Integer width;
    private Integer height;
  }

  @Data
  public static class Source {
    private Integer sourceType; // 0:无源,1:嗨动输入,2:嗨动IPC,3:凯撒Tx
    private Integer sourceId;
    private Integer streamId; // IPC流ID
  }
}
