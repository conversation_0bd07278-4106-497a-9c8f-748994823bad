package com.mediacomm.caesar.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.CaesarResponse;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWall;
import com.mediacomm.caesar.domain.vp7.Vp7VersionResponse;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.Version;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.LayoutRect;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.PreviewType;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * caesar的期望值.
 */
@Slf4j
public class InspectCaesarUtil {

  /**
   * 获取视频分辨率类型.
   *
   * @param asset Asset.
   * @return 视频分辨率类型.
   */
  public static VideoResolutionTypeEnum getVideoResolutionType(KvmAsset asset) {
    if (asset != null) {
      for (Property pro : asset.getProperties()) {
        if (pro.getPropertyKey().equals(PropertyKeyConst.CAESAR_TX_RESOLUTION_TYPE_KEY)) {
          return VideoResolutionTypeEnum.getVideoResolutionType(
                  Integer.parseInt(pro.getPropertyValue()));
        }
      }
    }
    return VideoResolutionTypeEnum.RESOLUTION_2K;
  }

  /**
   * 判断设备是否为4k.
   *
   * @param asset Asset.
   * @return 4K:true.
   */
  public static boolean is4k(KvmAsset asset) {
    VideoResolutionTypeEnum resolutionTypeEnum = getVideoResolutionType(asset);
    return resolutionTypeEnum == VideoResolutionTypeEnum.RESOLUTION_4K_30HZ
            || resolutionTypeEnum == VideoResolutionTypeEnum.RESOLUTION_4K_60HZ;
  }

  /**
   * 获取4画面Rx布局.
   *
   * @param value 0:全屏,1:左右分屏,2:四宫格,3:一大三小.
   * @return 4画面Rx布局,默认全屏.
   */
  public static LayoutData getFourScreenRxLayoutData(int value) {
    LayoutData layoutData = new LayoutData();
    layoutData.setWidth(1920);
    layoutData.setHeight(1080);
    switch (value) {
      case 1 -> {
        LayoutRect rect1 = new LayoutRect(1, 0, 0, 960, 1080);
        LayoutRect rect2 = new LayoutRect(2, 960, 0, 960, 1080);
        layoutData.setPanels(List.of(rect1, rect2));
      }
      case 2 -> {
        LayoutRect rect1 = new LayoutRect(1, 0, 0, 960, 540);
        LayoutRect rect2 = new LayoutRect(2, 960, 0, 960, 540);
        LayoutRect rect3 = new LayoutRect(3, 0, 540, 960, 540);
        LayoutRect rect4 = new LayoutRect(4, 960, 540, 960, 540);
        layoutData.setPanels(List.of(rect1, rect2, rect3, rect4));
      }
      case 3 -> {
        LayoutRect rect1 = new LayoutRect(1, 0, 0, 1440, 1080);
        LayoutRect rect2 = new LayoutRect(2, 1440, 0, 480, 360);
        LayoutRect rect3 = new LayoutRect(3, 1440, 360, 480, 360);
        LayoutRect rect4 = new LayoutRect(4, 1440, 720, 480, 360);
        layoutData.setPanels(List.of(rect1, rect2, rect3, rect4));
      }
      default -> {
        LayoutRect rect = new LayoutRect(1, 0, 0, 1920, 1080);
        layoutData.setPanels(Collections.singletonList(rect));
      }
    }
    return layoutData;
  }

  /**
   * 获取布局类型.
   *
   * @param layoutData .
   * @return .
   */
  public static int layoutType(LayoutData layoutData) {
    if (layoutData == null || CollectionUtil.isEmpty(layoutData.getPanels())) {
      return -1;
    }
    int size = layoutData.getPanels().size();
    if (size == 2) {
      return 1;
    } else if (size == 4) {
      boolean isSameFourScreen = true;
      for (LayoutRect panel : layoutData.getPanels()) {
        if (panel.getWidth() != layoutData.getWidth() / 2
            || panel.getHeight() != layoutData.getHeight() / 2) {
          isSameFourScreen = false;
          break;
        }
      }
      if (isSameFourScreen) {
        return 2;
      } else {
        return 3;
      }
    }
    return 0;
  }

  /**
   * 判断外设是否为双HDMI.
   *
   * @param asset Asset.
   * @return 根据设备properties中videoNumber值判断.
   */
  public static boolean isDoubleVideo(KvmAsset asset) {
    boolean redundant = false;
    if (asset != null) {
      for (Property property : asset.getProperties()) {
        if (property.getPropertyKey().equals("videoNumber")) {
          redundant = Integer.parseInt(property.getPropertyValue()) == 2;
          break;
        }
      }
    }
    return redundant;
  }

  /**
   * 视频分辨率类型枚举.
   */
  public enum VideoResolutionTypeEnum {
    RESOLUTION_2K(0),
    RESOLUTION_4K_30HZ(1),
    RESOLUTION_4K_60HZ(2);

    private final int value;

    VideoResolutionTypeEnum(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }

    /**
     * 根据给定的值返回对应的视频分辨率类型.
     * 如果给定的值在视频分辨率类型枚举中不存在，则返回2K分辨率类型.
     *
     * @param value 视频分辨率类型对应的值.
     * @return 对应的视频分辨率类型，如果不存在则返回RESOLUTION_2K.
     */
    public static VideoResolutionTypeEnum getVideoResolutionType(int value) {
      for (VideoResolutionTypeEnum type : values()) {
        if (type.getValue() == value) {
          return type;
        }
      }
      return RESOLUTION_2K;
    }
  }

  /**
   * 根据给定的值返回特殊扩展类型.
   * 1: 融合通信终端.
   * 2: 四画面RX.
   * 3: 嗨动单画面子卡.
   * 4: 嗨动四画面子卡.
   * 5: VP7.
   * 6: 新预览终端.
   *
   * @param value 值.
   * @return 对应的特殊扩展类型.
   */
  public static DeviceType getSpecialExtType(int value) {
    switch (value) {
      case 1 -> {
        return DeviceType.CAESAR_DECODE_DEVICE;
      }
      case 2 -> {
        return DeviceType.CAESAR_FOUR_SCREEN_RX;
      }
      case 3 -> {
        return DeviceType.CAESAR_KAITO_SECOND_CARD;
      }
      case 5 -> {
        return DeviceType.CAESAR_VP7;
      }
      case 6 -> {
        return DeviceType.CAESAR_R2P4F;
      }
      default -> {
        return DeviceType.CAESAR_RX;
      }
    }
  }

  /**
   * 根据设备类型获取预览策略模式.设备类型获取预览策略模式.
   *
   * @param deviceType 设备类型.
   * @return 预览策略模式.
   */
  public static CaesarPreviewStrategyMode getStrategyModeByDeviceType(DeviceType deviceType) {
    return switch (deviceType) {
      case CAESAR_FOUR_SCREEN_RX -> CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX;
      case CAESAR_R1C8_VIDEO_WALL -> CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6;
      case CAESAR_R2P4F -> CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F;
      default -> CaesarPreviewStrategyMode.NONE;
    };
  }

  /**
   * 根据预览策略模式判断是否支持高分辨率.
   *
   * @param strategyMode 预览策略模式.
   * @return .
   */
  public static boolean isSupportHighResolution(CaesarPreviewStrategyMode strategyMode) {
    return strategyMode == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX
            || strategyMode == CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F;
  }

  /**
   * 根据视频墙判断是否支持高分辨率.
   *
   * @param videoWall 视频墙.
   * @return .
   */
  public static boolean isSupportHighResolution(KvmVideoWall videoWall) {
    return Objects.equals(videoWall.getDeviceModel(), DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())
            || Objects.equals(videoWall.getDeviceModel(), DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId());
  }

    /**
   * 根据设备类型生成预览关联通道集合.
   *
   * @return .
   */
  public static Map<String, Collection<KvmPreviewAsso>> buildPreviewChannelsByType(
      Collection<KvmAssetVo> assets) {
    Map<String, Collection<KvmPreviewAsso>> result = new HashMap<>();
    assets.forEach(asset -> {
      Collection<KvmPreviewAsso> channels = buildPreviewChannels(asset);
      if (CollectionUtil.isNotEmpty(channels)) {
        result.put(asset.getAssetId(), channels);
      }
    });
    return result;
  }

  /**
   * 生成预览关联通道集合.
   *
   * @return .
   */
  public static Collection<KvmPreviewAsso> buildPreviewChannels(KvmAssetVo previewAsset) {
    DeviceType deviceType = DeviceType.valueOf(previewAsset.getDeviceType());
    Collection<KvmPreviewAsso> channels = new ArrayList<>();
    if (StringUtils.isEmpty(previewAsset.getDeviceIp())) {
      return channels;
    }
    switch (deviceType) {
      case CAESAR_FOUR_SCREEN_RX -> {
        // 0->主码流,1->子码流.
        int streamType = Integer.parseInt(Property
                .findValueByKey(previewAsset.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "1"));
        String previewAddress = Property.findValueByKey(previewAsset.getCollectorProperties(),
                PropertyKeyConst.PREVIEW_ADDRESS, "");
        if (StringUtils.isNotEmpty(previewAddress)) {
          String encoderUrl = String.format("http://%s:8080/snapshot/%d/file.jpg", previewAddress, streamType);
          for (int i = 0; i < 4; i++) {
            KvmPreviewAsso asso = new KvmPreviewAsso();
            asso.setRxId(previewAsset.getAssetId());
            asso.setSeq(i);
            asso.setUrl(encoderUrl);
            channels.add(asso);
          }
        }
      }
      case CAESAR_R2P4F -> {
        for (int i = 0; i < 16; i ++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          // 应用同R1C8的预览轮巡策略时，需要使用电视墙Id分组，以保证4KTx能正常被开窗
          asso.setWallId(previewAsset.getDeviceId());
          asso.setRxId(previewAsset.getAssetId());
          asso.setSeq(i);
          asso.setUrl(String.format("http://%s:8080/mediacomm/download-image", previewAsset.getDeviceIp()));
          channels.add(asso);
        }
      }
      default -> {
        return channels;
      }
    }
    return channels;
  }

  /**
   * 获取凯撒主机的扩展类型.
   *
   * @param properties .
   * @return .
   */
  public static DeviceType getDeviceTypeFromCollectorProperties(List<Property> properties) {
    String deviceType = Property.findValueByKey(properties, "extendModel", "");
    if (StringUtils.isNotEmpty(deviceType)) {
      return DeviceType.valueOf(deviceType);
    }
    return DeviceType.CAESAR;
  }

  /**
   * 获取凯撒主机的外设的组合hardcode.
   *
   * @return .
   */
  public static String getCaesarDeviceHardCode(String masterId, String sn, Integer deviceId) {
    return String.format("%s.%s.%d", masterId, sn, deviceId);
  }

  /**
   * 获取Kaito的设备硬编码.
   *
   * @param masterId .
   * @param groupId .
   * @param type input || ipc.
   * @param deviceId .
   * @return .
   */
  public static String getKaitoDeviceHardCode(String masterId, Integer groupId, String type,
                                              Integer deviceId) {
    return String.format("%s.%d.%s.%d", masterId, groupId, type, deviceId);
  }

  public static String getCaesarWallUniqueSearchKey(String masterId, CaesarVideoWall caesarWall) {
    return String.join(".", masterId, caesarWall.getDeviceType().getDeviceType(),
            String.valueOf(caesarWall.getId()));
  }

  /**
   * 根据 KvmAsset 构建 KaitoVideoWall 的 Source 对象.
   *
   * @param asset 表示设备资产的信息，包含设备ID、设备型号等.
   * @return 返回构建好的 KaitoVideoWall.Source 对象，该对象设置了源的ID、类型和流ID（如果适用）。
   */
  public static KaitoVideoWall.Source buildKaitoSource(KvmAsset asset) {
    KaitoVideoWall.Source source = new KaitoVideoWall.Source();
    source.setSourceId(asset.getDeviceId()); // 设置源的ID为设备的ID
    // 根据设备型号设置源的类型，并根据需要设置流ID
    if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
      source.setSourceType(1); // 设备类型为KAITO02_INPUT
    } else if (Objects.equals(asset.getDeviceModel(),
        DeviceType.KAITO02_IPC.getDeviceTypeId())) {
      source.setSourceType(2); // 设备类型为KAITO02_IPC
      // 从设备属性中解析主流ID，并设置流ID
      String streamType = Property.findValueByKey(
          asset.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "mainStreamId");
      int streamId = Integer.parseInt(
          Property.findValueByKey(
              asset.getProperties(), streamType, "0"));
      source.setStreamId(streamId);
    } else if (Objects.equals(asset.getDeviceModel(),
        DeviceType.CAESAR_TX.getDeviceTypeId())) {
      source.setSourceType(3); // 设备类型为CAESAR_TX
    } else {
      source.setSourceType(0); // 对于未知设备型号，设置源类型为无源
    }
    return source;
  }

  /**
   * 根据凯撒接口错误值返回指定错误码.
   *
   * @param response .
   * @return .
   */
  public static String getResultFromApiStatus(CaesarResponse response) {
    return switch (response.getStatus()) {
      case -1 -> Result.failureStr(response.getDescr(), ResponseCode.UNKNOWN_ERROR_13006);
      case -2 -> Result.failureStr(response.getDescr(), ResponseCode.EX_FAILURE_400);
      case -3 -> Result.failureStr(response.getDescr(), ResponseCode.OUT_OF_LIMIT_13005);
      default -> Result.failureStr(response.getDescr(), ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * 获取关联主机ID, 不存在时返回主机的自身ID.
   */
  public static String getRelatedMasterId(KvmMaster master) {
    return Property.findValueByKey(master.getCollectorProperties(), "associateHost", master.getMasterId());
  }

  /**
   * 获取来自大屏类型的预览通道.
   *
   * @param videoWall .
   * @param expectType 期望返回的预览方式，图片或码流.
   * @return .
   */
  public static Collection<KvmPreviewAsso> buildPreviewChannelsByType(KvmVideoWall videoWall,
                                                                       PreviewType expectType) {
    Collection<KvmPreviewAsso> channels = new ArrayList<>();
    if (videoWall != null) {
      String previewAddress = Property.findValueByKey(videoWall.getCollectorProperties(),
              PropertyKeyConst.PREVIEW_ADDRESS, "");
      String previewModel = Property.findValueByKey(videoWall.getCollectorProperties(),
              PropertyKeyConst.PREVIEW_MODEL, "");
      PreviewType previewType = PreviewType.valueOf(Property.findValueByKey(
              videoWall.getCollectorProperties(), PropertyKeyConst.PREVIEW_TYPE, "NONE"));
      if (previewType != PreviewType.NONE && StringUtils.isNotEmpty(previewAddress)
              && StringUtils.isNotEmpty(previewModel)) {
        String vp6Url = "";
        if (expectType == PreviewType.SNAPSHOT) {
          vp6Url = "http://%s:8080/snapshot/%d/0/file.jpg";
        } else if (expectType == PreviewType.RTSP) {
          vp6Url = "rtsp://%s:554/%d";
        }
        for (int i = 0; i < 8; i++) {
          KvmPreviewAsso asso = new KvmPreviewAsso();
          asso.setWallId(videoWall.getWallId());
          asso.setSeq(i + 1);
          asso.setUrl(String.format(vp6Url, previewAddress, i));
          channels.add(asso);
        }
      }
    }
    return channels;
  }

  public static boolean isAvailableVp7(KvmAsset vp7) {
    return vp7 != null && Objects.equals(vp7.getDeviceModel(), DeviceType.CAESAR_VP7.getDeviceTypeId())
            && vp7.getDeviceIp() != null && !vp7.getDeviceIp().isEmpty();
  }

  public static List<Version> toVersion(Vp7VersionResponse response) {
    Class<?> responseClass = response.getClass();
    List<Version> versions = new ArrayList<>();
    for (Field field : responseClass.getDeclaredFields()) {
      field.setAccessible(true);
      String versionName = field.getName();
      String version = "0";
      String date = "0";
      String versionAndDate = "";
      try {
        versionAndDate = (String) field.get(response);
      } catch (IllegalAccessException e) {
        log.error(e.getMessage(), e);
      }
      if (StringUtils.isNotEmpty(versionAndDate)) {
        String[] versionAndDateArray = versionAndDate.split("-");
        if (versionAndDateArray.length == 2) {
          version = versionAndDateArray[0];
          date = DateUtil.format(DateUtil.parse(versionAndDateArray[1], "yyMMdd"), "yyyy.MM.dd");
        }
      }
      versions.add(new Version(versionName, version, date));
    }
    return versions;
  }
}
