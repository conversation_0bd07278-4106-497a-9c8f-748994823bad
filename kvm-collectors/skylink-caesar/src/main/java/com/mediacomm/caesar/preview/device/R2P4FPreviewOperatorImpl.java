package com.mediacomm.caesar.preview.device;

import cn.hutool.core.util.ZipUtil;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.CaesarTxId;
import com.mediacomm.caesar.domain.vp7.R2P4FResponse;
import com.mediacomm.caesar.domain.vp7.R2P4FStatus;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.task.SkyLinkTaskPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.nio.file.StandardCopyOption.ATOMIC_MOVE;

/**
 * .
 */
@Slf4j
public class R2P4FPreviewOperatorImpl implements PreviewDeviceOperator {
  private final SkyLinkTaskPool taskRunner;
  private final DeviceDataGetter deviceDataGetter;
  private final CaesarPreviewStrategyMode strategyMode =
          CaesarPreviewStrategyMode.PREVIEW_STRATEGY_R2P4F;
  private PreviewInfoOperator previewMap;

  private CloseableHttpClient httpClient;

  public R2P4FPreviewOperatorImpl(SkyLinkTaskPool taskRunner,
                                  DeviceDataGetter deviceDataGetter, PreviewInfoOperator previewMap) {
    this.taskRunner = taskRunner;
    this.deviceDataGetter = deviceDataGetter;
    this.previewMap = previewMap;
    // 配置同步客户端的连接池
    PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
    cm.setMaxTotal(50);      // 最大连接数
    cm.setDefaultMaxPerRoute(8); // 每个路由（主机）的最大连接数
    // 配置请求的默认超时时间
    RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(5000) // 连接超时
            .setSocketTimeout(5000)  // 读取数据超时
            .setConnectionRequestTimeout(5000) // 从连接池获取连接的超时
            .build();
    httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(requestConfig).build();
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanel(Map<String, KvmPreviewAsso> txChannelMap) {
    return null;
  }

  @Override
  public CompletableFuture<Boolean> closePanels(Collection<KvmPreviewAsso> channels) {
    return null;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanelAndGetPreview(Map<String, KvmPreviewAsso> txChannelMap,
                                                                        Map<String, String> txSavePathMap,
                                                                        Collection<KvmPreviewAsso> unusedChannels) {
    // 根据rx id分组
    Map<String, Map<String, KvmPreviewAsso>> group = txChannelMap.entrySet().stream().collect(
            Collectors.groupingBy(item -> item.getValue().getRxId(),
                    Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
    result.complete(new HashMap<>());
    for (Map.Entry<String, Map<String, KvmPreviewAsso>> stringMapEntry : group.entrySet()) {
      KvmAsset r2p4f = deviceDataGetter.getExtendDevice(stringMapEntry.getKey());
      if (r2p4f != null && StringUtils.isNotEmpty(r2p4f.getDeviceIp())) {
        // key: assetId，value: deviceId
        Map<String, Integer> txAssetIdAndDeviceIds = stringMapEntry.getValue().keySet().stream()
                .map(deviceDataGetter::getExtendDevice).filter(Objects::nonNull)
                .collect(Collectors.toMap(KvmAsset::getAssetId, KvmAsset::getDeviceId));
        result = result.thenCombine(taskRunner.addAsyncTask(
                        () -> openPanelAndGetPreviewImpl(r2p4f, txAssetIdAndDeviceIds, txSavePathMap)),
                (lhs, rhs) -> {
                  Map<String, Boolean> newResult = new HashMap<>(lhs);
                  newResult.putAll(rhs);
                  return newResult;
                }
        );
      }

    }
    return result;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> getPreview(Map<String, String> txSavePathMap) {
    return null;
  }

  @Override
  public CaesarPreviewStrategyMode getPreviewStrategyMode() {
    return this.strategyMode;
  }

  @Override
  public void close() throws IOException {
    httpClient.close();
  }

  private Map<String, Boolean> openPanelAndGetPreviewImpl(KvmAsset r2p4f, Map<String, Integer> previewReqParam,
                                                          Map<String, String> txSavePathMap) {
    Map<String, Boolean> result = new HashMap<>();
    previewReqParam.keySet().forEach(assetId -> result.put(assetId, false));
    try {
      R2P4FResponse response = sendPreviewRequest(r2p4f, previewReqParam);
      if (response == null || response.getCode() != 0) {
        return result;
      }
      // 过滤有效 TxId
      Collection<CaesarTxId> ids = filterValidTxIds(response, previewReqParam);
      if (ids.isEmpty()) {
        log.error("R2P4F {} Preview openPanelAndGetPreviewImpl success, but the tx status is abnormal! response: {}",
                r2p4f.getDeviceIp(), response);
        return result;
      }
      // 可用资源变化
      boolean isLimit = response.getChannel() != deviceDataGetter.getPreviewChannels(r2p4f.getAssetId()).size();
      if (isLimit) {
        // deviceDataGetter创建的预览通道被PreviewMap中的pollingPreviewChannels引用
        // 所以仅更新的deviceDataGetter中的kvmAssetsPreviewAssos的话，轮巡线程中的预览通道的仍然是旧的
        deviceDataGetter.updatePreviewChannel(r2p4f.getAssetId(), response.getChannel());
        Collection<KvmPreviewAsso> oldChannels = new ArrayList<>(previewMap.getPollingPreviewChannels());
        oldChannels.removeIf(item -> item.getRxId().equals(r2p4f.getAssetId()));
        oldChannels.addAll(deviceDataGetter.getPreviewChannels(r2p4f.getAssetId()));
        previewMap.setPollingPreviewChannels(oldChannels);
      }
      // 下载并处理图片
      downloadAndProcessImages(r2p4f, previewReqParam, txSavePathMap, result, response.getTimestamp());
    } catch (Exception e) {
      log.error("R2P4F Preview openPanelAndGetPreviewImpl error!", e);
    }
    return result;
  }

  /**
   * 发送预览请求并返回响应
   * @param r2p4f 设备资产信息
   * @param previewReqParam 预览请求参数
   * @return R2P4F 响应对象
   */
  private R2P4FResponse sendPreviewRequest(KvmAsset r2p4f, Map<String, Integer> previewReqParam) {
    String openUrl = String.format("http://%s:8080/mediacomm/preview-request-image", r2p4f.getDeviceIp());
    Collection<CaesarTxId> ids = previewReqParam.values().stream().map(CaesarTxId::new).toList();
    return JsonUtils.decode(executeHttpPost(openUrl, JsonUtils.encode(ids)), R2P4FResponse.class);
  }

  private String executeHttpPost(String url, String jsonBody) {
    HttpPost post = new HttpPost(url);
    StringEntity strEntity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
    post.setEntity(strEntity);
    try (CloseableHttpResponse res = httpClient.execute(post)) {
      int statusCode = res.getStatusLine().getStatusCode();
      HttpEntity entity = res.getEntity();
      if (statusCode != 200) {
        EntityUtils.consume(entity);
        return null;
      }
      return entity != null ? EntityUtils.toString(entity) : null;
    } catch (IOException e) {
      log.error("Failed to execute HTTP POST request: {}", e.getMessage());
      return null;
    }
  }

  /**
   * 过滤有效的 TxId
   * @param response R2P4F 响应对象
   * @param previewReqParam 预览请求参数
   * @return 有效的 TxId 集合
   */
  private Collection<CaesarTxId> filterValidTxIds(R2P4FResponse response, Map<String, Integer> previewReqParam) {
    return previewReqParam.values().stream()
            .map(CaesarTxId::new)
            .filter(item -> {
              for (R2P4FResponse.TxStatus txStatus : response.getResult()) {
                if (txStatus.getTxid() == item.getTxid()) {
                  R2P4FStatus status = R2P4FStatus.getByCode(txStatus.getStatus());
                  if (status == R2P4FStatus.SUCCESS) {
                    return true;
                  } else {
                    log.debug("R2P4F Preview openPanelAndGetPreviewImpl success, " +
                                    "but the tx status is abnormal! response: {} status: {}", response, status);
                    return false;
                  }
                }
              }
              return false;
            }).toList();
  }

  /**
   * 下载并处理图片
   * @param r2p4f 设备资产信息
   * @param previewReqParam 预览请求参数
   * @param txSavePathMap 图片保存路径映射
   * @param result 结果映射
   */
  private void downloadAndProcessImages(KvmAsset r2p4f, Map<String, Integer> previewReqParam,
                                        Map<String, String> txSavePathMap, Map<String, Boolean> result, long timestamp) {
    String tempPath = "/tmp/snapshot/caesar/temp";
    String getImageZipUrl = String.format("http://%s:8080/mediacomm/download-image?timestamp=%d",
            r2p4f.getDeviceIp(), timestamp);
    File parentFile = new File("/tmp/snapshot/caesar", "temp");
    try {
      if (!parentFile.exists() && !parentFile.mkdirs()) {
        throw new IOException("Can not creates the directory");
      }
      HttpResponse zipFileRes = executeHttpGet(getImageZipUrl);
      if (zipFileRes.getStatusLine().getStatusCode() != 200) {
        log.error("Get R2P4F image failed! {}", zipFileRes.getStatusLine().getStatusCode());
        return;
      }
      HttpEntity zipFileEntity = zipFileRes.getEntity();
      if (zipFileEntity == null) {
        log.error("R2P4F response is empty! {}", getImageZipUrl);
        return;
      }
      File zipFile = File.createTempFile("temp", ".zip", parentFile);
      try (InputStream is = zipFileEntity.getContent()) {
        FileUtils.copyInputStreamToFile(is, zipFile);
        ZipUtil.unzip(zipFile);
        for (Map.Entry<String, Integer> entry : previewReqParam.entrySet()) {
          String savePath = txSavePathMap.get(entry.getKey());
          if (StringUtils.isNotEmpty(savePath)) {
            String txIdFileName = String.format("%s/%s/%d.jpg", tempPath, FilenameUtils.getBaseName(zipFile.getName()), entry.getValue());
            File txSnapshotSrc = new File(txIdFileName);
            if (txSnapshotSrc.exists()) {
              Files.move(txSnapshotSrc.toPath(), Paths.get(savePath), ATOMIC_MOVE);
              result.put(entry.getKey(), true);
            }
          }
        }
      } catch (IOException e) {
        log.error("R2P4F download failed! {}", getImageZipUrl, e);
      } finally {
        if (zipFile.exists()) {
          Files.delete(zipFile.toPath());
        }
        File dir = new File(zipFile.getParent() + "/" + FilenameUtils.getBaseName(zipFile.getName()));
        FileUtils.deleteDirectory(dir);
      }
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      log.error("Failed to download and process images for R2P4F {}: {}", r2p4f.getDeviceIp(), e.getMessage(), e);
    }
  }

  private HttpResponse executeHttpGet(String url) throws Exception {
    HttpGet get = new HttpGet(url);
    return httpClient.execute(get);
  }
}
