package com.mediacomm.caesar.controller;

import com.mediacomm.caesar.domain.vp7.Up7UpgradeCancelResponse;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeCancelParam;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeParam;
import com.mediacomm.caesar.domain.vp7.Vp7UpgradeResponse;
import com.mediacomm.caesar.domain.vp7.Vp7VersionResponse;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.dao.PeripheralUpgradePackage;
import com.mediacomm.entity.dao.PeripheralUpgradeTask;
import com.mediacomm.entity.dao.Picture;
import com.mediacomm.entity.dao.VideoWallBanner;
import com.mediacomm.entity.dao.VideoWallBanner.Logo;
import com.mediacomm.entity.dto.CommonsMultipartFileDto;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.PeripheralUpgradePackageService;
import com.mediacomm.system.service.PeripheralUpgradeTaskService;
import com.mediacomm.system.service.PictureService;
import com.mediacomm.system.service.VideoWallBannerService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.UpgradeTaskStatus;
import com.mediacomm.util.task.SkyLinkTaskPool;
import com.mediacomm.system.variable.sysenum.DeviceType;
import feign.FeignException;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
@Slf4j
public class Vp7CmdServer {
  @Resource
  private Vp7FeignClientApi cli;
  @Resource
  private KvmAssetService assetService;
  @Resource
  private PictureService picService;
  @Resource
  private VideoWallBannerService bannerService;
  @Resource
  private SkyLinkTaskPool taskPool;
  @Resource
  private PeripheralUpgradeTaskService pts;
  @Resource
  private PeripheralUpgradePackageService packageService;

  private static final String URI_FORMAT = "http://%s:8080";
  private static final String IMAGE_BOTTOM_FULL = "/tmp/vp7BottomImage.png";
  private static final String IMAGE_LOGO_FULL = "/tmp/vp7LogoImage.png";

  /**
   * 上传VP7底图.
   *
   * @param wall VP7视频墙ID.
   * @param hash Picture hash.
   * @return Result.
   */
  public String uploadBottomImage(KvmVideoWall wall, long hash) {
    for (String ip : getVp7Ips(wall)) {
      Picture pic = picService.getById(hash);
      if (pic != null) {
        URI uri = URI.create(String.format(URI_FORMAT, ip));
        File file = new File(IMAGE_BOTTOM_FULL);
        try (FileOutputStream os = new FileOutputStream(file)) {
          os.write(pic.getContent());
        } catch (Exception e) {
          log.error("Create vp7 bottom image failed!", e);
          return Result.failureStr("Create bottom image failed.", ResponseCode.EX_FAILURE_500);
        }
        try {
          cli.uploadBgImage(uri, new CommonsMultipartFileDto(file), String.valueOf(pic.getId()));
        } catch (FeignException e) {
          log.error("Upload {} vp7 bottom image failed!", ip, e);
          return Result.failureStr("Upload bottom image failed.", ResponseCode.UNKNOWN_ERROR_13006);
        }
      }
    }
    return Result.okStr();
  }

  /**
   * VP7Banner配置.
   */
  public String setBannerConfig(KvmVideoWall wall, VideoWallBanner banner) {
    Set<String> decodeIps = getVp7Ips(wall);
    List<CompletableFuture<Map<String, Boolean>>> futures = new ArrayList<>(decodeIps.size());
    for (String ip : decodeIps) {
      URI uri = URI.create(String.format(URI_FORMAT, ip));
      banner.getContent().setId(banner.getId());
      bannerLogoUpload(uri, banner);
      String ntpAddr = Property.findValueByKey(
              wall.getCollectorProperties(), PropertyKeyConst.NTP_ADDR, null);
      String ntpPort = Property.findValueByKey(
              wall.getCollectorProperties(), PropertyKeyConst.NTP_PORT, null);
      if (StringUtils.isNotBlank(ntpAddr) && StringUtils.isNotBlank(ntpPort)) {
        Collection<VideoWallBanner.Clock> clocks = banner.getContent().getClockInfoArray();
        for (VideoWallBanner.Clock clock : clocks) {
          clock.setSource(String.format("%s:%s", ntpAddr, ntpPort));
        }
      }
      futures.add(taskPool.addAsyncTask(() -> {
        Map<String, Boolean> result = new HashMap<>();
        try {
          cli.setBannerConfig(uri, banner.getContent());
          result.put(ip, Boolean.TRUE);
        } catch (FeignException e) {
          log.error("Set banner config failed for IP: {}", ip, e);
          result.put(ip, Boolean.FALSE);
        }
        return result;
      }));
    }
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    CompletableFuture<Map<String, Boolean>> combinedFuture = allFutures.thenApply(v -> {
      Map<String, Boolean> combinedResult = new HashMap<>();
      for (CompletableFuture<Map<String, Boolean>> future : futures) {
        combinedResult.putAll(future.join());
      }
      return combinedResult;
    });
    boolean setBannerSuccess = true;
    String failedAddr = "";
    try {
      Map<String, Boolean> results = combinedFuture.get();
      for (Map.Entry<String, Boolean> entry : results.entrySet()) {
        if (!entry.getValue()) {
          setBannerSuccess = false;
          failedAddr = entry.getKey();
          break;
        }
      }
    } catch (Exception e) {
      log.error("Error waiting for banner configuration tasks to complete", e);
      return Result.failureStr(e.getMessage(), ResponseCode.EX_FAILURE_500);
    }
    if (setBannerSuccess) {
      bannerService.updateById(banner);
      return Result.okStr();
    }
    return Result.failureStr(String.format("The video wall %s banner setup failed.", failedAddr), ResponseCode.UNKNOWN_ERROR_13006);
  }


  /**
   * 从VP7上获取banner配置.
   * 一切数据以云视为准，如果跟设备的条幅数据不一致，要求从云视上下发配置vp7的条幅数据.
   */
  public String getBannerConfig(KvmVideoWall wall) {
    return Result.okStr(bannerService.listByVideoWallId(wall.getWallId()).stream().filter(VideoWallBanner::isStatus).collect(Collectors.toList()));
  }

  public boolean startUpgrade(KvmAsset vp7, String packageUrl, String auth, int taskId) {
    Vp7UpgradeParam requestBody = new Vp7UpgradeParam(packageUrl, auth, taskId);
    URI uri = URI.create(String.format(URI_FORMAT, vp7.getDeviceIp()));
    PeripheralUpgradeTask task = pts.getById(taskId);
    if (task == null) {
      log.error("Not found upgrade task.");
      return Boolean.FALSE;
    }
    try {
      Vp7UpgradeResponse response = cli.startOta(uri, requestBody);
      updateOtaStatus(vp7, response, task);
    } catch (FeignException e) {
      updateUpgradeStatus(task, UpgradeTaskStatus.FAILED, e.getMessage());
      log.error("Upgrade vp7 device {} {} failed.", vp7.getName(), vp7.getDeviceIp(), e);
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }

  public boolean cancelUpgrade(KvmAsset vp7, int taskId) {
    PeripheralUpgradeTask task = pts.getById(taskId);
    if (task == null) {
      return Boolean.FALSE;
    }
    try {
      URI uri = URI.create(String.format(URI_FORMAT, vp7.getDeviceIp()));
      Vp7UpgradeCancelParam cancelParam = new Vp7UpgradeCancelParam(taskId);
      Up7UpgradeCancelResponse response = cli.cancelOta(uri, cancelParam);
      if (response.getState() == Vp7UpgradeResponse.State.CANCELLED) {
        task.setEndTime(System.currentTimeMillis());
        updateUpgradeStatus(task, UpgradeTaskStatus.CANCEL, "The task has been canceled.");
        return Boolean.TRUE;
      }
    } catch (FeignException e) {
      log.error("Cancel vp7 device {} {} upgrade failed.", vp7.getName(), vp7.getDeviceIp(), e);
      updateUpgradeStatus(task, UpgradeTaskStatus.TIMEOUT, e.getMessage());
    }
    return Boolean.FALSE;
  }

  public void getVp7VersionAndUpdateDevice(Collection<KvmAsset> vp7s) {
    vp7s.forEach(vp7 -> {
      if (InspectCaesarUtil.isAvailableVp7(vp7)) {
        Thread.ofVirtual().name("getVp7VersionAndUpdateDevice-" + vp7.getVersion()).start(() -> {
          URI uri = URI.create(String.format(URI_FORMAT, vp7.getDeviceIp()));
          Vp7VersionResponse res = cli.getVersion(uri);
          if (res != null) {
            vp7.setVersion(InspectCaesarUtil.toVersion(res));
            assetService.updateById(vp7);
          } else {
            log.error("Get vp7 device {} {} version failed.", vp7.getName(), vp7.getDeviceIp());
          }
        });
      }
    });
  }

  public PeripheralUpgradeTask getVp7VersionAndUpdateTask(KvmAsset vp7, int taskId) {
    PeripheralUpgradeTask task = pts.getById(taskId);
    if (task == null) {
      return null;
    }
    PeripheralUpgradePackage vp7Package = packageService.getById(task.getPackageId());
    if (vp7Package == null) {
      return task;
    }
    try {
      URI uri = URI.create(String.format(URI_FORMAT, vp7.getDeviceIp()));
      Vp7VersionResponse res = cli.getVersion(uri);
      if (res != null) {
        updateUpgradeStatus(task, UpgradeTaskStatus.SUCCESS, "");
        vp7.setVersion(InspectCaesarUtil.toVersion(res));
        assetService.updateById(vp7);
      }
    } catch (FeignException e) {
      log.error("Get vp7 device {} {} version failed.", vp7.getName(), vp7.getDeviceIp(), e);
    }
    return task;
  }

  private void bannerLogoUpload(URI uri, VideoWallBanner banner) {
    for (Logo logo : banner.getContent().getLogoInfoArray()) {
      if (logo.isEnable()) {
        Picture pic = picService.getById(Long.valueOf(logo.getLogo()));
        if (pic != null) {
          File file = new File(IMAGE_LOGO_FULL);
          try (FileOutputStream os = new FileOutputStream(file)) {
            os.write(pic.getContent());
          } catch (Exception e) {
            log.error("Create vp7 logo image failed!", e);
          }
          log.info("Upload vp7 logo image.");
          Thread.ofVirtual().name(uri.getHost() + " banner logo upload.").start(() ->
                  cli.uploadLogo(uri, new CommonsMultipartFileDto(file), String.valueOf(pic.getId())));
        }
      }
    }
  }

  private Set<String> getVp7Ips(KvmVideoWall wall) {
    Set<String> decodeIps = new HashSet<>();
    for (KvmVideoWallDecoder decoder : wall.getDecoders()) {
      KvmAssetVo vp7 = assetService.oneByDeviceIdAndModelId(decoder.getDeviceId(),
              DeviceType.CAESAR_VP7.getDeviceTypeId(), wall.getMasterId());
      if (vp7 != null && StringUtils.isNotBlank(vp7.getDeviceIp())) {
        decodeIps.add(vp7.getDeviceIp());
      } else {
        log.error("Vp7 {} ip is null.", decoder.getDeviceId());
      }
    }
    return decodeIps;
  }

  private void updateOtaStatus(KvmAsset vp7, Vp7UpgradeResponse response, PeripheralUpgradeTask task) {
    if (response.getCurrentState() == Vp7UpgradeResponse.State.REJECTED) {
      updateUpgradeStatus(task, UpgradeTaskStatus.FAILED, "The upgrade was denied.");
    }
    taskPool.addTask(() -> {
      try {
        Thread.sleep(response.getNextPollDelay() <= 0 ? 1000 : response.getNextPollDelay() * 1000L);
        a: while (true) {
          Vp7UpgradeResponse upgradeStatus = cli.getOtaStatus(URI.create(String.format(URI_FORMAT, vp7.getDeviceIp())));
          if (upgradeStatus.getTaskId() != task.getId()) {
            updateUpgradeStatus(task, UpgradeTaskStatus.CANCEL, "The upgrade task was cancelled.");
            break;
          }
          switch (upgradeStatus.getCurrentState()) {
            case FAILED, REJECTED -> {
              updateUpgradeStatus(task, UpgradeTaskStatus.FAILED, upgradeStatus.getStatusDetail());
              break a;
            }
            case ACCEPTED, VERIFYING ->
                    updateUpgradeStatus(task, UpgradeTaskStatus.WAITING, upgradeStatus.getProgressPercent());
            case DOWNLOADING, INSTALLING, REBOOTING ->
                    updateUpgradeStatus(task, UpgradeTaskStatus.UPGRADING, upgradeStatus.getProgressPercent());
            case COMPLETED -> {
              task.setEndTime(System.currentTimeMillis());
              updateUpgradeStatus(task, UpgradeTaskStatus.SUCCESS, upgradeStatus.getProgressPercent());
              vp7.setVersion(task.getPackageVersion());
              assetService.updateById(vp7);
              break a;
            } default -> {
              updateUpgradeStatus(task, UpgradeTaskStatus.FAILED, "Unknown upgrade status.");
              log.error("Unknown upgrade {} {} status.", vp7.getName(), vp7.getDeviceIp());
              break a;
            }
          }
          Thread.sleep(upgradeStatus.getNextPollDelay() <= 0 ? 1000 : upgradeStatus.getNextPollDelay() * 1000L);
        }
      } catch (Exception e) {
        updateUpgradeStatus(task, UpgradeTaskStatus.TIMEOUT, e.getMessage());
        log.error("Get vp7 upgrade status failed!", e);
      }
    });
  }

  /**
   * 更新任务状态，带有进度。
   */
  private void updateUpgradeStatus(PeripheralUpgradeTask task, UpgradeTaskStatus status, int progress) {
    task.setProgress(progress);
    // 调用新的、统一的核心方法
    updateTaskInDatabase(task, status);
  }

  /**
   * 更新任务状态，带有错误信息。
   */
  private void updateUpgradeStatus(PeripheralUpgradeTask task, UpgradeTaskStatus status, String errorMsg) {
    task.setErrorMessage(errorMsg);
    task.setEndTime(System.currentTimeMillis());
    // 调用新的、统一的核心方法
    updateTaskInDatabase(task, status);
  }

  /**
   * 这是一个新的、不重载的私有方法，负责所有数据库更新的公共逻辑。
   * 它被上面两个方法明确调用，PMD 绝对不会再对它产生误判。
   */
  private void updateTaskInDatabase(PeripheralUpgradeTask task, UpgradeTaskStatus status) {
    task.setStatus(status.getCode());
    pts.updateById(task);
  }
}
