package com.mediacomm.caesar.util;

import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.PreviewType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * .
 */
@Component
@Getter
@Slf4j
public class CaesarDbServiceUtil {
  @Resource
  private KvmVideoWallService videoWallService;
  @Resource
  private KvmAssetService kvmAssetService;
  @Resource
  private KvmMasterService masterService;
  @Resource
  private RedisUtil redisUtil;

  /**
   * 根据要求返回需要的凯撒电视墙数据.
   *
   * @param ignoreIncompleteVideoWall 是否不要不是全部光纤接入的电视墙.
   * @return 符合要求的预览电视墙.
   */
  public List<KvmVideoWall> getOnlinePreviewWalls(boolean ignoreIncompleteVideoWall, String masterId, PreviewType type) {
    List<KvmVideoWall> reviewWalls = new ArrayList<>();
    // 读取主机当前的电视墙
    List<CaesarVideoWall> currentWalls = new ArrayList<>(getOnlineVideoWall(masterId));
    if (currentWalls.isEmpty()) {
      log.error("GetAvailablePreviewChannel failed! Because {} is no walls", masterId);
      return null;
    }
    for (CaesarVideoWall currentWall : currentWalls) {
      if (ignoreIncompleteVideoWall && currentWall.getOnlinedevices().size() < 8) {
        continue;
      }
      KvmVideoWall wall = getVideoWallService().oneByUniqueSearchKey(InspectCaesarUtil
              .getCaesarWallUniqueSearchKey(masterId, currentWall));
      if (wall != null) {
        PreviewType previewType = PreviewType.valueOf(Property.findValueByKey(wall.getCollectorProperties(),
                PropertyKeyConst.PREVIEW_TYPE, String.valueOf(PreviewType.NONE)));
        if (previewType == type && Objects.equals(wall.getDeviceModel(),
                DeviceType.CAESAR_R1C8_VIDEO_WALL.getDeviceTypeId())) {
          reviewWalls.add(wall);
        }
      }
    }
    return reviewWalls;
  }

  /**
   * 获取缓存的凯撒大屏数据.
   *
   * @param masterId 主机Id.
   * @return .
   */
  public Collection<CaesarVideoWall> getOnlineVideoWall(String masterId) {
    Optional<Map<String, String>> optCaesarVideoWallMap =
            getRedisUtil().hmget(RedisKey.KC_SC + masterId);
    Collection<CaesarVideoWall> wallList = new ArrayList<>();
    if (optCaesarVideoWallMap.isPresent()) {
      Optional<Collection<String>> walls = Optional.of(optCaesarVideoWallMap.get().values());
      for (String wall : walls.get()) {
        wallList.add(JsonUtils.decode(wall, CaesarVideoWall.class));
      }
    }
    return wallList;
  }

  /**
   * 读取预览的策略.
   *
   * @param masterId .
   * @return .
   */
  public CaesarPreviewStrategyMode getStrategyMode(String masterId) {
    return getRedisUtil().getStr(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId))
            .map(CaesarPreviewStrategyMode::valueOf).orElse(CaesarPreviewStrategyMode.NONE);
  }
}
