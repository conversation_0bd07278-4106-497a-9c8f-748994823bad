package com.mediacomm.caesar;

import com.mediacomm.caesar.controller.CaesarCmdServer;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.system.base.kvm.KvmRunner;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * CaesarRunner.
 */
@Slf4j
@Component
public class CaesarRunner extends KvmRunner implements CommandLineRunner {
  @Resource
  private CaesarCmdServer caesarCmdServer;
  @Resource
  private KvmMasterService kvmMasterService;

  /**
   * 初始化.
   */
  @Override
  public void run(String... args) {
    log.info("Caesar service running!");
  }

  /**
   * 定时器.
   */
  @Scheduled(fixedRate = 5000)
  public void execute() {
    Collection<KvmMaster> caesarMasters =
            kvmMasterService.allByDeviceModel(DeviceType.CAESAR.getDeviceTypeId());
    caesarCmdServer.refreshRedundantMode(caesarMasters);
    caesarCmdServer.refreshPreviewStatus(caesarMasters);
  }

  /**
   * 指定消费者的线程数量,一个线程会打开一个Channel，
   * 一个队列上的消息只会被消费一次（不考虑消息重新入队列的情况）,下面的表示至少开启5个线程，最多10个。
   * 线程的数目需要根据你的任务来决定，如果是计算密集型，线程的数目就应该少一些.
   *
   * @param msg        负载信息
   * @param headers    头部信息
   * @param routingKey 路由信息
   */
  @RabbitListener(queues = MessageType.CAESAR_KVM, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    return switch (routingKey) {
      case RoutingKey.CAESAR_KVM_ADD -> "CAESAR_KVM_ADD";
      case RoutingKey.CAESAR_KVM_DELETE -> "CAESAR_KVM_DELETE";
      case RoutingKey.CAESAR_KVM_REBOOT -> caesarCmdServer.reboot(msg);
      case RoutingKey.CAESAR_KVM_REFRESH_EXTEND_DEVICE -> caesarCmdServer.refreshExtendDevice(msg);
      case RoutingKey.CAESAR_KVM_REFRESH_CONFIG -> caesarCmdServer.refreshConfig(msg);
      case RoutingKey.CAESAR_KVM_GET_TX_SNAPSHOT -> caesarCmdServer.getTxSnapshot(msg);
      case RoutingKey.CAESAR_KVM_GET_VW_SCENE -> caesarCmdServer.getVwScene(msg);
      case RoutingKey.CAESAR_KVM_GET_VW_PANELS -> caesarCmdServer.getVwPanels(msg);
      case RoutingKey.CAESAR_KVM_OPEN_VW_PANEL -> caesarCmdServer.openVwPanel(msg);
      case RoutingKey.CAESAR_KVM_CLOSE_VW_PANEL -> caesarCmdServer.closeVwPanel(msg);
      case RoutingKey.CAESAR_KVM_CLOSE_ALL_VW_PANEL -> caesarCmdServer.closeAllVwPanel(msg);
      case RoutingKey.CAESAR_KVM_OPEN_VW_PANELS -> caesarCmdServer.openVwPanels(msg);
      case RoutingKey.CAESAR_KVM_MOVE_VW_PANEL -> caesarCmdServer.moveVwPanels(msg);
      case RoutingKey.CAESAR_KVM_SWAP_VW_PANEL_LAYER -> caesarCmdServer.swapVwPanelLayer(msg);
      case RoutingKey.CAESAR_KVM_GET_SEAT_PANELS -> caesarCmdServer.getSeatPanels(msg);
      case RoutingKey.CAESAR_KVM_SEAT_OPEN_TX -> caesarCmdServer.seatOpenTx(msg);
      case RoutingKey.CAESAR_KVM_SEAT_OPEN_TXES -> caesarCmdServer.seatOpenTxes(msg);
      case RoutingKey.CAESAR_KVM_CLOSE_ALL_SEAT_PANEL -> caesarCmdServer.closeSeatPanels(msg);
      case RoutingKey.CAESAR_KVM_CLOSE_SEAT_PANEL -> caesarCmdServer.closeSeatPanel(msg);
      case RoutingKey.CAESAR_KVM_CONNECT_TX_RX -> caesarCmdServer.connectTxRx(msg);
      case RoutingKey.CAESAR_KVM_GET_SNAPSHOT -> caesarCmdServer.getSnapshot(msg);
      case RoutingKey.CAESAR_KVM_CLIENT_HEARTBEAT -> caesarCmdServer.updateClient(msg);
      case RoutingKey.CAESAR_VIDEO_WALL_UPDATE -> caesarCmdServer.changeVideoWall(msg);
      case RoutingKey.CAESAR_EXTEND_UPDATE -> caesarCmdServer.changeExtend(msg);
      case RoutingKey.CAESAR_KVM_OPEN_VS -> caesarCmdServer.openStreaming(msg);
      case RoutingKey.CAESAR_CONNECT_TX_ENCODER -> caesarCmdServer.connectToEncoder(msg);
      case RoutingKey.CAESAR_BANNER_GET -> caesarCmdServer.getBannerInfo(msg);
      case RoutingKey.CAESAR_BANNER_ENABLE -> caesarCmdServer.enableBanner(msg);
      case RoutingKey.CAESAR_BANNER_SET -> caesarCmdServer.setBanner(msg);
      case RoutingKey.CAESAR_BANNER_BG_COLOR_ENABLE -> caesarCmdServer.enableBannerBgColor(msg);
      case RoutingKey.CAESAR_VW_BOTTOM_IMAGE_SET -> caesarCmdServer.uploadVideoWallBottomImage(msg);
      case RoutingKey.CAESAR_VW_BOTTOM_IMAGE_ENABLE -> caesarCmdServer.enableVideoWallBottomImage(msg);
      case RoutingKey.CAESAR_EXTEND_VERSION -> caesarCmdServer.refreshExtendVersion(msg);
      case RoutingKey.CAESAR_START_UPGRADE_PACKAGE -> caesarCmdServer.startUpgradeAsset(msg);
      case RoutingKey.CAESAR_STOP_UPGRADE_PACKAGE -> caesarCmdServer.cancelUpgradeAsset(msg);
      case RoutingKey.CAESAR_CHECK_UPGRADE_PACKAGE -> caesarCmdServer.checkUpgradeAsset(msg);
      default -> Result.failureStr("Caesar no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
