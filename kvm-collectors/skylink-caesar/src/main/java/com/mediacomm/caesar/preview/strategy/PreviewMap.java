package com.mediacomm.caesar.preview.strategy;

import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class PreviewMap implements PreviewInfoOperator {

  public static final int MAX_REAL_TIME_ERROR_TIME = 3;
  /**
   * 实时预览的TXID与其预览信息.
   */
  private Map<String, RealTimePreviewInfo> realTimePreviewInfos = new HashMap<>();

  /**
   * 轮询预览的TXID与其预览信息.
   */
  private Map<String, PollingPreviewStatus> pollingPreviewInfos = new HashMap<>();

  /**
   * 轮询预览的通道.
   */
  private Set<KvmPreviewAsso> pollingPreviewChannels = new HashSet<>();

  /**
   * TxId所对应的所有请求信息  key:txId, key:requestKey, value:isImportant.
   */
  private Map<String, Map<String, Boolean>> txIdRequestInfos = new HashMap<>();

  /**
   * 每个TX上一次使用预览图的时间.
   */
  private Map<String, Long> lastPreviewUsedTime = new HashMap<>();

  private Map<String, Long> lastRequestKeyUsedtime = new HashMap<>();

  @Override
  public boolean isRealTimePreviewing(String txId) {
    return realTimePreviewInfos.containsKey(txId);
  }

  @Override
  public boolean isPollingPreviewing(String txId) {
    return pollingPreviewInfos.containsKey(txId);
  }

  @Override
  public boolean isPreviewing(String txId) {
    return isRealTimePreviewing(txId) || isPollingPreviewing(txId);
  }

  /**
   * 返回的是一个新的 HashSet 实例，它是当前内部集合的一个副本.
   *
   * @return 轮巡通道的副本集合.
   */
  @Override
  public Collection<KvmPreviewAsso> getPollingPreviewChannels() {
    return new HashSet<>(pollingPreviewChannels);
  }

  @Override
  public Collection<String> getPollingPreviewTxes() {
    return new HashSet<>(pollingPreviewInfos.keySet());
  }

  @Override
  public RealTimePreviewInfo getRealTimePreviewInfo(String txId) {
    RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
    if (info == null) {
      return null;
    } else {
      RealTimePreviewInfo result = new RealTimePreviewInfo();
      result.channel = info.channel;
      result.status = info.status;
      return result;
    }
  }

  @Override
  public PollingPreviewStatus getPollingPreviewStatus(String txId) {
    return pollingPreviewInfos.get(txId);
  }

  @Override
  public boolean setRealTimePreview(String txId, KvmPreviewAsso channel, boolean is4k,
      String requestKey, boolean isImportant) {
    if (!setRealTimePreviewImpl(txId, channel, is4k)) {
      return false;
    }
    // 保存请求信息
    Map<String, Boolean> requestInfos = txIdRequestInfos.get(txId);
    if (requestInfos == null) {
      requestInfos = new HashMap<>();
      txIdRequestInfos.put(txId, requestInfos);
    }
    requestInfos.put(requestKey, isImportant);
    return true;
  }

  @Override
  public boolean setRealTimePreview(String txId, KvmPreviewAsso channel, boolean is4k) {
    if (!isPreviewing(txId)) {
      log.warn(String.format("%s is not previewing!", txId));
      return false;
    }
    return setRealTimePreviewImpl(txId, channel, is4k);
  }

  @Override
  public boolean setRealTimePreviewStatus(String txId, RealTimePreviewStatus status) {
    RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
    if (info == null) {
      log.warn(String.format("%s is not in real time preview!", txId));
      return false;
    }

    if (info.status == RealTimePreviewStatus.REAL_TIME_READY
        && status == RealTimePreviewStatus.REAL_TIME_ESTABLISHING) {
      info.runError = 0;
      info.status = status;
      return true;
    } else if (info.status == RealTimePreviewStatus.REAL_TIME_ESTABLISHING && (
        status == RealTimePreviewStatus.REAL_TIME_READY
            || status == RealTimePreviewStatus.REAL_TIME_RUN)) {
      info.runError = 0;
      info.status = status;
      return true;
    } else {
      log.warn(String
          .format("Fail to set real time preview status. Current status is %s. New status is %s",
              info.status.toString(), status.toString()));
      return false;
    }
  }

  @Override
  public boolean clearRealTimePreviewError(String txId) {
    RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
    if (info == null) {
      log.warn(String.format("%s is not in real time preview!", txId));
      return false;
    }
    if (info.status == RealTimePreviewStatus.REAL_TIME_RUN) {
      info.runError = 0;
    }
    return true;
  }

  @Override
  public boolean setRealTimePreviewError(String txId) {
    RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
    if (info == null) {
      log.warn(String.format("%s is not in real time preview!", txId));
      return false;
    }
    if (info.status == RealTimePreviewStatus.REAL_TIME_RUN) {
      info.runError++;
      // 错误次数太多，变成READY状态
      if (info.runError >= MAX_REAL_TIME_ERROR_TIME) {
        info.runError = 0;
        info.status = RealTimePreviewStatus.REAL_TIME_READY;
      }
      return true;
    } else {
      log.warn(String
          .format("Fail to set real time error. Current status is %s. ", info.status.toString()));
      return false;
    }
  }

  @Override
  public long getLastPreviewUsedTime(String txId) {
    Long time = lastPreviewUsedTime.get(txId);
    return time == null ? 0 : time;
  }

  @Override
  public boolean isImportant(String txId) {
    Map<String, Boolean> info = txIdRequestInfos.get(txId);
    if (info == null) {
      return false;
    }
    // 只要有一个request是重要的，就是重要的
    for (Map.Entry<String, Boolean> entry : info.entrySet()) {
      if (entry.getValue()) {
        return true;
      }
    }
    return false;
  }

  @Override
  public boolean stopRealTimePreview(String txId) {
    RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
    if (info == null) {
      log.warn(String.format("%s is not in real time preview!", txId));
      return false;
    }
    if (info.channel != null) {
      info.channel.setUsed(false);
      info.channel = null;
    }
    info.status = RealTimePreviewStatus.REAL_TIME_STOPPED;
    return true;
  }

  @Override
  public boolean setPollingPreview(String txId, String requestKey, boolean isImportant) {
    setPollingPreviewImpl(txId);
    // 保存请求信息
    Map<String, Boolean> requestInfos = txIdRequestInfos.get(txId);
    if (requestInfos == null) {
      requestInfos = new HashMap<>();
      txIdRequestInfos.put(txId, requestInfos);
    }
    requestInfos.put(requestKey, isImportant);
    return true;
  }

  @Override
  public boolean setPollingPreview(String txId) {
    if (!isPreviewing(txId)) {
      log.warn(String.format("%s is not previewing!", txId));
      return false;
    }
    setPollingPreviewImpl(txId);
    return true;
  }

  @Override
  public boolean stopPollingPreview(String txId) {
    PollingPreviewStatus status = pollingPreviewInfos.get(txId);
    if (status == null) {
      log.warn(String.format("%s is not in polling preview!", txId));
      return false;
    }
    pollingPreviewInfos.put(txId, PollingPreviewStatus.POLLING_STOPPED);
    return true;
  }

  @Override
  public void updateRequestKeyTxes(String requestKey, boolean isRequestImportant,
      Collection<String> txes) {
    Set<String> txesSet = new HashSet<>(txes);
    Set<String> needRemoveTx = new HashSet<>();
    for (Map.Entry<String, Map<String, Boolean>> entry : txIdRequestInfos.entrySet()) {
      if (txesSet.contains(entry.getKey())) {
        txIdRequestInfos.get(entry.getKey()).put(requestKey, isRequestImportant);
      } else {
        Map<String, Boolean> requestInfo = txIdRequestInfos.get(entry.getKey());
        requestInfo.remove(requestKey);
        if (requestInfo.isEmpty()) {
          needRemoveTx.add(entry.getKey());
        }
      }
    }
    for (String tx : needRemoveTx) {
      log.info(String.format("Remove %s preview.", tx));
      if (isRealTimePreviewing(tx)) {
        stopRealTimePreview(tx);
      } else if (isPollingPreviewing(tx)) {
        stopPollingPreview(tx);
      }
      txIdRequestInfos.remove(tx);
      realTimePreviewInfos.remove(tx);
      pollingPreviewInfos.remove(tx);
      lastPreviewUsedTime.remove(tx);
    }
  }

  @Override
  public void updateLastPreviewUsedTime(String txId, long milli) {
    lastPreviewUsedTime.put(txId, milli);
  }

  @Override
  public void updateRequestKeyLastUsedTime(String requestKey, long milli) {
    lastRequestKeyUsedtime.put(requestKey, milli);
  }

  @Override
  public long getTxConfinedToRequestLastUsedTime(String requestKey) {
    long result = 0;
    for (Map.Entry<String, Map<String, Boolean>> entry : txIdRequestInfos.entrySet()) {
      if (entry.getValue().size() == 1 && entry.getValue().containsKey(requestKey)) {
        String tx = entry.getKey();
        result = Math.max(result, getLastPreviewUsedTime(tx));
      }
    }
    return result;
  }

  @Override
  public Map<String, Long> getAllRequestKeyLastUsedTime() {
    return new HashMap<>(lastRequestKeyUsedtime);
  }

  @Override
  public void removeRequestKey(String requestKey) {
    updateRequestKeyTxes(requestKey, false, Collections.emptyList());
    lastRequestKeyUsedtime.remove(requestKey);
  }

  @Override
  public void clearData() {
    // 释放通道
    setPollingPreviewChannels(Collections.emptyList());
    for (String tx : getRealTimePreviewTxes()) {
      stopRealTimePreview(tx);
    }
    // 清空数据
    pollingPreviewInfos.clear();
    realTimePreviewInfos.clear();
    pollingPreviewChannels.clear();
    txIdRequestInfos.clear();
    lastPreviewUsedTime.clear();
  }

  /**
   * .
   */
  public void printData() {
    log.info("tx request infos :" + txIdRequestInfos);
    log.info("tx last used time :" + lastPreviewUsedTime);
    log.info("request last used time:" + lastRequestKeyUsedtime);
  }

  /**
   * .
   */
  @Override
  public void setPollingPreviewChannels(Collection<KvmPreviewAsso> channels) {
    for (KvmPreviewAsso channel : pollingPreviewChannels) {
      channel.setUsed(false);
    }
    pollingPreviewChannels.clear();
    pollingPreviewChannels.addAll(channels);
    for (KvmPreviewAsso channel : pollingPreviewChannels) {
      channel.setUsed(true);
      channel.setHighResolution(false);
    }
  }

  @Override
  public Collection<String> getRealTimePreviewTxes() {
    return new HashSet<>(realTimePreviewInfos.keySet());
  }

  private void setPollingPreviewImpl(String txId) {
    if (isRealTimePreviewing(txId)) {
      // 删除实时预览
      RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
      if (info.channel != null) {
        info.channel.setUsed(false);
      }
      realTimePreviewInfos.remove(txId);
    }
    pollingPreviewInfos.put(txId, PollingPreviewStatus.POLLING);
  }

  private boolean setRealTimePreviewImpl(String txId, KvmPreviewAsso channel, boolean is4k) {
    if (channel == null) {
      log.warn("Channel is null!");
      return false;
    }
    if (channel.isUsed()) {
      log.warn(
          String.format("Channel %d:%d is used!", channel.getWallId(), channel.getSeq()));
      return false;
    }
    if (isPollingPreviewing(txId)) {
      pollingPreviewInfos.remove(txId);
    }
    if (isRealTimePreviewing(txId)) {
      RealTimePreviewInfo info = realTimePreviewInfos.get(txId);
      if (info.status == RealTimePreviewStatus.REAL_TIME_STOPPED) {
        // 如果暂停，就启动
        info.status = RealTimePreviewStatus.REAL_TIME_READY;
      }
      if (info.channel != null && info.channel != channel) {
        // 替换channel
        info.status = RealTimePreviewStatus.REAL_TIME_READY;
        info.channel.setUsed(false);
        info.channel = null;
      }
      info.runError = 0;
      info.channel = channel;
      info.channel.setUsed(true);
      info.channel.setHighResolution(is4k);
    } else {
      // 新建
      RealTimePreviewInfo info = new RealTimePreviewInfo();
      realTimePreviewInfos.put(txId, info);
      info.channel = channel;
      info.status = RealTimePreviewStatus.REAL_TIME_READY;
      info.channel.setUsed(true);
      info.channel.setHighResolution(is4k);
    }
    return true;
  }
}
