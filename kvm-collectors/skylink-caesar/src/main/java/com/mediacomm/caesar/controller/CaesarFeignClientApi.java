package com.mediacomm.caesar.controller;

import com.mediacomm.caesar.domain.CaesarDeviceNetInfo;
import com.mediacomm.caesar.domain.CaesarDeviceStatus;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarRedundantMode;
import com.mediacomm.caesar.domain.CaesarResponse;
import com.mediacomm.caesar.domain.CaesarRx;
import com.mediacomm.caesar.domain.CaesarSeat;
import com.mediacomm.caesar.domain.CaesarSeatPanelRect;
import com.mediacomm.caesar.domain.CaesarSeatPanels;
import com.mediacomm.caesar.domain.CaesarServer;
import com.mediacomm.caesar.domain.CaesarServerStatus;
import com.mediacomm.caesar.domain.CaesarSwitchBanner;
import com.mediacomm.caesar.domain.CaesarSwitchBannerColor;
import com.mediacomm.caesar.domain.CaesarSwitchBottomImage;
import com.mediacomm.caesar.domain.CaesarTx;
import com.mediacomm.caesar.domain.CaesarUser;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.CaesarVwScene;
import com.mediacomm.caesar.domain.EncoderDetailParam;
import com.mediacomm.caesar.domain.EncoderEdidParam;
import com.mediacomm.caesar.domain.kaito.Action;
import com.mediacomm.caesar.domain.kaito.KaitoDeviceIdReq;
import com.mediacomm.caesar.domain.kaito.KaitoGroupData;
import com.mediacomm.caesar.domain.kaito.KaitoInputData;
import com.mediacomm.caesar.domain.kaito.KaitoIpcData;
import com.mediacomm.caesar.domain.kaito.KaitoLayerReq;
import com.mediacomm.caesar.domain.kaito.KaitoPanelData;
import com.mediacomm.caesar.domain.kaito.KaitoPanelSwapData;
import com.mediacomm.caesar.domain.kaito.KaitoPanelsData;
import com.mediacomm.caesar.domain.kaito.KaitoRequestBody;
import com.mediacomm.caesar.domain.kaito.KaitoResponseBody;
import com.mediacomm.caesar.domain.kaito.KaitoScreenOsdReq;
import com.mediacomm.caesar.domain.kaito.KaitoVideoServerInfo;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWall;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWallGroup;
import com.mediacomm.caesar.domain.kaito.KaitoWriteSourceReq;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaRequestBody;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaResponseBody;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaScreenDetail;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaWordOsdRequestBody;
import java.net.URI;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * CaesarFeignClientApi.
 */
@FeignClient(name = "caesar-service", url = "http://localhost")
public interface CaesarFeignClientApi {

  @PostMapping("/mediacomm/reboot-server/{serverId}")
  void rebootServer(URI uri, @PathVariable("serverId") String serverId);


  @PostMapping("/mediacomm/reboot-ext/{deviceId}")
  void rebootDevice(URI uri, @PathVariable("deviceId") String deviceId);

  @GetMapping("/mediacomm/txlist")
  List<CaesarTx> getTxList(URI uri);

  @GetMapping("/mediacomm/rxlist")
  List<CaesarRx> getRxList(URI uri);

  @GetMapping("/mediacomm/video-walls")
  List<CaesarVideoWall> getVideoWalls(URI uri);

  @GetMapping("/mediacomm/seatlist")
  List<CaesarSeat> getSeatList(URI uri);

  @GetMapping("/mediacomm/server-info")
  List<CaesarServer> getServerInfo(URI uri);

  @GetMapping("/mediacomm/users")
  List<CaesarUser> getUsers(URI uri);

  @GetMapping("/mediacomm/status/servers")
  List<CaesarServerStatus> getServerStatus(URI uri);

  @GetMapping("/mediacomm/exts")
  List<CaesarDeviceStatus> getDeviceStatus(URI uri);

  @GetMapping("/mediacomm/exts/{decoderId}")
  List<CaesarDeviceStatus> getDeviceStatusByDecoderId(URI uri,
                                                      @PathVariable("decoderId") int decoderId);

  @GetMapping("/mediacomm/seat/panels/{decoderId}")
  CaesarSeatPanels getFourScreenRxPanels(URI uri, @PathVariable("decoderId") int decoderId);

  @GetMapping("/mediacomm/video-wall/scenes/{vwId}")
  List<CaesarVwScene> getVideoWallScenes(URI uri, @PathVariable("vwId") int vwId);

  @GetMapping("/mediacomm/video-wall/panels/{vwId}")
  CaesarVideoPanels getVideoWallPanels(URI uri, @PathVariable("vwId") int vwId);

  @GetMapping("/cgi-bin/get.cgi")
  EncoderDetailParam getEncoderDetail(URI uri);

  @GetMapping("/cgi-bin/getEdidList.cgi")
  EncoderEdidParam getEncoderEdid(URI uri);

  @PostMapping("/mediacomm/video-wall/open-panel/{vwId}")
  CaesarResponse openVideoWallPanel(URI uri, @PathVariable("vwId") int vwId,
                                    @RequestBody CaesarPanelRect caesarPanelRect,
                                    @RequestParam("addr") String addr,
                                    @RequestParam("name") String name);

  @PostMapping("/mediacomm/connect-rxtx/0")
  void connectTxRx(URI uri, @RequestBody Map<String, Integer> reqBody);

  @PostMapping("/mediacomm/video-wall/close-panel/{vwId}")
  CaesarResponse closeVideoWallPanel(URI uri, @PathVariable("vwId") int vwId,
                                     @RequestBody CaesarPanelRect caesarPanelRect,
                                     @RequestParam("addr") String addr,
                                     @RequestParam("name") String name);

  @PostMapping("/mediacomm/video-wall/open-panels/{vwId}")
  CaesarResponse openVideoWallPanels(URI uri, @PathVariable("vwId") int vwId,
                                     @RequestBody CaesarVideoPanels caesarVideoPanels,
                                     @RequestParam("addr") String addr,
                                     @RequestParam("name") String name);

  @PostMapping("/mediacomm/video-wall/close-all-panel/{vwId}")
  CaesarResponse closeVideoWallAllPanel(URI uri, @PathVariable("vwId") int vwId,
                                        @RequestParam("addr") String addr,
                                        @RequestParam("name") String name);

  @PostMapping("/mediacomm/video-wall/move-panel/{vwId}")
  CaesarResponse moveVideoWallPanel(URI uri, @PathVariable("vwId") int vwId,
                                    @RequestBody CaesarPanelRect caesarPanelRect,
                                    @RequestParam("addr") String addr,
                                    @RequestParam("name") String name);

  @PostMapping("/mediacomm/video-wall/swap-layer/{vwId}")
  void swapVideoWallLayer(URI uri, @PathVariable("vwId") int vwId,
                          @RequestBody Map<String, Integer> reqBody,
                          @RequestParam("addr") String addr,
                          @RequestParam("name") String name);

  @PostMapping("/mediacomm/seat/open-tx/{rxId}")
  void openTx(URI uri, @PathVariable("rxId") int rxId, @RequestBody Map<String, Integer> reqBody,
              @RequestParam("addr") String addr,
              @RequestParam("name") String name);

  @PostMapping("/mediacomm/seat/close-all-panel/{rxId}")
  void closeSeatAllPanel(URI uri, @PathVariable("rxId") int rxId,
                         @RequestParam("addr") String addr,
                         @RequestParam("name") String name);

  @PostMapping("/mediacomm/seat/open-panel/{decoderId}")
  void openFourScreenRxPanel(URI rui, @PathVariable("decoderId") int decoderId,
                             @RequestBody CaesarSeatPanelRect panelRect);

  @PostMapping("/mediacomm/seat/panels/{decoderId}")
  void openFourScreenRxPanels(URI uri, @PathVariable("decoderId") int decoderId,
                              @RequestBody CaesarSeatPanels seatPanels);

  @PostMapping("/mediacomm/seat/close-panel/{decoderId}")
  void closeFourScreenRxPanel(URI uri, @PathVariable("decoderId") int decoderId,
                              @RequestBody Map<String, Integer> panelId);

  @PostMapping("/mediacomm/seat/set-layout/{decoderId}")
  void setFourScreenRxLayout(URI uri, @PathVariable("decoderId") int decoderId,
                             @RequestBody Map<String, Integer> layoutType);

  /**
   * 获取VP7或预览终端的IP.
   */
  @GetMapping("/mediacomm/ext/ip/{decoderId}")
  Collection<CaesarDeviceNetInfo> getDeviceNetInfo(URI uri, @PathVariable int decoderId);

  /**
   * 读取主机冗余状态.
   */
  @GetMapping("/mediacomm/keepalived/state")
  CaesarRedundantMode getRedundantMode(URI uri);

  /**
   * 切换大屏条幅开关.
   */
  @PostMapping("/mediacomm/video-wall/switch-banner/{vwId}")
  CaesarResponse switchBanner(URI uri, @PathVariable int vwId,
                              @RequestBody CaesarSwitchBanner reqBody,
                              @RequestParam("addr") String addr,
                              @RequestParam("name") String name);

  /**
   * 切换大屏条幅背景色开关 .
   */
  @PostMapping("/mediacomm/video-wall/switch-banner-bg/{vwId}")
  CaesarResponse switchBannerBgColor(URI uri, @PathVariable int vwId,
                              @RequestBody CaesarSwitchBannerColor reqBody,
                              @RequestParam("addr") String addr,
                              @RequestParam("name") String name);

  /**
   * 切换大屏底图开关.
   */
  @PostMapping("/mediacomm/video-wall/switch-bg-image/{vwId}")
  CaesarResponse switchBannerBottomImage(URI uri, @PathVariable int vwId,
                                     @RequestBody CaesarSwitchBottomImage reqBody,
                                     @RequestParam("addr") String addr,
                                     @RequestParam("name") String name);

  @GetMapping("/kaito/videowall-group/{groupId}")
  KaitoGroupData getKaitoVideoWallGroup(URI uri, @PathVariable int groupId);

  @GetMapping("/kaito/input-card/list/{groupId}")
  Collection<KaitoInputData> getKaitoInputCardList(URI uri, @PathVariable int groupId);

  @GetMapping("/kaito/ipc/list/{groupId}")
  Collection<KaitoIpcData> getKaitoIpcList(URI uri, @PathVariable int groupId);

  @GetMapping("/kaito/videowall-group/list")
  Collection<KaitoVideoWallGroup> getKaitoVideoWallGroups(URI uri);

  @GetMapping("/kaito/videowall/{groupId}/{wallId}")
  KaitoVideoWall getKaitoVideoWall(URI uri, @PathVariable("groupId") int groupId,
                                   @PathVariable("wallId") int wallId);

  @PutMapping("/kaito/videowall/{groupId}/{wallId}/layer")
  KaitoPanelsData openKaitoPanel(URI uri, @PathVariable("groupId") int groupId,
                                 @PathVariable("wallId") int wallId,
                                 @RequestBody Collection<KaitoVideoWall.Layer> layers);

  @PostMapping("/kaito/videowall/{groupId}/{wallId}/layer/reset")
  KaitoPanelsData openKaitoPanels(URI uri, @PathVariable("groupId") int groupId,
                                  @PathVariable("wallId") int wallId,
                                  @RequestBody Collection<KaitoVideoWall.Layer> layers);

  @PostMapping("/kaito/videowall/layer/window/{groupId}/{wallId}/{layerId}")
  KaitoPanelData moveKaitoPanel(URI uri, @PathVariable("groupId") int groupId,
                                @PathVariable("wallId") int wallId,
                                @PathVariable("layerId") int layerId,
                                @RequestBody KaitoVideoWall.Window window);

  @PostMapping("/kaito/videowall/layer/source/{groupId}/{wallId}/{layerId}")
  KaitoPanelData switchKaitoPanel(URI uri, @PathVariable("groupId") int groupId,
                                  @PathVariable("wallId") int wallId,
                                  @PathVariable("layerId") int layerId,
                                  @RequestBody KaitoWriteSourceReq sourceReq);

  @PostMapping("/kaito/videowall/layer/zorder/{groupId}/{wallId}/{layerId}")
  KaitoPanelSwapData swapKaitoPanel(URI uri, @PathVariable("groupId") int groupId,
                                    @PathVariable("wallId") int wallId,
                                    @PathVariable("layerId") int layerId,
                                    @RequestBody Action action);

  @DeleteMapping("/kaito/videowall/layer/{groupId}/{wallId}/{layerId}")
  KaitoPanelsData closeKaitoPanel(URI uri, @PathVariable("groupId") int groupId,
                                  @PathVariable("wallId") int wallId,
                                  @PathVariable("layerId") int layerId);

  @PostMapping("/kaito/videowall/clear/{groupId}/{wallId}")
  void clearKaitoPanels(URI uri, @PathVariable("groupId") int groupId,
                        @PathVariable("wallId") int wallId);

  /**
   * 读取E系列嗨动大屏详情.
   */
  @PostMapping("/open/screen/readDetail")
  KaitoResponseBody getScreenReadDetail(URI uri,
                                        @RequestBody KaitoRequestBody<KaitoLayerReq> requestBody);

  /**
   * 读取Alpha系列嗨动大屏详情.
   */
  @PostMapping("/open/api/screen/readDetail")
  KaitoAlphaResponseBody<KaitoAlphaScreenDetail> getScreenReadDetail(URI uri, @RequestBody
      KaitoAlphaRequestBody<KaitoLayerReq> requestBody);

  /**
   * 开启E系列嗨动大屏的横幅.
   */
  @PostMapping("/open/screen/writeOSD")
  KaitoResponseBody enableBanner(URI uri,
                                 @RequestBody KaitoRequestBody<KaitoScreenOsdReq> requestBody);

  /**
   * 开启Alpha系列嗨动大屏的横幅.
   */
  @PostMapping("/open/api/screen/writeWordOsd")
  KaitoAlphaResponseBody<KaitoAlphaWordOsdRequestBody> enableBanner(URI uri, @RequestBody
      KaitoAlphaRequestBody<KaitoAlphaWordOsdRequestBody> requestBody);

  /**
   * 读取E系列视频预监视频流信息.
   */
  @PostMapping("/open/main/readVideoServerInfo")
  KaitoResponseBody getVideoServerInfo(URI uri,
                                       @RequestBody KaitoRequestBody<KaitoDeviceIdReq> requestBody);

  /**
   * 读取Alpha系列视频预监视频流信息.
   */
  @PostMapping("/open/api/main/readVideoServerInfo")
  KaitoAlphaResponseBody<KaitoVideoServerInfo> getVideoServerInfo(URI uri, @RequestBody
      KaitoAlphaRequestBody<KaitoDeviceIdReq> requestBody);

}
