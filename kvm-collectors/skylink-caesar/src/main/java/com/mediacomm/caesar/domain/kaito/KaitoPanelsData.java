package com.mediacomm.caesar.domain.kaito;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KaitoPanelsData {
  private Integer seq;
  private Collection<KaitoVideoWall.Layer> layers;
}
