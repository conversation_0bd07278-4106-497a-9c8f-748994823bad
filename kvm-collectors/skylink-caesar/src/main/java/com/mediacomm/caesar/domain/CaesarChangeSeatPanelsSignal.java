package com.mediacomm.caesar.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Collection;
import lombok.Data;

/**
 * 凯撒四画面Rx窗口改变的通知消息.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaesarChangeSeatPanelsSignal {
  private int decoderId; // 屏幕ID
  private int layoutType; // 布局类型
  private Collection<String> src; // 操作的信号源
  private String target; // rx名称
}
