package com.mediacomm.caesar.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.protocol.HttpContext;

/**
 * .
 */
@Slf4j
public class DownloadUtil {

  /**
   * create a new folder if it does not exists.
   */
  public static boolean isPathExist(String path) {
    File file = new File(path);
    if (!file.exists()) {
      try {
        return file.mkdirs();
      } catch (Exception ex) {
        return false;
      }
    }
    return true;
  }

  /**
   * .
   */
  public static Map<String, Boolean> saveImageAsync(CloseableHttpAsyncClient client,
                                                    Map<String, String> urlSavePaths) {
    HttpContext context = HttpClientContext.create();
    Map<String, Boolean> result = new ConcurrentHashMap<>();
    CountDownLatch latch = new CountDownLatch(urlSavePaths.size());
    List<Future<HttpResponse>> exeFutureList = new ArrayList<>();
    for (Map.Entry<String, String> entry : urlSavePaths.entrySet()) {
      final String url = entry.getKey();
      final String savePath = entry.getValue();
      HttpGet request = new HttpGet(url);
      request.setConfig(
          RequestConfig.copy(RequestConfig.DEFAULT).setConnectTimeout(5000).setSocketTimeout(5000)
              .setConnectionRequestTimeout(5000).build());
      exeFutureList.add(client.execute(request, context, new FutureCallback<>() {
        @Override
        public void completed(HttpResponse httpResponse) {
          try {
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
              try {
                result.put(url, saveFile(httpResponse.getEntity().getContent(),
                    httpResponse.getEntity().getContentLength(), savePath));
              } catch (IOException e) {
                log.warn("Fail to save file for " + url, e);
                result.put(url, false);
              }
            } else {
              log.debug("Fail to download " + url + " for status code "
                  + httpResponse.getStatusLine().getStatusCode());
              result.put(url, false);
            }
          } finally {
            latch.countDown();
          }
        }

        @Override
        public void failed(Exception e) {
          log.debug("Fail to download " + url, e);
          result.put(url, false);
          latch.countDown();
        }

        @Override
        public void cancelled() {
          log.debug("Fail to download " + url + " for cancelled");
          result.put(url, false);
          latch.countDown();
        }
      }));
    }

    try {
      if (!latch.await(urlSavePaths.size() * 2, TimeUnit.SECONDS)) {
        log.info("CountDownLatch await false");
      }
      for (Future<HttpResponse> f : exeFutureList) {
        f.cancel(true);
      }
    } catch (InterruptedException e) {
      e.printStackTrace();
      log.debug("Fail to download all " + urlSavePaths + " for timeout", e);
    }
    return result;
  }

  /**
   * 下载图片.
   */
  public static boolean saveImage(String imageUrl, String savePath) {
    HttpURLConnection connection = null;
    try {
      URL url = new URL(imageUrl);
      connection = (HttpURLConnection) url.openConnection();
      connection.setConnectTimeout(5000);
      connection.setReadTimeout(5000);
      connection.connect();
      return saveFile(connection.getInputStream(), connection.getContentLength(), savePath);
    } catch (IOException ex) {
      ex.printStackTrace();
      return false;
    } finally {
      if (connection != null) {
        connection.disconnect();
      }
    }
  }

  private static boolean saveFile(InputStream inputStream, long contentLength, String savePath)
      throws IOException {
    InputStream is = inputStream;
    //先读取所有的数据
    byte[] bos = readInputStream(is);
    if (bos.length != contentLength) {
      log.debug("Fail to read image for " + savePath);
      return false;
    }
    //然后写入文件.
    File tmpFile = null;
    try {
      File parentFile = new File(Paths.get(new File(savePath).getParent(), "temp").toString());
      if (!parentFile.exists() && !parentFile.mkdirs()) {
        throw new IOException("Can not creates the directory");
      }
      File imageFile = File.createTempFile("temp", null, parentFile);
      tmpFile = imageFile;
      try (FileOutputStream outStream = new FileOutputStream(imageFile)) {
        outStream.write(bos);
      }
      Files.move(imageFile.toPath(), Paths.get(savePath), StandardCopyOption.ATOMIC_MOVE);
      return true;
    } catch (IOException ex) {
      ex.printStackTrace();
      log.error(ex.getMessage());
      return false;
    } finally {
      if (tmpFile != null && tmpFile.exists()) {
        Files.delete(tmpFile.toPath());
      }
    }
  }

  /**
   * .
   */
  public static byte[] readInputStream(InputStream inputStream) throws IOException {
    byte[] buffer = new byte[1024];
    int len;
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    while ((len = inputStream.read(buffer)) != -1) {
      bos.write(buffer, 0, len);
    }
    bos.close();
    return bos.toByteArray();
  }

}

