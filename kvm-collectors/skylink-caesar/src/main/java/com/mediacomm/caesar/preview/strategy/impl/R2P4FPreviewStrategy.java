package com.mediacomm.caesar.preview.strategy.impl;

import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.caesar.preview.strategy.PreviewMap;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.system.variable.sysenum.DeviceType;
import lombok.extern.slf4j.Slf4j;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * .
 */
@Slf4j
public class R2P4FPreviewStrategy implements PreviewStrategy {
  private final DeviceDataGetter dataGetter;
  private PreviewInfoOperator currentPreview = new PreviewMap();

  public R2P4FPreviewStrategy(DeviceDataGetter dataGetter) {
    this.dataGetter = dataGetter;
  }

  @Override
  public void addPreview(ObjectIds objectIds) {
    synchronized (currentPreview) {
      Set<String> requestPreview = new HashSet<>(objectIds.getIds());
      Collection<String> timeoutPollingTx = getTimeoutPollingTxImpl(currentPreview);
      timeoutPollingTx.removeAll(requestPreview);
      for (String tx : timeoutPollingTx) {
        currentPreview.stopPollingPreview(tx);
      }
      long currentTime = System.currentTimeMillis();
      for (String tx : objectIds.getIds()) {
        currentPreview.updateLastPreviewUsedTime(tx, currentTime);
        currentPreview.setPollingPreview(tx, objectIds.getRequestKey(), objectIds.isImportant());
      }
    }
  }

  @Override
  public void onTxOnline(String txId) {

  }

  @Override
  public void onTxOffline(String txId) {

  }

  @Override
  public void onGetSnapshot(String txId) {
    synchronized (currentPreview) {
      currentPreview.updateLastPreviewUsedTime(txId, System.currentTimeMillis());
    }
  }

  @Override
  public void resetChannels() {
    synchronized (currentPreview) {
      Collection<KvmPreviewAsso> all = dataGetter.getAllPreviewChannels(DeviceType.CAESAR_R2P4F)
          .values().stream().flatMap(Collection::stream)
          .collect(Collectors.toSet());
      currentPreview.setPollingPreviewChannels(all);
    }
  }

  @Override
  public void refresh(PreviewInfoOperator currentPreview) {

  }

  @Override
  public PreviewInfoOperator getPreviewInfoOpt() {
    return currentPreview;
  }
}
