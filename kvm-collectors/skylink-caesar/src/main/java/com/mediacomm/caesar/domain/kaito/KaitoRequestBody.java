package com.mediacomm.caesar.domain.kaito;

import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mediacomm.util.JsonUtils;
import jakarta.validation.constraints.NotBlank;
import java.nio.charset.StandardCharsets;
import lombok.Data;

/**
 * .
 */
@Data
public class KaitoRequestBody<T> {
  @JsonProperty("pId") // 防止被转成pid
  private String pId;
  private String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
  private String sign;
  private String body;

  /**
   * 生成签名.
   *
   * @param secretKey 项目密钥.
   */
  public void setSign(@NotBlank String secretKey) {
    byte[] bytes = DigestUtil.sha256(String.format("%s%s%s%s", body, timeStamp, pId, secretKey)
            .getBytes(StandardCharsets.UTF_8));
    StringBuilder hexString = new StringBuilder();
    for (byte b : bytes) {
      hexString.append(String.format("%02X", b));
    }
    this.sign = hexString.toString().toLowerCase();
  }

  public void setBody(T body) {
    this.body = JsonUtils.encode(body);
  }

  public void setBody(String jsonBody) {
    this.body = jsonBody;
  }
}
