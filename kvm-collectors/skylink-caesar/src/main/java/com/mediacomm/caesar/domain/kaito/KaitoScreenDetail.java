package com.mediacomm.caesar.domain.kaito;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoScreenDetail {
  @JsonProperty("Osd")
  private Osd Osd;
  @JsonProperty("OsdImage")
  private Osd OsdImage;
  private int brightness;
  private int deviceId;
  private int gamma;
  private General general;
  private int mayUse;
  private OutputMode outputMode;
  private int screenId;

  @Data
  public static class Osd {
    private int enable;
    private int height;
    private Image image;
    private int isJudge;
    private int type; // 0：文字，1：图片
    private int width;
    private Words words;
    private int x;
    private int y;
  }

  @Data
  public static class Image {
    private File file;
    private int opacity;
  }

  @Data
  public static class File {
    private int fileLength;
    private String fileName;
    private int hashSum;
    private int height;
    private int width;
  }

  @Data
  public static class Words {
    private int aligned;
    private BackgroundColor backgroundColor;
    private int backgroundEnable;
    private Image backgroundImage;
    private int backgroundType;
    private String chars; // 横幅文本
    private int direction;
    private int font;
    private BackgroundColor fontColor;
    private int fontPercent;
    private int space;
    private int speed;
  }

  @Data
  public static class BackgroundColor {
    @JsonProperty("A")
    private int A;
    @JsonProperty("B")
    private int B;
    @JsonProperty("G")
    private int G;
    @JsonProperty("R")
    private int R;
  }

  @Data
  public static class General {
    private String createTime;
    private String name;
  }

  @Data
  public static class OutputMode {
    private Mosaic mosaic; // 屏幕拼接行列数
    private Offset offset; // 偏移量
    private Size size;
  }

  @Data
  public static class Mosaic {
    private int column;
    private int edgeEnable;
    private int edgeHeight;
    private int edgeWidth;
    private int row;
  }

  @Data
  public static class Offset {
    private int x;
    private int y;
  }

  @Data
  public static class Size {
    private int height;
    private int width;
    private int x;
    private int y;
  }
}
