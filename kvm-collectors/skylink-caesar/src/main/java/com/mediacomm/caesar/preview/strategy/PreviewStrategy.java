package com.mediacomm.caesar.preview.strategy;

import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * .
 */
public interface PreviewStrategy {
  int PREVIEW_TIME_OUT_MILLI = 5000;
  /**
   * 处理预览TX的请求.
   *
   * @param objectIds 请求信息.
   * @param currentPreview 当前的预览信息.
   */
  void addPreview(ObjectIds objectIds);

  void onTxOnline(String txId);

  void onTxOffline(String txId);

  void onGetSnapshot(String txId);

  void resetChannels();

  /**
   * 刷新预览，处理预览资源不平衡的问题.
   *
   * @param currentPreview .
   */
  void refresh(PreviewInfoOperator currentPreview);

  PreviewInfoOperator getPreviewInfoOpt();

  /**
   * 获取非停止状态的超时的轮询TX列表.
   *
   * @param currentPreview 当前预览信息
   * @return 超时的轮询TX列表
   */
  default Collection<String> getTimeoutPollingTxImpl(PreviewInfoGetter currentPreview) {
    return currentPreview.getPollingPreviewTxes().stream()
            .filter((tx) -> checkPreviewTimeout(tx, currentPreview)
                    && currentPreview.getPollingPreviewStatus(tx)
                    != PreviewInfoGetter.PollingPreviewStatus.POLLING_STOPPED)
            .collect(
                    Collectors.toSet());
  }

  private boolean checkPreviewTimeout(String txId, PreviewInfoGetter currentPreview) {
    return System.currentTimeMillis() - currentPreview.getLastPreviewUsedTime(txId)
            > PREVIEW_TIME_OUT_MILLI;
  }
}
