package com.mediacomm.caesar.preview.device;

import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.util.CaesarTcpClient;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;



/**
 * .
 */
@Slf4j
public class FourScreenRxPreviewOperatorImpl implements PreviewDeviceOperator {
  private final SkyLinkTaskPool taskRunner;
  private final DeviceDataGetter deviceDataGetter;
  private final CaesarPreviewStrategyMode strategyMode =
          CaesarPreviewStrategyMode.PREVIEW_STRATEGY_4CRX;
  private CloseableHttpAsyncClient httpClient;
  private final HttpGet httpGet = new HttpGet();

  public FourScreenRxPreviewOperatorImpl(SkyLinkTaskPool taskRunner,
                                         DeviceDataGetter deviceDataGetter) {
    this.taskRunner = taskRunner;
    this.deviceDataGetter = deviceDataGetter;
    try {
      ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor();
      PoolingNHttpClientConnectionManager cm =
              new PoolingNHttpClientConnectionManager(ioReactor);
      cm.setMaxTotal(50);
      cm.setDefaultMaxPerRoute(8);
      httpClient =
              HttpAsyncClients.custom().setConnectionManager(cm).build();
    } catch (IOReactorException e) {
      log.error("Fail to create DefaultConnectingIOReactor", e);
      httpClient = HttpAsyncClients.createDefault();
    }
    httpClient.start();
    httpGet.setConfig(RequestConfig.copy(RequestConfig.DEFAULT).setConnectTimeout(5000)
            .setSocketTimeout(5000)
            .setConnectionRequestTimeout(5000).build());

  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanel(
          Map<String, KvmPreviewAsso> txChannelMap) {
    return null;
  }

  @Override
  public CompletableFuture<Boolean> closePanels(Collection<KvmPreviewAsso> channels) {
    return null;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanelAndGetPreview(
          Map<String, KvmPreviewAsso> txChannelMap,
          Map<String, String> txSavePathMap,
          Collection<KvmPreviewAsso> unusedChannels) {
    // 根据rx id分组
    Map<String, Map<String, KvmPreviewAsso>> group = txChannelMap.entrySet().stream().collect(
            Collectors.groupingBy(item -> item.getValue().getRxId(),
                    Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
    result.complete(new HashMap<>());
    for (Map.Entry<String, Map<String, KvmPreviewAsso>> stringMapEntry : group.entrySet()) {
      result = result.thenCombine(taskRunner.addAsyncTask(
                      () -> openPanelAndGetPreviewImpl(stringMapEntry.getKey(), stringMapEntry.getValue(),
                              txSavePathMap)),
              (lhs, rhs) -> {
                Map<String, Boolean> newResult = new HashMap<>(lhs);
                newResult.putAll(rhs);
                return newResult;
              }
      );
    }
    return result;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> getPreview(Map<String, String> txSavePathMap) {
    return null;
  }

  @Override
  public CaesarPreviewStrategyMode getPreviewStrategyMode() {
    return this.strategyMode;
  }

  @Override
  public void close() throws IOException {
    httpClient.close();
  }

  private Map<String, Boolean> openPanelAndGetPreviewImpl(String rxId,
                                                          Map<String, KvmPreviewAsso> txChannelMap,
                                                          Map<String, String> savePath) {
    Map<String, Boolean> result = new HashMap<>();
    String imageUrl = null;
    int[] txIds = new int[txChannelMap.keySet().size()];
    int count = 0;
    for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
      KvmAsset tx = deviceDataGetter.getExtendDevice(entry.getKey());
      if (tx != null) {
        txIds[count] = tx.getDeviceId();
        count++;
      }
      imageUrl = entry.getValue().getUrl();
      result.put(entry.getKey(), false);
    }
    if (StringUtils.isEmpty(imageUrl)) {
      log.error("Not found image url.");
      return result;
    }
    log.debug("4Rx previewOpen {}", txIds);
    KvmAsset rx = deviceDataGetter.getExtendDevice(rxId);
    int previewIntervalTime = Integer.parseInt(
            Property.findValueByKey(rx.getCollectorProperties(), "previewIntervalTime", "1500"));
    try {
      CaesarTcpClient cli = new CaesarTcpClient(deviceDataGetter.getMasterIp());
      cli.openMultiviewPreview(rx.getDeviceId(), txIds);
      Thread.sleep(previewIntervalTime);
      httpGet.setURI(URI.create(imageUrl));
      Future<HttpResponse> imageFuture = this.httpClient.execute(httpGet, null);
      HttpResponse imageResponse = imageFuture.get();
      if (imageResponse.getStatusLine().getStatusCode() == 200) {
        BufferedImage image = ImageIO.read(imageResponse.getEntity().getContent());
        BufferedImage scaledImage = null;
        if (image.getWidth() > 1920 && image.getHeight() > 1080) {
          int width = image.getWidth() / 2;
          int height = image.getHeight() / 2;
          // 图片压缩
          scaledImage = new BufferedImage(width, height, image.getType());
          AffineTransform at = new AffineTransform();
          at.scale((double) width / image.getWidth(), (double) height / image.getHeight());
          AffineTransformOp op = new AffineTransformOp(at, AffineTransformOp.TYPE_BILINEAR);
          scaledImage = op.filter(image, scaledImage);
        }
        int cut = 0;
        for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
          File src = new File(savePath.get(entry.getKey()));
          File parentFile = new File(Paths.get(src.getParent(), "temp").toString());
          if (!parentFile.exists() && !parentFile.mkdirs()) {
            throw new IOException("Can not creates the directory");
          }
          File imageFile = File.createTempFile("temp", null, parentFile);
          BufferedImage subImage;
          if (scaledImage != null) {
            subImage = cutImage(scaledImage, cut);
          } else {
            subImage = cutImage(image, cut);
          }
          if (subImage != null) {
            ImageIO.write(subImage, "jpg", imageFile);
            Files.move(imageFile.toPath(), Paths.get(savePath.get(entry.getKey())),
                    StandardCopyOption.ATOMIC_MOVE);
            result.put(entry.getKey(), true);
          }
          cut++;
        }
      } else {
        log.error("Get 4CRX image failed! {}", imageResponse.getStatusLine().getStatusCode());
      }
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return result;
  }

  private BufferedImage cutImage(BufferedImage src, int location) {
    int scaledWidth = src.getWidth() / 2;
    int scaledHeight = src.getHeight() / 2;
    return switch (location) {
      case 0 -> src.getSubimage(0, 0, scaledWidth, scaledHeight);
      case 1 -> src.getSubimage(scaledWidth, 0, scaledWidth, scaledHeight);
      case 2 -> src.getSubimage(0, scaledHeight, scaledWidth, scaledHeight);
      case 3 -> src.getSubimage(scaledWidth, scaledHeight, scaledWidth, scaledHeight);
      default -> {
        log.error(
                String.format("Out of rx preview range!Current:%d", location));
        yield null;
      }
    };
  }
}
