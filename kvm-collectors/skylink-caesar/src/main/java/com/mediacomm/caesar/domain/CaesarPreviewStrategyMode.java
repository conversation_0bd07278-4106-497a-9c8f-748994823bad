package com.mediacomm.caesar.domain;

import lombok.Getter;

/**
 * 凯撒中文预览策略.
 */
public enum CaesarPreviewStrategyMode {
  /**
   * 使用四画面Rx做预览.
   */
  PREVIEW_STRATEGY_4CRX(2),
  /**
   * 使用预览VP6做预览.
   */
  PREVIEW_STRATEGY_VP6(1),
  /**
   * 使用预览R2P4F做预览.
   */
  PREVIEW_STRATEGY_R2P4F(3),
  /**
   * 无设备可用于预览.
   */
  NONE(0);

  @Getter
  private int priority; // 预览策略优先级，值越大，优先级越高

  CaesarPreviewStrategyMode(int priority) {
    this.priority = priority;
  }
}
