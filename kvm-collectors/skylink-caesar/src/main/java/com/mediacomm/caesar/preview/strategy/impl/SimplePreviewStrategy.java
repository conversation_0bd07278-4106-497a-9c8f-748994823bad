package com.mediacomm.caesar.preview.strategy.impl;

import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.PreviewInfoGetter.PollingPreviewStatus;
import com.mediacomm.caesar.preview.PreviewInfoGetter.RealTimePreviewStatus;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.preview.strategy.PreviewInfoOperator;
import com.mediacomm.caesar.preview.strategy.PreviewMap;
import com.mediacomm.caesar.preview.strategy.PreviewStrategy;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.OptionalInt;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 预览策略接口.
 */
@Slf4j
public class SimplePreviewStrategy implements PreviewStrategy {

  private PreviewInfoOperator currentPreview = new PreviewMap();

  private DeviceDataGetter dataGetter;

  public static final int PREVIEW_TIME_OUT_MILLI = 5000;

  public static final int REQUEST_KEY_TIME_OUT_MILLI = PREVIEW_TIME_OUT_MILLI * 15;

  public static final int MAX_POLLING_ITERATION = 4;

  public static final double POLLING_CHANNEL_PERCENTAGE = 0.25;

  public SimplePreviewStrategy(DeviceDataGetter dataGetter) {
    this.dataGetter = dataGetter;
  }

  @Override
  public void addPreview(ObjectIds objectIds) {
    synchronized (currentPreview) {
      // 添加预览，先使用实时预览，再使用轮询预览
      // 如果预览图超过PREVIEW_TIME_OUT_MILLI没有被使用，说明预览暂时不用，可以被挤掉
      // 对于important tx，尽可能实时预览，如果有非important tx在实时预览列表，可以被挤掉
      Set<String> requestPreview = new HashSet<>(objectIds.getIds());

      long currentTime = System.currentTimeMillis();
      // 收集可以被挤掉的预览信号
      Collection<String> unimportantRealTimeTx = getUnimportantRealtimeTxImpl(currentPreview);
      Collection<String> timeoutRealTimeTx = getTimeoutRealTimeTxImpl(currentPreview);
      Collection<String> timeoutPollingTx = getTimeoutPollingTxImpl(currentPreview);

      unimportantRealTimeTx.removeAll(requestPreview);
      timeoutRealTimeTx.removeAll(requestPreview);
      timeoutPollingTx.removeAll(requestPreview);
      // 挤掉超时的预览
      for (String tx : timeoutRealTimeTx) {
        currentPreview.stopRealTimePreview(tx);
      }
      for (String tx : timeoutPollingTx) {
        currentPreview.stopPollingPreview(tx);
      }
      Set<String> statusChangedTx = new HashSet<>();

      statusChangedTx.addAll(timeoutRealTimeTx);
      statusChangedTx.addAll(timeoutPollingTx);

      // 开通预览
      Map<String, Boolean> importantPollingTx = new HashMap<>();
      for (String tx : objectIds.getIds()) {
        currentPreview.updateLastPreviewUsedTime(tx, currentTime);
        if (currentPreview.isPreviewing(tx)) {
          // 如果已经停止，就恢复预览
          if (resumePreviewImpl(tx, currentPreview)) {
            statusChangedTx.add(tx);
          }
          if (currentPreview.isPollingPreviewing(tx) && objectIds.isImportant()) {
            importantPollingTx.put(tx, dataGetter.is4kTx(tx));
          }
        } else {
          boolean is4k = dataGetter.is4kTx(tx);
          KvmPreviewAsso channel = dataGetter.getAvailablePreviewChannel(is4k, true);
          if (channel != null) {
            currentPreview.setRealTimePreview(tx, channel, is4k, objectIds.getRequestKey(),
                objectIds.isImportant());
          } else {
            currentPreview
                .setPollingPreview(tx, objectIds.getRequestKey(), objectIds.isImportant());
            if (objectIds.isImportant()) {
              importantPollingTx.put(tx, is4k);
            }
          }
          statusChangedTx.add(tx);
        }
      }
      // 挤掉不重要的实时预览
      statusChangedTx.addAll(
          promoteImportantPollingTx(unimportantRealTimeTx, importantPollingTx, currentPreview));
      currentPreview.updateRequestKeyTxes(objectIds.getRequestKey(), objectIds.isImportant(),
          objectIds.getIds());
      currentPreview.updateRequestKeyLastUsedTime(objectIds.getRequestKey(), currentTime);
      if (statusChangedTx.size() > 0) {
        log.info(
            String.format("Preview status changed for addPreview %s.", objectIds.toString()));
        for (String tx : statusChangedTx) {
          printUpdateStatus(tx, "addPreview", currentPreview);
        }
      }
    }
  }

  /**
   * 尝试把重要的轮询预览TX提升为实时预览.
   *
   * @param unimportantRealTimeTx 不重要的实时预览TX
   * @param importantPollingTx    重要的轮询预览TX
   * @param currentPreview        当前预览信息
   * @return 修改了预览状态的TX
   */
  private Collection<String> promoteImportantPollingTx(Collection<String> unimportantRealTimeTx,
      Map<String, Boolean> importantPollingTx, PreviewInfoOperator currentPreview) {
    Set<String> statusChangedTx = new HashSet<>();
    if (importantPollingTx.size() > 0) {
      for (Map.Entry<String, Boolean> entry : importantPollingTx.entrySet()) {
        KvmPreviewAsso channel = dataGetter.getAvailablePreviewChannel(entry.getValue(), true);
        while (channel == null && !unimportantRealTimeTx.isEmpty()) {
          String first = unimportantRealTimeTx.iterator().next();
          currentPreview.setPollingPreview(first);
          unimportantRealTimeTx.remove(first);
          channel = dataGetter.getAvailablePreviewChannel(entry.getValue(), true);
          statusChangedTx.add(first);
        }
        if (channel != null) {
          currentPreview.setRealTimePreview(entry.getKey(), channel, entry.getValue());
          statusChangedTx.add(entry.getKey());
        }
      }
    }
    return statusChangedTx;
  }

  @Override
  public void onTxOnline(String txId) {
    synchronized (currentPreview) {
      // 恢复预览
      if (checkPreviewTimeout(txId, currentPreview)) {
        return;
      }
      if (resumePreviewImpl(txId, currentPreview)) {
        printUpdateStatus(txId, "onTxOnline", currentPreview);
      }
    }
  }

  @Override
  public void onTxOffline(String txId) {
    synchronized (currentPreview) {
      if (currentPreview.isRealTimePreviewing(txId)) {
        currentPreview.stopRealTimePreview(txId);
        printUpdateStatus(txId, "onTxOffline", currentPreview);
      } else if (currentPreview.isPollingPreviewing(txId)) {
        currentPreview.stopPollingPreview(txId);
        printUpdateStatus(txId, "onTxOffline", currentPreview);
      }
    }
  }

  @Override
  public void onGetSnapshot(String txId) {
    synchronized (currentPreview) {
      currentPreview.updateLastPreviewUsedTime(txId, System.currentTimeMillis());
      if (resumePreviewImpl(txId, currentPreview)) {
        printUpdateStatus(txId, "onGetSnapshot", currentPreview);
      }
    }
  }

  @Override
  public void resetChannels() {
    synchronized (currentPreview) {
      log.info("Reset channels.");
      // 释放所有通道
      currentPreview.setPollingPreviewChannels(Collections.emptyList());
      for (String tx : currentPreview.getRealTimePreviewTxes()) {
        currentPreview.stopRealTimePreview(tx);
      }
      // 重新分配通道
      Map<Integer, Collection<KvmPreviewAsso>> allChannels =
              dataGetter.getAllPreviewChannels(true);
      OptionalInt count = allChannels.entrySet().stream()
          .mapToInt((item) -> item.getValue().size()).reduce((lhs, rhs) -> lhs + rhs);
      if (!count.isPresent() || count.getAsInt() == 0) {
        log.info("No available channels");
      } else {
        // 分配轮询预览通道
        log.info(String.format("Total %d channels.", count.getAsInt()));
        int pollingPreviewCnt = Math
            .max((int) (count.getAsInt() * POLLING_CHANNEL_PERCENTAGE), 2); // 1/4通道作为预览
        pollingPreviewCnt = Math.min(count.getAsInt(), pollingPreviewCnt);
        log.info(String.format("%d channels for polling.", pollingPreviewCnt));
        Set<KvmPreviewAsso> pollingChannels = new HashSet<>();
        for (Map.Entry<Integer, Collection<KvmPreviewAsso>> entry : allChannels.entrySet()) {
          if (pollingPreviewCnt - pollingChannels.size() <= 0) {
            break;
          }
          pollingChannels.addAll(
              entry.getValue().stream().limit(pollingPreviewCnt - pollingChannels.size())
                  .toList());
        }
        currentPreview.setPollingPreviewChannels(pollingChannels);
        for (KvmPreviewAsso channel : pollingChannels) {
          log.info(String
              .format("Polling channel %d:%d at url %s", channel.getWallId(), channel.getSeq(),
                  channel.getUrl()));
        }
        // 分配实时预览通道
        for (String tx : currentPreview.getRealTimePreviewTxes()) {
          if (!checkPreviewTimeout(tx, currentPreview)) {
            resumePreviewImpl(tx, currentPreview);
          }
        }
        // 检查是否需要提升轮询为预览
        Set<String> unimportantRealTimeTx = new HashSet<>();
        Map<String, Boolean> importPollingTx = new HashMap<>();
        for (String tx : currentPreview.getRealTimePreviewTxes()) {
          if (currentPreview.getRealTimePreviewInfo(tx).status
              != RealTimePreviewStatus.REAL_TIME_STOPPED && !currentPreview.isImportant(tx)) {
            unimportantRealTimeTx.add(tx);
          }
        }
        for (String tx : currentPreview.getPollingPreviewTxes()) {
          if (currentPreview.getPollingPreviewStatus(tx) != PollingPreviewStatus.POLLING_STOPPED
              && currentPreview.isImportant(tx)) {
            importPollingTx.put(tx, dataGetter.is4kTx(tx));
          }
        }
        promoteImportantPollingTx(unimportantRealTimeTx, importPollingTx, currentPreview);

        // 打印
        for (String tx : currentPreview.getRealTimePreviewTxes()) {
          printUpdateStatus(tx, "resetChannels", currentPreview);
        }
        for (String tx : currentPreview.getPollingPreviewTxes()) {
          printUpdateStatus(tx, "resetChannels", currentPreview);
        }
      }
    }
  }

  @Override
  public void refresh(PreviewInfoOperator currentPreview) {
    synchronized (currentPreview) {
      // 删除超时请求
      Map<String, Long> requestKeyLastUsedTime = currentPreview.getAllRequestKeyLastUsedTime();
      for (Map.Entry<String, Long> entry : requestKeyLastUsedTime.entrySet()) {
        String requestKey = entry.getKey();
        if (System.currentTimeMillis() - entry.getValue() > REQUEST_KEY_TIME_OUT_MILLI
            && System.currentTimeMillis() - currentPreview
                .getTxConfinedToRequestLastUsedTime(requestKey) > REQUEST_KEY_TIME_OUT_MILLI) {
          log.info(String.format("Remove request key %s for timeout.", requestKey));
          currentPreview.removeRequestKey(requestKey);
        }
      }
      // 停止超时预览
      Collection<String> timeoutRealTimeTx = getTimeoutRealTimeTxImpl(currentPreview);
      Collection<String> timeoutPollingTx = getTimeoutPollingTxImpl(currentPreview);
      for (String tx : timeoutRealTimeTx) {
        currentPreview.stopRealTimePreview(tx);
      }
      for (String tx : timeoutPollingTx) {
        currentPreview.stopPollingPreview(tx);
      }
      Set<String> statusChangedTx = new HashSet<>();

      statusChangedTx.addAll(timeoutRealTimeTx);
      statusChangedTx.addAll(timeoutPollingTx);
      // 提升重要的轮询TX
      Collection<String> importantPollingTx = getImportantPollingTxImpl(currentPreview);
      if (!importantPollingTx.isEmpty()) {
        Collection<String> unimportantRealTimeTx = getUnimportantRealtimeTxImpl(currentPreview);
        statusChangedTx
            .addAll(promoteImportantPollingTx(unimportantRealTimeTx, importantPollingTx.stream()
                .collect(Collectors.toMap((e) -> e, (e) -> dataGetter.is4kTx(e))), currentPreview));
      }
      // 解决轮询TX太多的情况
      // 获取非停止状态的TX，其中4k TX会变成2个，方便计算个数
      Collection<String> pollingTx = currentPreview.getPollingPreviewTxes().stream().filter(
          (tx) -> currentPreview.getPollingPreviewStatus(tx)
              != PollingPreviewStatus.POLLING_STOPPED).map(tx -> dataGetter.is4kTx(tx) ? Arrays
          .asList(tx, tx) : Arrays.asList(tx)).flatMap(Collection::stream).collect(
          Collectors.toList());
      int pollingChannelSize = currentPreview.getPollingPreviewChannels().size();
      if (pollingChannelSize > 0 && pollingTx.size() / pollingChannelSize > MAX_POLLING_ITERATION) {
        for (String tx : pollingTx.stream()
            .limit(pollingTx.size() - pollingChannelSize * MAX_POLLING_ITERATION).collect(
                Collectors.toSet())) {
          boolean is4k = dataGetter.is4kTx(tx);
          KvmPreviewAsso channel = dataGetter
              .getAvailablePreviewChannel(is4k, true);
          if (channel == null) {
            break;
          }
          currentPreview.setRealTimePreview(tx, channel, is4k);
          statusChangedTx.add(tx);
        }
      }

      for (String tx : statusChangedTx) {
        printUpdateStatus(tx, "refresh", currentPreview);
      }
    }
  }

  @Override
  public PreviewInfoOperator getPreviewInfoOpt() {
    return currentPreview;
  }

  /**
   * 检查是否预览超时.
   *
   * @param txId           tx id
   * @param currentPreview 当前预览信息
   * @return 超时返回true
   */
  private boolean checkPreviewTimeout(String txId, PreviewInfoGetter currentPreview) {
    return System.currentTimeMillis() - currentPreview.getLastPreviewUsedTime(txId)
        > PREVIEW_TIME_OUT_MILLI;
  }

  /**
   * 恢复预览.
   *
   * @param txId           tx id
   * @param currentPreview 当前预览信息
   * @return 是否更新了预览状态
   */
  private boolean resumePreviewImpl(String txId, PreviewInfoOperator currentPreview) {
    if (currentPreview.isRealTimePreviewing(txId)
        && currentPreview.getRealTimePreviewInfo(txId).status
        == RealTimePreviewStatus.REAL_TIME_STOPPED) {
      resumeRealTimePreviewImpl(txId, currentPreview);
      return true;
    } else if (currentPreview.isPollingPreviewing(txId)
        && currentPreview.getPollingPreviewStatus(txId) == PollingPreviewStatus.POLLING_STOPPED) {
      if (currentPreview.isImportant(txId)) {
        resumeRealTimePreviewImpl(txId, currentPreview);
      } else {
        currentPreview.setPollingPreview(txId);
      }
      return true;
    }
    return false;
  }

  /**
   * 实时预览. 如果没有实时预览通道，转为轮询预览.
   *
   * @param txId           tx id
   * @param currentPreview 当前预览信息
   */
  private void resumeRealTimePreviewImpl(String txId, PreviewInfoOperator currentPreview) {
    boolean is4k = dataGetter.is4kTx(txId);
    KvmPreviewAsso channel = dataGetter.getAvailablePreviewChannel(is4k, true);
    if (channel == null) {
      currentPreview.setPollingPreview(txId);
    } else {
      currentPreview.setRealTimePreview(txId, channel, is4k);
    }
  }

  /**
   * 获取非停止状态的超时的实时预览.
   *
   * @param currentPreview 当前预览信息
   * @return 超时的实时预览列表
   */
  protected Collection<String> getTimeoutRealTimeTxImpl(PreviewInfoGetter currentPreview) {
    return currentPreview.getRealTimePreviewTxes().stream()
        .filter((tx) -> checkPreviewTimeout(tx, currentPreview)
            && currentPreview.getRealTimePreviewInfo(tx).status
            != RealTimePreviewStatus.REAL_TIME_STOPPED).collect(
            Collectors.toSet());
  }

  /**
   * 获取非停止状态的重要轮询列表.
   *
   * @param currentPreview 当前预览信息
   * @return 重要的轮询列表
   */
  protected Collection<String> getImportantPollingTxImpl(PreviewInfoGetter currentPreview) {
    return currentPreview.getPollingPreviewTxes().stream()
        .filter((tx) ->
            currentPreview.getPollingPreviewStatus(tx) != PollingPreviewStatus.POLLING_STOPPED
                && currentPreview.isImportant(tx)
        )
        .collect(
            Collectors.toSet());
  }

  /**
   * 获取非停止状态的不重要的实时预览TX列表.
   *
   * @param currentPreview 当前预览信息
   * @return 不重要的实时预览TX列表
   */
  protected Collection<String> getUnimportantRealtimeTxImpl(PreviewInfoGetter currentPreview) {
    return currentPreview.getRealTimePreviewTxes().stream()
        .filter((tx) -> currentPreview.getRealTimePreviewInfo(tx).status
            != RealTimePreviewStatus.REAL_TIME_STOPPED && !currentPreview.isImportant(tx)).collect(
            Collectors.toSet());
  }

  protected void printUpdateStatus(String txId, String updateSource,
      PreviewInfoGetter currentPreview) {
    String status = "not previewing";
    if (currentPreview.isRealTimePreviewing(txId)) {
      PreviewInfoGetter.RealTimePreviewInfo info = currentPreview.getRealTimePreviewInfo(txId);
      if (info.channel != null) {
        String res = info.channel.isHighResolution() ? "4k" : "2k";
        status = String
            .format("[%s][%s]Real time previewing %s. Channel %s:%d at url %s. Unused time:%dms.",
                currentPreview.isImportant(txId) ? "Important" : "Unimportant",
                res,
                info.status.toString(),
                info.channel.getWallId(),
                info.channel.getSeq(),
                info.channel.getUrl(),
                System.currentTimeMillis() - currentPreview.getLastPreviewUsedTime(txId)
            );
      }
    } else if (currentPreview.isPollingPreviewing(txId)) {
      PollingPreviewStatus info = currentPreview.getPollingPreviewStatus(txId);
      status = String.format("[%s]Polling previewing %s. Unused time:%dms",
          currentPreview.isImportant(txId) ? "Important" : "Unimportant",
          info.toString(),
          System.currentTimeMillis() - currentPreview.getLastPreviewUsedTime(txId));
    }
    log.info(String.format("Update %s preview status for %s. %s", txId, updateSource, status));
  }
}
