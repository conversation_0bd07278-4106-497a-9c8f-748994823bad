package com.mediacomm.caesar.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mediacomm.entity.message.reqeust.MqRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CaesarMqRequest.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class CaesarMqRequest<T> extends MqRequest<T> {
  private KvmOperationFrom kvmOperationFrom;
}
