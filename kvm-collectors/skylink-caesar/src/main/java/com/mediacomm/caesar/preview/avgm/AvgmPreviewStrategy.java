package com.mediacomm.caesar.preview.avgm;

import com.mediacomm.caesar.domain.avgm.TxConnectRequest;
import com.mediacomm.caesar.domain.avgm.TxConnectedStatusResponse;
import com.mediacomm.caesar.util.CaesarTcpClient;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResponseCode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class AvgmPreviewStrategy implements Runnable {
  private final AvgmDeviceDataGetter deviceDataGetter;
  private final CaesarTcpClient cli;
  // key:txId
  private Map<String, KvmPreviewAsso> txConnectStatus = new HashMap<>();
  //客户端状态更新时间点,key:deviceId,value:ms
  private Map<String, Long> deviceTime = new ConcurrentHashMap<>();
  // key:客户端,value:客户端当前预览的所有Tx
  private Map<String, Set<String>> clientCurrentTxMap = new HashMap<>();
  private volatile boolean checkLink = true;

  public AvgmPreviewStrategy(AvgmDeviceDataGetter deviceDataGetter,
                             KvmMaster master) {
    this.deviceDataGetter = deviceDataGetter;
    this.cli = new CaesarTcpClient(master.getDeviceIp());
    this.deviceDataGetter.addThread(this);
  }

  public void resetChannel() {
    Map<Integer, Collection<KvmPreviewAsso>> allChannels = this.deviceDataGetter.getAllPreviewChannels();
    if (allChannels.isEmpty()) {
      log.info("Avgm no available channels");
    } else {
      allChannels.keySet().forEach(key -> {
        KvmVideoWall wall = deviceDataGetter.getKvmPreviewWalls().get(key);
        Map<Integer, Integer> seqMap = new HashMap<>();
        for (int i = 1; i <= allChannels.get(key).size(); i++) {
          seqMap.put(i, 0);
        }
        Integer scenario = Property.findValueByKey(wall.getCollectorProperties(),
                PropertyKeyConst.PREVIEW_MODEL, null, Integer.class);
        try {
          cli.openPanel(scenario, seqMap);
        } catch (Exception e) {
          log.error(e.getMessage(), e);
        }
      });
    }
  }

  /**
   * 更新客户端状态.
   */
  public void updateClient(String clientId) {
    synchronized (deviceTime) {
      deviceTime.put(clientId, System.currentTimeMillis());
    }
  }

  /**
   * 根据客户端请求，创建预览连接，返回连接对应关系.
   */
  public Collection<TxConnectedStatusResponse> connectReviewAsso(TxConnectRequest request) {
    log.info("Avgm connectReviewAsso request:{}", request);
    String clientId = request.getClientId();
    synchronized (clientCurrentTxMap) {
      Set<String> oldTxIds = clientCurrentTxMap.get(clientId);
      clientCurrentTxMap.put(clientId, request.getTxIds());
      if (oldTxIds != null && !oldTxIds.isEmpty()) {
        Set<String> needCloseTx = new HashSet<>();
        for (String oldTxId : oldTxIds) {
          if (!request.getTxIds().contains(oldTxId)) {
            needCloseTx.add(oldTxId);
          }
        }
        closeTxs(clientId, needCloseTx);
      }
    }

    if (!deviceTime.containsKey(clientId)) {
      updateClient(clientId);
    }
    Collection<TxConnectedStatusResponse> response = new ArrayList<>();
    Map<Integer, Map<Integer, Integer>> scenarioMap = new HashMap<>();
    for (String txId : request.getTxIds()) {
      TxConnectedStatusResponse connectedStatus = new TxConnectedStatusResponse();
      connectedStatus.setTxId(txId);
      KvmPreviewAsso channel = txConnectStatus.get(txId);
      if (channel != null) {
        connectedStatus.setUrl(channel.getUrl());
        response.add(connectedStatus);
      } else {
        KvmAssetVo tx = deviceDataGetter.getKvmAsset(txId);
        if (tx != null) {
          boolean is4k = InspectCaesarUtil.is4k(tx);
          KvmPreviewAsso newChannel = deviceDataGetter.getAvailablePreviewChannel(is4k);
          if (newChannel != null) {
            // 建立新的连接
            KvmVideoWall wall = deviceDataGetter.getKvmPreviewWalls().get(newChannel.getWallId());
            Integer scenario = Property.findValueByKey(wall.getCollectorProperties(),
                    PropertyKeyConst.PREVIEW_MODEL, null, Integer.class);
            scenarioMap.computeIfAbsent(scenario, k -> new HashMap<>());
            scenarioMap.get(scenario).put(newChannel.getSeq(), tx.getDeviceId());
            connectedStatus.setUrl(newChannel.getUrl());
            txConnectStatus.put(txId, newChannel);
            newChannel.setUsed(true);
            newChannel.setHighResolution(is4k);
          } else {
            connectedStatus.setState(ResponseCode.OUT_OF_LIMIT_13005);
          }
          response.add(connectedStatus);
        }
      }
    }
    openPanels(scenarioMap);
    return response;
  }

  @Override
  public void run() {
    while (checkLink) {
      if (deviceTime.isEmpty()) {
        try {
          Thread.sleep(1000);
        } catch (Exception e) {
          log.error(e.getMessage(), e);
        }
      } else {
        long ms = System.currentTimeMillis();
        for (Map.Entry<String, Long> entry : deviceTime.entrySet()) {
          long delay = ms - entry.getValue();
          String clientId = entry.getKey();
          if (delay > 15000) {
            synchronized (deviceTime) {
              deviceTime.remove(clientId);
            }
            Set<String> needCloseTx = clientCurrentTxMap.remove(clientId);
            log.info("the Caesar AVGM client {} timeout:{}", clientId, delay);
            closeTxs(clientId, needCloseTx);
          }
        }
      }

    }
  }

  public void stop() {
    checkLink = false;
  }

  private void closeTxs(String clientId, Set<String> needCloseTx) {
    log.info("Avgm closeTxs request:{} {}", clientId, needCloseTx);
    if (needCloseTx == null || needCloseTx.isEmpty()) {
      return;
    }
    Iterator<String> iterator = needCloseTx.iterator();
    while (iterator.hasNext()) {
      String closeTx = iterator.next();
      // 其他客户端没有预览该tx，释放资源
      clientCurrentTxMap.forEach((k, v) -> {
        if (!clientId.equals(k) && v.contains(closeTx)) {
          needCloseTx.remove(closeTx);
        }
      });
    }
    Map<Integer, Map<Integer, Integer>> scenarioMap = new HashMap<>();
    for (String closeTx : needCloseTx) {
      KvmPreviewAsso channel = txConnectStatus.remove(closeTx);
      if (channel != null) {
        KvmVideoWall wall = deviceDataGetter.getKvmPreviewWalls().get(channel.getWallId());
        Integer scenario = Property.findValueByKey(wall.getCollectorProperties(),
                PropertyKeyConst.PREVIEW_MODEL, null, Integer.class);
        scenarioMap.computeIfAbsent(scenario, k -> new HashMap<>());
        scenarioMap.get(scenario).put(channel.getSeq(), 0);
        channel.reset();
      }
    }
    openPanels(scenarioMap);
  }

  private void openPanels(Map<Integer, Map<Integer, Integer>> scenarioMap) {
    scenarioMap.forEach((k, v) -> {
      try {
        cli.openPanel(k, v);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    });
  }
}
