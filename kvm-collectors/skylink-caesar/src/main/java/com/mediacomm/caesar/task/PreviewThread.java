package com.mediacomm.caesar.task;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.mediacomm.caesar.preview.PreviewInfoGetter;
import com.mediacomm.caesar.preview.device.PreviewDeviceOperator;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import java.util.Collection;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class PreviewThread implements Runnable {
  @Setter
  protected PreviewInfoGetter previewInfo;
  protected DeviceDataGetter dataGetter;
  @Setter
  protected PreviewDeviceOperator operator;
  protected volatile boolean stop = false;
  @Getter
  private volatile boolean running = false;

  public abstract String getThreadName();

  protected abstract void execute();

  public abstract boolean isStop();

  @Override
  public void run() {
    Thread.currentThread().setName(getThreadName());
    running = true;
    while (!isStop()) {
      try {
        execute();
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
    running = false;
  }

  /**
   * 获取电视墙KvmVideoWall与KvmPreviewAsso的对应关系.
   *
   * @param channels 所有可预览的通道.
   * @return 电视墙Id对应的预览通道的Map.
   */
  public Map<Integer, Collection<KvmPreviewAsso>> getKvmVideoWallWithReviewAsso(
      Collection<KvmPreviewAsso> channels) {
    Multimap<Integer, KvmPreviewAsso> kvmHttpReviewMultimap = ArrayListMultimap.create();
    for (KvmPreviewAsso asso : channels) {
      kvmHttpReviewMultimap.put(asso.getWallId(), asso);
    }
    return kvmHttpReviewMultimap.asMap();
  }

  public abstract void stop();
}
