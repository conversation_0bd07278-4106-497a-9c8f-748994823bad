package com.mediacomm.caesar.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * CaesarDeviceStatus.
 * 主机版本 ≥ v5.1.0_beta.28, 将多个状态整合为一个数组，并同时保留了旧的字段.
 */
@Slf4j
@Data
@EqualsAndHashCode(exclude = {"edidValid", "videoInput", "videoLineStatus"})
public class CaesarDeviceStatus {
  private int deviceId;
  private String deviceType;
  private transient boolean edid1Valid;
  private transient boolean edid2Valid;
  private List<Boolean> edidValid = new ArrayList<>();
  @JsonProperty("video1Input")
  private transient boolean video1Input;
  @JsonProperty("video2Input")
  private transient boolean video2Input;
  private List<Boolean> videoInput = new ArrayList<>();
  private boolean linkStatus;
  private transient boolean videoLine1Status;
  private transient boolean videoLine2Status;
  private List<Boolean> videoLineStatus = new ArrayList<>();
  private String loginUser;
  private int mouseCount;
  private int keyboardCount;
  private int udiskCount;
  private int touchCount;
  @JsonProperty("typeBStatus")
  private boolean typebStatus;
  private int inputWidth;
  private int inputHeight;
  private List<ConnectStatus> connectStatusList = new ArrayList<>();
  private int link1Port;
  private int link2Port;
  private boolean isVpcon;

  /**
   * 兼容api旧版本,统一处理数组中的状态值.
   */
  public List<Boolean> getEdidValid() {
    if (edidValid.isEmpty()) {
      edidValid.add(edid1Valid);
      edidValid.add(edid2Valid);
    }
    return edidValid;
  }

  /**
   * 兼容api旧版本,统一处理数组中的状态值.
   */
  public List<Boolean> getVideoInput() {
    if (videoInput.isEmpty()) {
      videoInput.add(video1Input);
      videoInput.add(video2Input);
    }
    return videoInput;
  }

  /**
   * 兼容api旧版本,统一处理数组中的状态值.
   */
  public List<Boolean> getVideoLineStatus() {
    if (videoLineStatus.isEmpty()) {
      videoLineStatus.add(videoLine1Status);
      videoLineStatus.add(videoLine2Status);
    }
    return videoLineStatus;
  }
}
