package com.mediacomm.caesar.domain.kaito.alpha;

import com.mediacomm.caesar.domain.kaito.KaitoScreenDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阿尔法-设置屏幕OSD文字信息.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KaitoAlphaWordOsdRequestBody {
  private int enable;
  private int height;
  private int isJudge;
  private int type = 0;
  private int width;
  private KaitoScreenDetail.Words words;
  private int x;
  private int y;
  private int screenId;
  private int deviceId = 0;
}
