package com.mediacomm.caesar.preview.device;

import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.preview.strategy.DeviceDataGetter;
import com.mediacomm.caesar.util.CaesarTcpClient;
import com.mediacomm.caesar.util.DownloadUtil;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmPreviewAsso;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.exception.SkyLinkException;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.util.task.SkyLinkTaskPool;
import java.io.IOException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;

/**
 * caesar preview.
 */
@Slf4j
public class PreviewDeviceOperatorImpl implements PreviewDeviceOperator {

  private final CaesarPreviewStrategyMode strategyMode = CaesarPreviewStrategyMode.PREVIEW_STRATEGY_VP6;

  static class TxPreviewInfo {

    /**
     * 保存每个TX的预览图的地址.
     */
    private Map<String, String> txUrlMap = new ConcurrentHashMap<>();

    /**
     * 保存预览图地址对应的TX.
     */
    private Map<String, String> urlTxMap = new ConcurrentHashMap<>();

    /**
     * 保存每个TX当前开窗或者上一次开窗(当前没有开窗)的视频墙.
     */
    private Map<String, Integer> txVideoWallMap = new ConcurrentHashMap<>();

    /**
     * 更新TX对应的视频墙. 调用前需要锁定TX与对应的视频墙.
     *
     * @param tx          TX ID
     * @param videoWallId 视频墙ID
     */
    public void updateTxVideoWall(String tx, Integer videoWallId) {
      txVideoWallMap.put(tx, videoWallId);
    }

    /**
     * 获取TX开窗或上一次开窗的视频墙. 调用前需要锁定TX.
     *
     * @param tx TX ID
     * @return 视频墙ID
     */
    public Integer getTxVideoWall(String tx) {
      return txVideoWallMap.get(tx);
    }

    /**
     * 更新TX对应的预览图地址. 调用前需要锁定TX与URL对应的视频墙.
     *
     * @param tx  TX ID
     * @param url 预览图地址
     */
    public void updateTxUrl(String tx, String url) {
      if (!tx.isEmpty()) {
        txUrlMap.put(tx, url);
      }
      if (!url.isEmpty()) {
        urlTxMap.put(url, tx);
      }
    }

    /**
     * 获取TX对应的预览图地址. 调用前锁定TX与其最近对应的URL的视频墙.
     *
     * @param tx TX ID
     * @return 预览图地址.
     */
    public String getTxUrl(String tx) {
      if (txUrlMap.containsKey(tx)) {
        if (!Objects.equals(urlTxMap.get(txUrlMap.get(tx)), tx)) {
          txUrlMap.remove(tx);
          txVideoWallMap.remove(tx);
          return null;
        } else {
          return txUrlMap.get(tx);
        }
      } else {
        return null;
      }
    }

  }

  /**
   * 开窗到拿图间的时间差.
   */
  protected static final int OPEN_PANEL_DELAY_MILLI = 500;


  /**
   * 保存每个ID的锁.
   */
  private List<Lock> locks = new ArrayList<>();
  /**
   * 保存每个ID的锁在locks对应的位置.
   */
  private Map<String, Integer> lockIndex = new ConcurrentHashMap<>();
  /**
   * 标记TX是否正在获取预览图. 获取预览图前标记为true，获取预览图后无论成功与否，都标记为false.
   */
  private Map<String, Boolean> isGettingPreview = new ConcurrentHashMap<>();

  private TxPreviewInfo currentPreviewInfo = new TxPreviewInfo();

  private SkyLinkTaskPool taskRunner;

  private DeviceDataGetter deviceDataGetter;

  private CloseableHttpAsyncClient httpClient;

  /**
   * .
   */
  public PreviewDeviceOperatorImpl(SkyLinkTaskPool taskRunner, DeviceDataGetter dataGetter) {
    this.taskRunner = taskRunner;
    this.deviceDataGetter = dataGetter;
    try {
      ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor();
      PoolingNHttpClientConnectionManager cm =
          new PoolingNHttpClientConnectionManager(ioReactor);
      cm.setMaxTotal(50);
      cm.setDefaultMaxPerRoute(8);
      httpClient =
          HttpAsyncClients.custom().setConnectionManager(cm).build();
    } catch (IOReactorException e) {
      log.error("Fail to create DefaultConnectingIOReactor", e);
      httpClient = HttpAsyncClients.createDefault();
    }
    httpClient.start();
  }

  @Override
  public void close() throws IOException {
    httpClient.close();
    synchronized (locks) {
      lockIndex.clear();
      locks.clear();
    }
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanel(
      Map<String, KvmPreviewAsso> txChannelMap) {
    // 根据videowallid分组
    Map<Integer, Map<String, KvmPreviewAsso>> group = txChannelMap.entrySet().stream().collect(
        Collectors.groupingBy(item -> item.getValue().getWallId(),
            Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
    result.complete(new HashMap<>());
    for (Map.Entry<Integer, Map<String, KvmPreviewAsso>> entry : group.entrySet()) {
      final Integer videoWallId = entry.getKey();
      // 获取不使用的通道
      Collection<KvmPreviewAsso> previewChannels = new HashSet<>(deviceDataGetter
          .getPreviewChannels(videoWallId));
      previewChannels.removeAll(entry.getValue().values());
      final Collection<KvmPreviewAsso> unusedChannels = previewChannels.stream()
          .filter(e -> !e.isUsed())
          .collect(Collectors.toSet());
      result = result.thenCombine(
          taskRunner
              .addAsyncTask(() -> openPanelImpl(videoWallId, entry.getValue(), unusedChannels)),
          (lhs, rhs) -> {
            Map<String, Boolean> newResult = new HashMap<>(lhs);
            newResult.putAll(rhs);
            return newResult;
          });
    }
    return result;
  }

  @Override
  public CompletableFuture<Boolean> closePanels(Collection<KvmPreviewAsso> channels) {
    // 根据videowallid分组
    Map<Integer, Set<KvmPreviewAsso>> channelsByVideoWallId = channels.stream()
        .collect(Collectors.groupingBy(KvmPreviewAsso::getWallId, Collectors.toSet()));
    CompletableFuture<Boolean> result = new CompletableFuture<>();
    result.complete(Boolean.TRUE);
    for (Map.Entry<Integer, Set<KvmPreviewAsso>> entry : channelsByVideoWallId.entrySet()) {
      final Integer videoWallId = entry.getKey();
      result = result.thenCombine(
          taskRunner
              .addAsyncTask(() ->
                  openPanelImpl(videoWallId, Collections.emptyMap(), entry.getValue())),
          (lhs, rhs) -> true);
    }
    return result;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> openPanelAndGetPreview(
      Map<String, KvmPreviewAsso> txChannelMap,
      Map<String, String> txSavePathMap,
      Collection<KvmPreviewAsso> unusedChannels) {
    // 根据videowallid分组
    Map<Integer, Map<String, KvmPreviewAsso>> group = txChannelMap.entrySet().stream().collect(
        Collectors.groupingBy(item -> item.getValue().getWallId(),
            Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    Map<Integer, Set<KvmPreviewAsso>> unusedChannelGroup = unusedChannels.stream().collect(
        Collectors.groupingBy(KvmPreviewAsso::getWallId, Collectors.toSet()));
    //
    CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
    result.complete(new HashMap<>());
    for (Map.Entry<Integer, Map<String, KvmPreviewAsso>> entry : group.entrySet()) {
      Integer videoWallId = entry.getKey();
      // 收集不使用的通道
      Collection<KvmPreviewAsso> previewChannels = deviceDataGetter
          .getPreviewChannels(videoWallId);
      final Set<KvmPreviewAsso> unusedChannelsByVideoWall = previewChannels.stream()
          .filter(e -> !e.isUsed())
          .collect(Collectors.toSet());
      if (unusedChannelGroup.containsKey(videoWallId)) {
        unusedChannelsByVideoWall.addAll(unusedChannelGroup.get(videoWallId));
      }
      unusedChannelsByVideoWall.removeAll(entry.getValue().values());
      result = result.thenCombine(
          taskRunner.addAsyncTask(
              () -> openPanelAndGetPreviewImpl(videoWallId, entry.getValue(),
                  txSavePathMap, unusedChannelsByVideoWall)),
          (lhs, rhs) -> {
            Map<String, Boolean> newResult = new HashMap<>(lhs);
            newResult.putAll(rhs);
            return newResult;
          });
    }
    return result;
  }

  @Override
  public CompletableFuture<Map<String, Boolean>> getPreview(
      Map<String, String> txSavePathMap) {
    CompletableFuture<Map<String, Boolean>> result = new CompletableFuture<>();
    Set<String> unfinishedTx = txSavePathMap.keySet().stream()
        .filter(tx -> Boolean.TRUE.equals(isGettingPreview.get(tx))).collect(
            Collectors.toSet());
    // 先设置结果为false
    result.complete(txSavePathMap.entrySet().stream()
        .collect(Collectors
            .toMap(Map.Entry::getKey, entry -> unfinishedTx.contains(entry.getKey()))));
    Map<String, String> newTxSavePathMap = new HashMap<>(txSavePathMap);
    newTxSavePathMap.keySet().removeAll(unfinishedTx);
    // 根据视频墙分组
    Map<Integer, Map<String, String>> txSavePathMapGroupTyVideoWall = newTxSavePathMap.entrySet()
        .stream()
        .map(item -> new AbstractMap.SimpleImmutableEntry<>(
            currentPreviewInfo.getTxVideoWall(item.getKey()), item))
        .filter(item -> item.getKey() != null)
        .collect(Collectors.groupingBy(AbstractMap.SimpleImmutableEntry::getKey,
            Collectors.toMap(e -> e.getValue().getKey(), e -> e.getValue().getValue())));

    // 开启任务
    for (Map.Entry<Integer, Map<String, String>> entry : txSavePathMapGroupTyVideoWall
        .entrySet()) {
      final Integer videoWallId = entry.getKey();
      final Map<String, String> map = entry.getValue();
      if (videoWallId != null) {
        map.keySet().forEach(tx -> isGettingPreview.put(tx, true));
        result = result
            .thenCombine(taskRunner.addAsyncTask(() -> getPreviewImpl(videoWallId, map)),
                (lhs, rhs) -> {
                  lhs.putAll(rhs);
                  return lhs;
                });
      }
    }
    return result;
  }

  @Override
  public CaesarPreviewStrategyMode getPreviewStrategyMode() {
    return strategyMode;
  }

  protected Map<String, Boolean> getPreviewFromDevice(Map<String, String> urlSavePathMap) {
    return DownloadUtil.saveImageAsync(httpClient, urlSavePathMap);
  }

  protected void openPanelAtDevice(Integer videoWallId, Map<String, Integer> txChannelMap,
                                   Collection<Integer> unusedChannels)
      throws SkyLinkException, InterruptedException {
    CaesarTcpClient cli = new CaesarTcpClient(deviceDataGetter.getMasterIp());
    KvmVideoWall videoWall = deviceDataGetter.getKvmPreviewAssoWallById(videoWallId);
    if (videoWall == null) {
      throw new SkyLinkException(String.format("Fail to find video wall by id %s!", videoWallId));
    }
    // 转换成凯撒的实际的ID
    Map<Integer, Integer> seqTxMap = txChannelMap.entrySet().stream().collect(Collectors
        .toMap(Map.Entry::getValue,
            e -> deviceDataGetter.getExtendDevice(e.getKey()).getDeviceId()
        ));
    // 关闭窗口
    for (Integer seq : unusedChannels) {
      seqTxMap.put(seq, 0);
    }
    cli.openPanel(Property.findValueByKey(videoWall.getCollectorProperties(),
            PropertyKeyConst.PREVIEW_MODEL, null, Integer.class), seqTxMap);
    Thread.sleep(OPEN_PANEL_DELAY_MILLI);
  }

  private Map<String, Boolean> openPanelImpl(Integer videoWallId,
                                             Map<String, KvmPreviewAsso> txChannelMap,
                                             Collection<KvmPreviewAsso> unusedChannels) {
    Map<String, Boolean> result = new HashMap<>();
    // 先把结果设置成false
    for (String tx : txChannelMap.keySet()) {
      result.put(tx, false);
    }
    List<Lock> txLocks = acquireTxLock(txChannelMap.keySet());
    List<Lock> videoWallLocks = acquireVideoWallLock(Collections.singletonList(videoWallId));
    try {
      // 保存对应的videowallid
      for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
        currentPreviewInfo.updateTxVideoWall(entry.getKey(), videoWallId);
      }
      // 做实际的开窗操作
      openPanelAtDevice(videoWallId, txChannelMap.entrySet().stream()
              .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getSeq())),
          unusedChannels.stream().map(KvmPreviewAsso::getSeq).collect(
              Collectors.toSet()));
      // 保存TX预览路径与结果
      for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
        currentPreviewInfo.updateTxUrl(entry.getKey(), entry.getValue().getUrl());
        result.put(entry.getKey(), true);
      }
      for (KvmPreviewAsso channel : unusedChannels) {
        currentPreviewInfo.updateTxUrl("", channel.getUrl());
      }
    } catch (Exception exception) {
      log.warn("Fail to openPanel!", exception);
    } finally {
      releaseLock(videoWallLocks);
      releaseLock(txLocks);
    }
    return result;
  }

  private Map<String, Boolean> openPanelAndGetPreviewImpl(Integer videoWallId,
                                                          Map<String, KvmPreviewAsso> txChannelMap,
                                                          Map<String, String> savePath,
                                                          Collection<KvmPreviewAsso>
                                                              unusedChannels) {
    Map<String, Boolean> result = new HashMap<>();
    // 先把结果设置成false
    for (String tx : txChannelMap.keySet()) {
      result.put(tx, false);
    }
    List<Lock> txLocks = acquireTxLock(txChannelMap.keySet());
    List<Lock> videoWallLocks = acquireVideoWallLock(Collections.singletonList(videoWallId));
    try {
      // 保存对应的videowallid
      for (Map.Entry<String, KvmPreviewAsso> entry : txChannelMap.entrySet()) {
        currentPreviewInfo.updateTxVideoWall(entry.getKey(), videoWallId);
      }
      // 开窗
      openPanelAtDevice(videoWallId, txChannelMap.entrySet().stream()
              .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getSeq())),
          unusedChannels.stream().map(KvmPreviewAsso::getSeq).collect(Collectors.toSet()));
      // 下载预览图
      Map<String, String> urlSavePathMap = new HashMap<>();
      Map<String, String> urlTxMap = new HashMap<>();
      for (Map.Entry<String, String> entry : savePath.entrySet()) {
        String tx = entry.getKey();
        String path = entry.getValue();
        KvmPreviewAsso channel = txChannelMap.get(tx);
        if (channel != null) {
          currentPreviewInfo.updateTxUrl(tx, channel.getUrl());
          urlSavePathMap.put(channel.getUrl(), path);
          urlTxMap.put(channel.getUrl(), tx);
        }
      }
      for (KvmPreviewAsso channel : unusedChannels) {
        currentPreviewInfo.updateTxUrl("", channel.getUrl());
      }
      getPreviewFromDevice(urlSavePathMap).forEach((url, ret) -> {
        if (urlTxMap.containsKey(url)) {
          result.put(urlTxMap.get(url), ret);
        }
      });

    } catch (SkyLinkException | InterruptedException | RuntimeException exception) {
      log.warn("Fail to openPanelAndGetPreview!", exception);
    } finally {
      releaseLock(videoWallLocks);
      releaseLock(txLocks);
    }
    return result;
  }

  private Map<String, Boolean> getPreviewImpl(Integer videoWallId, Map<String, String> txSaveMap) {
    List<Lock> txLocks = acquireTxLock(txSaveMap.keySet());
    List<Lock> videoWallLocks = acquireVideoWallLock(Collections.singletonList(videoWallId));
    try {
      Map<String, Boolean> result = new HashMap<>();
      // 默认false
      txSaveMap.keySet().forEach(tx -> result.put(tx, false));
      // 筛选需要拿图的TX
      Iterator<Map.Entry<String, String>> iterator = txSaveMap.entrySet().iterator();
      while (iterator.hasNext()) {
        Map.Entry<String, String> entry = iterator.next();
        String tx = entry.getKey();
        if (!videoWallId.equals(currentPreviewInfo.getTxVideoWall(entry.getKey()))) {
          log.warn(String
              .format("Fail to get %s preview for mismatch videoWallId, expected:%s, actual:%s.",
                  tx, videoWallId, currentPreviewInfo.getTxVideoWall(entry.getKey())));
          iterator.remove();
        } else if (currentPreviewInfo.getTxUrl(tx) == null) {
          log.warn(String.format("Fail to get %s preview for null url.", tx));
          iterator.remove();
        }
      }
      // 获取URL
      Map<String, String> urlTxMap = txSaveMap.entrySet().stream()
          .collect(Collectors.toMap(entry -> currentPreviewInfo.getTxUrl(entry.getKey()),
              Map.Entry::getKey));
      Map<String, String> urlSavePathMap = urlTxMap.entrySet().stream().collect(
          Collectors.toMap(Map.Entry::getKey, entry -> txSaveMap.get(entry.getValue())));
      // 获取图片
      result.keySet().forEach(tx -> isGettingPreview.put(tx, false));
      getPreviewFromDevice(urlSavePathMap)
          .forEach((url, ret) -> result.put(urlTxMap.get(url), ret));
      return result;
    } finally {
      releaseLock(videoWallLocks);
      releaseLock(txLocks);
    }
  }

  private List<Lock> acquireTxLock(Collection<String> ids) {
    // 加上TX前缀
    return acquireLock(ids.stream().map(id -> "tx." + id).collect(Collectors.toList()));
  }

  private List<Lock> acquireVideoWallLock(Collection<Integer> ids) {
    // 加上videowall前缀
    return acquireLock(ids.stream().map(id -> "videowall." + id).collect(Collectors.toList()));
  }

  private List<Lock> acquireLock(Collection<String> ids) {
    List<Lock> sortedLocks;
    synchronized (locks) {
      Map<String, Integer> needLockIndex = new HashMap<>();
      for (String tx : ids) {
        if (!lockIndex.containsKey(tx)) {
          int index = locks.size();
          lockIndex.put(tx, locks.size());
          locks.add(new ReentrantLock());
          log.info(String.format("Create lock for %s at %d", tx, index));
        }
        needLockIndex.put(tx, lockIndex.get(tx));
      }
      // 对锁排序
      sortedLocks = needLockIndex.values().stream()
          .sorted(Integer::compare)
          .map((item) -> locks.get(item))
          .collect(Collectors.toList());
    }

    for (Lock lock : sortedLocks) {
      lock.lock();
    }
    return sortedLocks;
  }

  private void releaseLock(List<Lock> toReleasedLocks) {
    Collections.reverse(toReleasedLocks);
    for (Lock lock : toReleasedLocks) {
      lock.unlock();
    }
  }
}
