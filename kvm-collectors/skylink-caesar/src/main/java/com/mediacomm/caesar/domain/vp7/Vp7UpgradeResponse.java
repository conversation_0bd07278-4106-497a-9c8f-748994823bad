package com.mediacomm.caesar.domain.vp7;

import lombok.Data;

/**
 * .
 */
@Data
public class Vp7UpgradeResponse {
  private int taskId;
  private State currentState;
  private String statusDetail;
  private int nextPollDelay; // 建议调用方在x秒后再次查询状态
  private int progressPercent;

  /**
   * ACCEPTED,已接受,成功接收升级指令，任务已加入队列，即将开始
   * DOWNLOADING,下载中,正在从指定的URL下载升级包
   * VERIFYING,校验中,升级包下载完成，正在进行完整性校验
   * INSTALLING,安装中,校验通过，正在将固件写入闪存
   * REBOOTING,重启中,固件安装完成，设备正在重启以应用新版本
   * COMPLETED,升级成功,设备已成功重启并运行新版
   * REJECTED,已拒绝,设备拒绝了升级请求
   * FAILED,升级失败,在升级过程中的某个环节失败
   * CANCELLED,升级取消
   */
  public enum State {
    ACCEPTED,
    VERIFYING,
    DOWNLOADING,
    INSTALLING,
    REBOOTING,
    COMPLETED,
    REJECTED,
    FAILED,
    CANCELLED
  }
}
