package com.mediacomm.caesar.controller;

import com.mediacomm.caesar.domain.kaito.KaitoDeviceIdReq;
import com.mediacomm.caesar.domain.kaito.KaitoGroupData;
import com.mediacomm.caesar.domain.kaito.KaitoLayerReq;
import com.mediacomm.caesar.domain.kaito.KaitoRequestBody;
import com.mediacomm.caesar.domain.kaito.KaitoResponseBody;
import com.mediacomm.caesar.domain.kaito.KaitoScreenDetail;
import com.mediacomm.caesar.domain.kaito.KaitoScreenOsdReq;
import com.mediacomm.caesar.domain.kaito.KaitoVideoServerInfo;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaRequestBody;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaResponseBody;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaScreenDetail;
import com.mediacomm.caesar.domain.kaito.alpha.KaitoAlphaWordOsdRequestBody;
import com.mediacomm.util.JsonUtils;
import jakarta.annotation.Resource;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 直接向嗨动主机发送请求的指令.
 */
@Component
@Slf4j
public class CaesarKaitoCmd {
  private static final String E = "E";
  private static final String ALPHA = "ALPHA";
  @Resource
  private CaesarFeignClientApi cli;

  /**
   * 获取视频服务器信息.
   */
  public KaitoVideoServerInfo getVideoServerInfo(URI kaitoUrl, KaitoGroupData groupData) {
    KaitoDeviceIdReq deviceIdReq = new KaitoDeviceIdReq(0);
    KaitoVideoServerInfo info = null;
    if (E.equals(groupData.getType())) {
      KaitoRequestBody<KaitoDeviceIdReq> deviceIdReqBody =
              getKaitoRequestBody(groupData, deviceIdReq);
      KaitoResponseBody videoServerInfo = cli.getVideoServerInfo(kaitoUrl, deviceIdReqBody);
      info = JsonUtils.decode(videoServerInfo.getBody(), KaitoVideoServerInfo.class);
      if (info == null) {
        log.error(String.format("%s%s", "E getVideoServerInfo failed!",
                JsonUtils.encode(videoServerInfo)));
      } else {
        URI rtsp = URI.create(info.getMvrUrl());
        String wsMvr = "ws://" + rtsp.getHost() + ":" + (rtsp.getPort() - 10); // E系列的实际Mvr地址
        info.setMvrUrl(wsMvr);
      }
    } else if (ALPHA.equals(groupData.getType())) {
      KaitoAlphaResponseBody<KaitoVideoServerInfo> responseBody = cli.getVideoServerInfo(kaitoUrl,
              getKaitoAlphaRequestBody(groupData, deviceIdReq));
      info = responseBody.getBody();
      if (StringUtils.isEmpty(info.getConfigUrl())) {
        URI mvrUri = URI.create(info.getMvrUrl());
        info.setConfigUrl(String.format("ws://%s:7999/configVideo", mvrUri.getHost()));
      }
    }
    return info;
  }

  /**
   * 获取屏幕详情.
   *
   * @param kaitoUrl 嗨动url.
   * @param groupData 嗨动主机信息.
   * @param screenId 屏幕id.
   * @return 屏幕详情.
   */
  public KaitoScreenDetail getScreenReadDetail(URI kaitoUrl, KaitoGroupData groupData, Integer screenId) {
    KaitoLayerReq reqBody = new KaitoLayerReq(0, screenId);
    KaitoScreenDetail detail = null;
    if (E.equals(groupData.getType())) {
      KaitoResponseBody kaitoScreenDetail = cli.getScreenReadDetail(kaitoUrl,
              getKaitoRequestBody(groupData, reqBody));
      detail = JsonUtils.decode(kaitoScreenDetail.getBody(), KaitoScreenDetail.class);
      if (detail == null) {
        log.error("Failed to obtain details about the screen from E. response: {}", kaitoScreenDetail);
      }
    } else if (ALPHA.equals(groupData.getType())) {
      KaitoAlphaResponseBody<KaitoAlphaScreenDetail> responseBody = cli.getScreenReadDetail(kaitoUrl,
              getKaitoAlphaRequestBody(groupData, reqBody));
      detail = new KaitoScreenDetail();
      detail.setScreenId(responseBody.getBody().getScreenId());
      detail.setBrightness(responseBody.getBody().getBrightness());
      detail.setGamma(responseBody.getBody().getGamma());
      detail.setMayUse(responseBody.getBody().getMayUse());
      detail.setOsd(responseBody.getBody().getOsd());
      detail.setOsdImage(responseBody.getBody().getOsdImage());
      detail.setOutputMode(responseBody.getBody().getOutputMode());
      detail.setGeneral(responseBody.getBody().getGeneral());
      detail.setDeviceId(responseBody.getBody().getDeviceId());
    }
    return detail;
  }

  /**
   * 启用/关闭banner.
   */
  public KaitoScreenDetail.Osd enableBanner(URI kaitoUrl, KaitoGroupData groupData, Integer screenId, boolean enable) {
    KaitoScreenDetail.Osd osd = null;
    KaitoScreenDetail detail;
    if (E.equals(groupData.getType())) {
      KaitoScreenOsdReq requestParam =
              new KaitoScreenOsdReq(0, screenId, enable ? 1 : 0);
      KaitoRequestBody<KaitoScreenOsdReq> kaitoScreenDetailReqBody =
              getKaitoRequestBody(groupData, requestParam);
      KaitoResponseBody responseBody = cli.enableBanner(kaitoUrl, kaitoScreenDetailReqBody);
      detail = JsonUtils.decode(responseBody.getBody(), KaitoScreenDetail.class);
      if (detail != null && detail.getOsd() != null) {
        osd = detail.getOsd();
      } else {
        log.error("Failed to obtain details about the E screen. response: {}", responseBody);
      }
    } else if (ALPHA.equals(groupData.getType())) {
      detail = getScreenReadDetail(kaitoUrl, groupData, screenId);
      if (detail != null) {
        KaitoScreenDetail.Osd currentSrceenOsd = detail.getOsd();
        if (currentSrceenOsd != null) {
          KaitoAlphaWordOsdRequestBody requestBody = KaitoAlphaWordOsdRequestBody.builder()
                  .words(detail.getOsd().getWords())
                  .enable(enable ? 1 : 0)
                  .height(currentSrceenOsd.getHeight())
                  .width(currentSrceenOsd.getWidth())
                  .x(currentSrceenOsd.getX())
                  .y(currentSrceenOsd.getY())
                  .isJudge(currentSrceenOsd.getIsJudge())
                  .type(currentSrceenOsd.getType())
                  .screenId(screenId)
                  .build();
          cli.enableBanner(kaitoUrl, getKaitoAlphaRequestBody(groupData, requestBody));
        }
        detail = getScreenReadDetail(kaitoUrl, groupData, screenId);
        osd = detail.getOsd();
      } else {
        log.error("Failed to obtain details about the ALPHA screen.");
      }
    }
    return osd;
  }

  private <R> KaitoRequestBody<R> getKaitoRequestBody(KaitoGroupData groupData, R body) {
    KaitoRequestBody<R> kaitoScreenDetailReqBody = new KaitoRequestBody<>();
    kaitoScreenDetailReqBody.setBody(body);
    kaitoScreenDetailReqBody.setPId(groupData.getPId());
    kaitoScreenDetailReqBody.setSign(groupData.getSecreteKey());
    return kaitoScreenDetailReqBody;
  }

  private <T> KaitoAlphaRequestBody<T> getKaitoAlphaRequestBody(KaitoGroupData groupData, T body) {
    KaitoAlphaRequestBody<T> kaitoAlphaRequestBody = new KaitoAlphaRequestBody<>();
    kaitoAlphaRequestBody.setBody(body);
    kaitoAlphaRequestBody.setPId(groupData.getPId());
    kaitoAlphaRequestBody.setSign();
    return kaitoAlphaRequestBody;
  }
}
