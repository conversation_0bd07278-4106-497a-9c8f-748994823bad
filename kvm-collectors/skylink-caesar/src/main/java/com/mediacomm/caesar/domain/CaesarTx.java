package com.mediacomm.caesar.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarTx.
 */
@Data
public class CaesarTx implements CaesarDeviceSameField {
  private int id;
  private String name;
  private String sn;
  private String deviceType;
  private String deviceModel;
  private String softVersion;
  private String systemVersion;
  private String fpgaVersion;
  private boolean redundant;
  private int videoNumber;
  // 分辨率.  0:2k   1:4k@30Hz    2:4k@60Hz
  private int videoResolutionType;
  private int videoIntfType;
  private int link1Port;
  private int link2Port;
  private List<CaesarProperty> properties = new ArrayList<>();
}
