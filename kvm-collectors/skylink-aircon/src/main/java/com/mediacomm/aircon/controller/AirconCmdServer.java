package com.mediacomm.aircon.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mediacomm.aircon.domain.AirconErrorCode;
import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.aircon.domain.AirconProperty;
import com.mediacomm.aircon.domain.AirconRx;
import com.mediacomm.aircon.domain.AirconSeat;
import com.mediacomm.aircon.domain.AirconSeatPanels;
import com.mediacomm.aircon.domain.AirconServer;
import com.mediacomm.aircon.domain.AirconTx;
import com.mediacomm.aircon.domain.AirconVideoPanels;
import com.mediacomm.aircon.domain.AirconVideoWall;
import com.mediacomm.aircon.preview.AirconPreviewManager;
import com.mediacomm.aircon.util.InspectAirconUtil;
import com.mediacomm.aircon.util.mapper.AirconEntityMapper;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.VideoPanels;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ChannelIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.DecoderIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.ObjectId;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxRequestBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxesRequestBody;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.message.reqeust.body.SwapVwPanelLayerRequestBody;
import com.mediacomm.entity.message.reqeust.body.TxIdRequestBody;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.SnapshotType;
import com.mediacomm.util.JsonUtils;
import feign.FeignException;
import jakarta.annotation.Resource;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

/**
 * AirconCmdServer.
 */
@Slf4j
@Controller
public class AirconCmdServer extends CmdServer {

  @Resource
  private AirconFeignClientApi cli;
  @Resource
  private AirconEntityMapper airconEntityMapper;
  @Resource
  private AirconPreviewManager airconPreviewManager;
  private static final int PORT = 80;

  /**
   * refreshExtendDevice.
   */
  public String refreshExtendDevice(String msg) {
    MqRequest request = JsonUtils.decode(msg, MqRequest.class);
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    return Result.okStr();
  }

  /**
   * refreshConfig.
   */
  public String refreshConfig(String msg) {
    MqRequest request = JsonUtils.decode(msg, MqRequest.class);
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    refreshConfig(kvmMaster);
    return Result.okStr();
  }

  /**
   * refreshConfig.
   */
  private void refreshConfig(KvmMaster kvmMaster) {
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    List<AirconTx> txList = cli.getTxList(uri);
    for (AirconTx airconTx : txList) {
      if (airconTx.isLinkStatus()) {
        KvmAsset tx = airconEntityMapper.toKvmAsset(airconTx, kvmMaster.getMasterId());
        kvmAssetService.saveOrUpdateKvmAsset(tx, kvmMaster.getMasterId());
      }
    }
    List<AirconRx> rxList = cli.getRxList(uri);
    for (AirconRx airconRx : rxList) {
      if (airconRx.isLinkStatus()) {
        KvmAsset rx = airconEntityMapper.toKvmAsset(airconRx, kvmMaster.getMasterId());
        kvmAssetService.saveOrUpdateKvmAsset(rx, kvmMaster.getMasterId());
      }
    }
    List<AirconVideoWall> videoWalls = cli.getVideoWalls(uri);
    for (AirconVideoWall videoWall : videoWalls) {
      if (CollectionUtil.isNotEmpty(videoWall.getVwDecoders())) {
        KvmVideoWall wall = airconEntityMapper.toKvmVideoWall(videoWall, kvmMaster.getMasterId());
        videoWallService.saveOrUpdateByUniqueSearchKey(wall);
      }
    }
    List<AirconSeat> seatList = cli.getSeatList(uri);
    for (AirconSeat airconSeat : seatList) {
      KvmSeat seat = airconEntityMapper.toKvmSeat(airconSeat, kvmMaster.getMasterId());
      kvmSeatService.saveOrUpdate(seat, kvmMaster.getMasterId());
    }
    List<AirconServer> serverInfo = cli.getServerInfo(uri);
    for (AirconServer airconServer : serverInfo) {
      kvmMaster.setAlias(airconServer.getName());
      List<Property> properties = new ArrayList<>(airconServer.getProperties().size());
      for (AirconProperty property : airconServer.getProperties()) {
        properties.add(new Property(property.getKey(), property.getValue()));
      }
      kvmMaster.setProperties(properties);
      kvmMaster.getProperties().add(new Property("sn", airconServer.getSn()));
      kvmMaster.setVersion(airconServer.getSoftVersion());
      kvmMasterService.saveOrUpdate(kvmMaster);
    }
  }

  /**
   * getVwPanels.
   */
  public String getVwPanels(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    AirconVideoPanels videoWallPanels = cli.getVideoWallPanels(uri, airconVideoWallId);
    return Result.okStr(airconEntityMapper.toVideoPanels(videoWallPanels, masterId));
  }

  /**
   * openVwPanel.
   */
  public String openVwPanel(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    AirconPanelRect panelRect = airconEntityMapper
            .toAirconPanelRect(request.getBody().getPanelRect());
    panelRect.setPanelId(0); // 云控大屏窗口id会自增
    AirconPanelRect response;
    try {
      log.debug("Open vw panel, request {}", JsonUtils.encode(panelRect));
      response = cli.openVideoWallPanel(uri, airconVideoWallId, panelRect);
      log.debug("Open vw panel, response {}", JsonUtils.encode(panelRect));
    } catch (FeignException e) {
      return doFeignException(e);
    }
    return Result.okStr(airconEntityMapper.toPanelRect(response, masterId));
  }

  /**
   * closeVwPanel.
   */
  public String closeVwPanel(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    AirconPanelRect airconPanelRect = airconEntityMapper.toAirconPanelRect(request.getBody().getPanelRect());
    log.debug("Close vw panel, request {}", JsonUtils.encode(airconPanelRect));
    cli.closeVideoWallPanel(uri, airconVideoWallId, airconPanelRect);
    return Result.okStr();
  }

  /**
   * closeAllVwPanel.
   */
  public String closeAllVwPanel(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    cli.closeVideoWallAllPanel(uri, airconVideoWallId);
    return Result.okStr();
  }

  /**
   * openVwPanels.
   */
  @Override
  public String openVwPanels(String msg) {
    MqRequest<OpenVwPanelsRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    VideoPanels videoPanels = new VideoPanels();
    videoPanels.setLayoutData(request.getBody().getLayoutData());
    videoPanels.setPanels(request.getBody().getPanelData().getPanels());
    AirconVideoPanels airconVideoPanels = airconEntityMapper.toAirconVideoPanels(videoPanels);
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    AirconVideoPanels res;
    try {
      log.debug("Open vw panels, request param {}", JsonUtils.encode(airconVideoPanels));
      res = cli.openVideoWallPanels(uri, airconVideoWallId, airconVideoPanels);
      res.setLayoutData(airconVideoPanels.getLayoutData());
      log.debug("Open vw panels, response {}", JsonUtils.encode(res));
    } catch (FeignException e) {
      return doFeignException(e);
    }
    return Result.okStr(airconEntityMapper.toVideoPanels(res, masterId));
  }

  /**
   * moveVwPanels.
   */
  public String moveVwPanels(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    AirconPanelRect airconPanelRect =
            airconEntityMapper.toAirconPanelRect(request.getBody().getPanelRect());
    try {
      log.debug("Move vw panel, request {}", JsonUtils.encode(airconPanelRect));
      cli.moveVideoWallPanel(uri, airconVideoWallId, airconPanelRect);
    } catch (FeignException e) {
      return doFeignException(e);
    }
    return Result.okStr(request.getBody().getPanelRect());
  }

  /**
   * swapVwPanelLayer.
   */
  public String swapVwPanelLayer(String msg) {
    MqRequest<SwapVwPanelLayerRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    int airconVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (airconVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    Map<String, Integer> map = new HashMap<>(4);
    map.put("panelId1", request.getBody().getPanelId1());
    map.put("seq1", request.getBody().getSeq1());
    map.put("panelId2", request.getBody().getPanelId2());
    map.put("seq2", request.getBody().getSeq2());
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    log.debug("Swap vw panel layer, request {}", JsonUtils.encode(map));
    cli.swapVideoWallLayer(uri, airconVideoWallId, map);
    return Result.okStr();
  }

  /**
   * getSeatPanels.
   */
  public String getSeatPanels(String msg) {
    MqRequest<ChannelIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    try {
      AirconSeatPanels airconSeatPanels = cli.getSeatPanels(uri, request.getBody().getDecoderId(),
              request.getBody().getChannelId());
      log.debug("Get seat panels, response {}", JsonUtils.encode(airconSeatPanels));
      return Result.okStr(airconEntityMapper.toSeatPanels(airconSeatPanels, masterId));
    } catch (FeignException e) {
      return doFeignException(e);
    }
  }

  /**
   * closeAllSeatPanel.
   */
  public String closeAllSeatPanel(String msg) {
    MqRequest<ChannelIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    cli.closeSeatAllPanel(uri, request.getBody().getDecoderId(), request.getBody().getChannelId());
    return Result.okStr();
  }

  /**
   * 关闭坐席屏幕上的指定窗口.
   *
   * @param msg 包含请求参数的JSON消息，格式为MqRequest<DecoderIdRequestBody>
   * @return 操作结果字符串，成功返回OK，失败返回错误信息
   */
  public String closeSeatPanel(String msg) {
    MqRequest<DecoderIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    Map<String, Integer> map = new HashMap<>();
    map.put("panelId", request.getBody().getPanelId());
    log.debug("Close seat panel, request {}", JsonUtils.encode(map));
    cli.closeTx(uri, request.getBody().getDecoderId(), map);
    return Result.okStr();
  }

  /**
   * openSeatPanels.
   */
  public String openSeatPanels(String msg) {
    MqRequest<SeatOpenTxesRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    AirconSeatPanels airconSeatPanels = airconEntityMapper.toAirconSeatPanels(request.getBody());
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    log.debug("Open seat panels, request {}", JsonUtils.encode(airconSeatPanels));
    List<AirconPanelRect> res = cli.openSeatPanels(uri, request.getBody().getDecoderId(), request.getBody().getChannelId(),
            airconSeatPanels);
    log.debug("Open seat panels, response {}", JsonUtils.encode(res));
    return Result.okStr(airconEntityMapper.toSeatPanels(airconSeatPanels, masterId));
  }

  /**
   * seatOpenTx.
   */
  public String seatOpenTx(String msg) {
    MqRequest<SeatOpenTxRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    KvmAsset asset = kvmAssetService.getById(request.getBody().getVideoSrcId());
    Map<String, Integer> map = new HashMap<>();
    map.put("videoSrcId", asset.getDeviceId());
    map.put("ctrlMode", request.getBody().getCtrlMode());
    map.put("seq", request.getBody().getPanelId() + 1); // 云控机制 找不到布局:全屏;其他:指定布局位置
    try {
      log.debug("Open seat panel, request {}", JsonUtils.encode(map));
      cli.openTx(uri, request.getBody().getDecoderId(), request.getBody().getChannelId(), map);
    } catch (FeignException e) {
      return doFeignException(e);
    }
    return Result.okStr();
  }

  public String getTxSnapshot(String msg) {
    MqRequest<ObjectIds> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    List<SnapshotStatus> statuses = Lists.newArrayList();
    for (String activeId : request.getBody().getActiveIds()) {
      KvmAsset asset = kvmAssetService.getById(activeId);
      SnapshotStatus status = SnapshotStatus.builder()
              .id(activeId)
              .masterId(kvmMaster.getMasterId())
              .type(SnapshotType.SKYLINK)
              .build();
      if (asset != null
              && Objects.equals(asset.getDeviceModel(), DeviceType.AIRCON_ENCODER.getDeviceTypeId())) {
        status.setPath(String.format("/aircon/%s.jpg", asset.getHardcode()));
        status.setLinkStatus(true);
        airconPreviewManager.onDevicePreview(asset, kvmMaster.getDeviceIp());
      }
      statuses.add(status);
    }
    return Result.okStr(statuses);
  }

  public String getSnapshot(String msg) {
    MqRequest<TxIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    String txId = request.getBody().getTxId();
    KvmAsset tx = kvmAssetService.getById(txId);
    if (tx != null) {
      airconPreviewManager.onDevicePreview(tx, kvmMaster.getDeviceIp());
    }
    return Result.okStr();
  }

  public String connectToEncoder(String msg) {
    MqRequest<TxIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmAsset kvmAsset = kvmAssetService.getById(request.getBody().getTxId());
    if (kvmAsset == null) {
      return Result.failureStr(NO_VIDEO_SRC, ResponseCode.DEVICE_NOT_IN_HOST_13003);
    }
    Integer streamType = Property.findValueByKey(kvmAsset.getCollectorProperties(),
            PropertyKeyConst.STREAM_TYPE, 1, Integer.class);
    String rtspUrl = Property.findValueByKey(kvmAsset.getCollectorProperties(), "rtspUrl",
            String.format("rtsp://%s/stream/chn/%d", kvmAsset.getDeviceIp(), streamType));
    Map<String, Object> kvmControlEnable = Maps.newHashMap();
    kvmControlEnable.put("kvmControlEnable", true);
    kvmControlEnable.put("resolutionWidth", 1920);
    kvmControlEnable.put("resolutionHeight", 1080);
    kvmControlEnable.put("address", rtspUrl);
    return Result.okStr(kvmControlEnable);
  }

  private String doFeignException(FeignException e) {
    log.error("Cmd to aircon failed!", e);
    Optional<ByteBuffer> body = e.responseBody();
    if (body.isPresent()) {
      Charset utf8 = StandardCharsets.UTF_8;
      AirconErrorCode err = JsonUtils.decode(utf8.decode(body.get()).toString(),
              AirconErrorCode.class);
      return InspectAirconUtil.getResultFromApiStatus(err);
    } else {
      log.error("Can not decode aircon error response! {}", e.getMessage());
      return Result.failureStr(INTERFACE_NOT_RETURN, ResponseCode.EX_FAILURE_500);
    }
  }
}
