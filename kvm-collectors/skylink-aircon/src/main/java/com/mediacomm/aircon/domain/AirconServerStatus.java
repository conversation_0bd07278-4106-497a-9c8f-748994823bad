package com.mediacomm.aircon.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AirconServerStatus {
  private double cpuRate;
  private double memRate;
  private double diskRate;
  private int deviceId;
  private boolean linkStatus;
  private String loginUser;
  private int powerStatus;
  private String sn;
  private double temperature;
  private List<NetStatus> netStatus;

  @Data
  public static class NetStatus {
    private boolean linkStatus;
    private String netName;
    private int ratedSpeed;
    private double speed;
  }
}
