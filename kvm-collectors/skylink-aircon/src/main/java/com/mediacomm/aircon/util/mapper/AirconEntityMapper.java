package com.mediacomm.aircon.util.mapper;

import com.mediacomm.aircon.domain.AirconKvmDecoder;
import com.mediacomm.aircon.domain.AirconPanelRect;
import com.mediacomm.aircon.domain.AirconRx;
import com.mediacomm.aircon.domain.AirconSeat;
import com.mediacomm.aircon.domain.AirconSeatPanels;
import com.mediacomm.aircon.domain.AirconTx;
import com.mediacomm.aircon.domain.AirconVideoPanels;
import com.mediacomm.aircon.domain.AirconVideoWall;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.LayerData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.SeatPanels;
import com.mediacomm.entity.message.VideoPanels;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxesRequestBody;
import org.mapstruct.Builder;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;

/**
 * .
 */
@Mapper(componentModel = "spring", uses = AirconEntityMapperResolver.class,
        builder = @Builder(disableBuilder = true),
        mappingControl = DeepClone.class)
public interface AirconEntityMapper {

  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "ip", target = "deviceIp")
  @Mapping(source = "sn", target = "hardcode")
  @Mapping(source = "name", target = "alias")
  @Mapping(target = "version", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "properties", ignore = true)
  KvmAsset toKvmAsset(AirconTx airconTx, @Context String masterId);

  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "ip", target = "deviceIp")
  @Mapping(source = "sn", target = "hardcode")
  @Mapping(source = "name", target = "alias")
  @Mapping(target = "version", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "properties", ignore = true)
  KvmAsset toKvmAsset(AirconRx airconRx, @Context String masterId);

  @Mapping(source = "id", target = "deviceId")
  @Mapping(target = "decoders", ignore = true)
  @Mapping(target = "singleW", ignore = true)
  @Mapping(target = "singleH", ignore = true)
  KvmVideoWall toKvmVideoWall(AirconVideoWall airconVideoWall, @Context String masterId);

  @Mapping(source = "id", target = "deviceId")
  @Mapping(source = "row", target = "rowCount")
  @Mapping(source = "col", target = "colCount")
  @Mapping(source = "kvmDecoders", target = "decoders")
  KvmSeat toKvmSeat(AirconSeat airconSeat, @Context String masterId);

  @Mapping(source = "channel", target = "channelId")
  KvmSeatDecoder toKvmSeatDecoder(AirconKvmDecoder airconKvmDecoder, @Context String masterId);

  @Mapping(target = "videoSrcId", ignore = true)
  PanelRect toPanelRect(AirconPanelRect airconPanelRect, @Context String masterId);

  VideoPanels toVideoPanels(AirconVideoPanels airconVideoPanels, @Context String masterId);

  @Mapping(target = "videoSrcId", ignore = true)
  AirconPanelRect toAirconPanelRect(PanelRect panelRect);

  AirconVideoPanels toAirconVideoPanels(VideoPanels videoPanels);

  @Mapping(target = "videoSrcId", ignore = true)
  LayerData toLayerData(AirconPanelRect airconPanelRect, @Context String masterId);

  @Mapping(source = "layout", target = "layoutData")
  AirconSeatPanels toAirconSeatPanels(SeatOpenTxesRequestBody videoPanels);

  SeatPanels toSeatPanels(AirconSeatPanels airconSeatPanels, @Context String masterId);
}
