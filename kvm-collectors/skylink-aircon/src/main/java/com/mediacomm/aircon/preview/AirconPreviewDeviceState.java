package com.mediacomm.aircon.preview;

import lombok.Getter;
import lombok.Setter;
import java.util.concurrent.Future;

/**
 * .
 */
@Getter
public class AirconPreviewDeviceState {
  private final String assetId;
  private final int deviceId;
  private final String hardCode;
  private final String url;
  private volatile long lastUpdateTime;
  @Setter
  private Future<?> taskFuture;

  public AirconPreviewDeviceState(String assetId, int deviceId, String url, String hardCode) {
    this.assetId = assetId;
    this.deviceId = deviceId;
    this.url = url;
    this.hardCode = hardCode;
    this.lastUpdateTime = System.currentTimeMillis();
  }

  public void updateLastPreviewTime() {
    this.lastUpdateTime = System.currentTimeMillis();
  }
}
