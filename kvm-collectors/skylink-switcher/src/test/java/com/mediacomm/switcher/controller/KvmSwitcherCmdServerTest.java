package com.mediacomm.switcher.controller;

import static net.datafaker.transformations.Field.field;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.mediacomm.config.rabbitmqtt.IMqttSender;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.SwitcherDevice;
import com.mediacomm.entity.message.reqeust.body.IdRequestBody;
import com.mediacomm.entity.message.reqeust.body.PositionRequestBody;
import com.mediacomm.entity.message.reqeust.body.RegisterRequestBody;
import com.mediacomm.entity.message.reqeust.body.SwitcherDevicesBody;
import com.mediacomm.entity.vo.EnvDeviceVo;
import com.mediacomm.switcher.domain.SwitcherResponse;
import com.mediacomm.switcher.domain.SwitcherUdpBody;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.SkyLinkStringUtil;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.datafaker.Faker;
import net.datafaker.transformations.JavaObjectTransformer;
import net.datafaker.transformations.Schema;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * KvmSwitcherCmdServerTest.
 */
@RunWith(MockitoJUnitRunner.class)
public class KvmSwitcherCmdServerTest {
  @Spy
  @InjectMocks
  private KvmSwitcherCmdServer kvmSwitcherCmdServer;
  @Mock
  private EnvDeviceService envDeviceServiceMock;
  @Mock
  private IMqttSender iMqttSender;

  private List<SwitcherDevice> devices;
  private final Faker faker = new Faker();

  @Before
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    JavaObjectTransformer jTransformer = new JavaObjectTransformer();
    Schema<Object, ?> schema = Schema.of(
        field("mac", () -> faker.internet().macAddress()),
        field("sn", () -> faker.idNumber().valid()));
    devices = new ArrayList<>();
    for (int i = 0; i < 100; i++) {
      SwitcherDevice device = (SwitcherDevice) jTransformer.apply(SwitcherDevice.class, schema);
      devices.add(device);
    }
  }

  @Test
  public void testScanDevicesWhenRequestIsNull() {
    String response = kvmSwitcherCmdServer.scanDevices(null);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull("Response should not be null", result);
    assertEquals("Expected response code to be EX_FAILURE_400", ResponseCode.EX_FAILURE_400,
        result.getCode());
  }

  @Test
  public void testScanDevicesWhenRequestBodyIsNull() {
    String msg = "{\"body\":null}";
    String response = kvmSwitcherCmdServer.scanDevices(msg);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull("Response should not be null", result);
    assertEquals("Expected response code to be EX_OK_200", ResponseCode.EX_OK_200,
        result.getCode());
  }

  @Test
  public void testScanDevicesHappyPath() {
    String msg = "{\"requestBody\":{\"udpBroadcastIp\":\"*************\",\"UDP_PORT\":7788}}";
    doNothing().when(kvmSwitcherCmdServer).udpBroadcaster(anyString(), any(), anyInt());
    String response = kvmSwitcherCmdServer.scanDevices(msg);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull("Response should not be null", result);
    assertEquals("Expected response code to be EX_OK_200", ResponseCode.EX_OK_200,
        result.getCode());
  }


  @Test
  public void testSetIp_Success() {
    SwitcherDevicesBody body = new SwitcherDevicesBody();
    List<SwitcherDevice> devices = new ArrayList<>();
    for (int i = 0; i < 100; i++) {
      SwitcherDevice switcherDevice = getSwitcherDevice();
      devices.add(switcherDevice);
    }
    body.setDevices(devices);

    MqRequest<SwitcherDevicesBody> request = new MqRequest<>();
    request.setBody(body);
    String msg = JsonUtils.encode(request);
    when(envDeviceServiceMock.oneBySnAndMac(anyString(), anyString())).thenReturn(getEnvDeviceVo());
    //doNothing().when(kvmSwitcherCmdServer).udpBroadcaster(anyString(), any(), anyInt());

    String response = kvmSwitcherCmdServer.setIp(msg);
    Result<?> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull("Response should not be null", result);
    assertEquals("Expected response code to be EX_OK_200", ResponseCode.EX_OK_200,
        result.getCode());
  }

  private SwitcherDevice getSwitcherDevice() {
    SwitcherDevice switcherDevice = new SwitcherDevice();
    switcherDevice.setSn(faker.idNumber().valid());
    switcherDevice.setMac(faker.internet().macAddress());
    switcherDevice.setIp(faker.internet().ipV4Address());
    switcherDevice.setMask(faker.internet().ipV4Address());
    switcherDevice.setGateway(faker.internet().ipV4Address());
    return switcherDevice;
  }

  private EnvDeviceVo getEnvDeviceVo() {
    EnvDeviceVo envDeviceVo = new EnvDeviceVo();
    envDeviceVo.setDeviceIp(faker.internet().ipV4Address());
    envDeviceVo.setHardcode("dd");
    return envDeviceVo;
  }

  @Test
  public void testUnregisterSuccess() {
    SwitcherDevicesBody body = new SwitcherDevicesBody();
    body.setDevices(devices);
    MqRequest<SwitcherDevicesBody> request = new MqRequest<>();
    request.setMasterId("masterId");
    request.setBody(body);
    String msg = JsonUtils.encode(request);
    when(envDeviceServiceMock.oneBySnAndMac(anyString(), anyString())).thenReturn(
        new EnvDeviceVo());
    kvmSwitcherCmdServer.unregister(msg);
    verify(iMqttSender, times(100)).sendToMqtt(anyString(), anyString());
  }

  @Test
  public void testUnregisterWithNullMessage() {
    kvmSwitcherCmdServer.unregister(null);

    verify(iMqttSender, times(0)).sendToMqtt(anyString(), anyString());
  }

  @Test
  public void testUnregisterWithEmptyDevices() {
    SwitcherDevicesBody body = new SwitcherDevicesBody();
    body.setDevices(Collections.emptyList());
    MqRequest<SwitcherDevicesBody> request = new MqRequest<>();
    request.setMasterId("masterId");
    request.setBody(body);
    String msg = JsonUtils.encode(request);
    kvmSwitcherCmdServer.unregister(msg);
    verify(iMqttSender, times(0)).sendToMqtt(anyString(), anyString());
  }

  @Test
  public void testSetPositionSuccess() {
    PositionRequestBody positionRequestBody = new PositionRequestBody();
    String uuid = SkyLinkStringUtil.uuid();
    positionRequestBody.setId(uuid);
    String group = faker.text().text();
    positionRequestBody.setGroup(group);
    String room = faker.text().text();
    positionRequestBody.setRoom(room);

    MqRequest<PositionRequestBody> mqRequest = new MqRequest<>();
    mqRequest.setMasterId("masterId");
    mqRequest.setBody(positionRequestBody);

    String json = JsonUtils.encode(mqRequest);

    EnvDevice envDevice = new EnvDevice();
    envDevice.setId(uuid);
    List<Property> collectorProperties = new ArrayList<>();
    collectorProperties.add(new Property("group", faker.text().text()));
    collectorProperties.add(new Property("room", faker.text().text()));
    envDevice.setCollectorProperties(collectorProperties);

    when(envDeviceServiceMock.getById(anyString())).thenReturn(envDevice);
    when(envDeviceServiceMock.saveOrUpdate(any())).thenReturn(true);

    String response = kvmSwitcherCmdServer.setPosition(json);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    ArgumentCaptor<EnvDevice> argumentCaptor = ArgumentCaptor.forClass(EnvDevice.class);
    verify(envDeviceServiceMock).saveOrUpdate(argumentCaptor.capture());
    EnvDevice argumentValue = argumentCaptor.getValue();
    assertNotNull(argumentValue);
    assertEquals(group, Property.findValueByKey(argumentValue.getCollectorProperties(), "group",
        null));
    assertEquals(room, Property.findValueByKey(argumentValue.getCollectorProperties(), "room",
        null));
    assertNotNull(result);
    assertEquals(ResponseCode.EX_OK_200, result.getCode());
  }

  @Test
  public void testSetPositionWithInvalidRequest() {
    String invalidJson = "{}";
    String response = kvmSwitcherCmdServer.setPosition(invalidJson);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull(result);
    assertEquals(ResponseCode.EX_FAILURE_400, result.getCode());
  }

  @Test
  public void testSetPositionWithNonexistentDevice() {
    PositionRequestBody positionRequestBody = new PositionRequestBody();
    positionRequestBody.setId("nonexistent");
    MqRequest<PositionRequestBody> mqRequest = new MqRequest<>();
    mqRequest.setMasterId("masterId");
    mqRequest.setBody(positionRequestBody);
    String json = JsonUtils.encode(mqRequest);
    when(envDeviceServiceMock.getById("nonexistent")).thenReturn(null);
    String response = kvmSwitcherCmdServer.setPosition(json);
    Result<String> result = JsonUtils.decode(response, new TypeReference<>() {
    });
    assertNotNull(result);
    assertEquals(ResponseCode.EX_FAILURE_400, result.getCode());
  }

  @Test
  public void testRegisterSuccess() throws NoSuchFieldException, IllegalAccessException {
    JavaObjectTransformer jTransformer = new JavaObjectTransformer();
    Schema<Object, ?> schema = Schema.of(
        field("mac", () -> faker.internet().macAddress()),
        field("sn", () -> faker.idNumber().valid()), field("ret", () -> 0));
    List<SwitcherDevice> devices = new ArrayList<>();
    Table<String, String, SwitcherUdpBody<SwitcherDevice>> allSwitcherDeviceTable =
        HashBasedTable.create();
    for (int i = 0; i < 100; i++) {
      SwitcherDevice device = (SwitcherDevice) jTransformer.apply(SwitcherDevice.class, schema);
      devices.add(device);
      SwitcherUdpBody<SwitcherDevice> dev = new SwitcherUdpBody<>();
      dev.setBody(device);
      allSwitcherDeviceTable.put(device.getSn(), device.getMac(), dev);
    }
    RegisterRequestBody registerRequestBody = new RegisterRequestBody();
    registerRequestBody.setServerIp("127.0.0.1");
    registerRequestBody.setDevices(devices);
    MqRequest<RegisterRequestBody> request = new MqRequest<>();
    request.setBody(registerRequestBody);
    String msg = JsonUtils.encode(request);
    Field field = KvmSwitcherCmdServer.class.getDeclaredField("allSwitcherDeviceTable");
    field.setAccessible(true);
    field.set(kvmSwitcherCmdServer, allSwitcherDeviceTable);
    //when(envDeviceServiceMock.save(any())).thenReturn(true);
    doNothing().when(kvmSwitcherCmdServer).registerDevices(any(), anyString());
    String result = kvmSwitcherCmdServer.register(msg);
    SwitcherResponse<List<SwitcherDevice>> res = JsonUtils.decode(result,
        new TypeReference<>() {
        });
    assertNotNull(res);
    for (SwitcherDevice switcherDevice : res.getResult()) {
      assertEquals(switcherDevice.getRet(), 0);
    }
  }

  @Test
  public void getPosition_ValidRequest_ReturnsPosition() {
    String uuid = SkyLinkStringUtil.uuid();
    IdRequestBody idRequestBody = new IdRequestBody();
    idRequestBody.setId(uuid);
    MqRequest<IdRequestBody> mqRequest = new MqRequest<>();
    mqRequest.setMasterId(faker.idNumber().valid());
    mqRequest.setBody(idRequestBody);
    String msg = JsonUtils.encode(mqRequest);
    EnvDevice envDevice = new EnvDevice();
    envDevice.setId(uuid);
    envDevice.setCollectorProperties(createProperties("group", "groupValue", "room", "roomValue"));
    when(envDeviceServiceMock.getById(uuid)).thenReturn(envDevice);
    String result = kvmSwitcherCmdServer.getPosition(msg);
    Result<PositionRequestBody> resultObj = JsonUtils.decode(result, new TypeReference<>() {
    });
    assertEquals(200, resultObj.getCode().intValue());
    assertEquals(uuid, resultObj.getResult().getId());
    assertEquals("groupValue", resultObj.getResult().getGroup());
    assertEquals("roomValue", resultObj.getResult().getRoom());
  }

  @Test
  public void getPosition_InvalidRequest_ReturnsFailure() {
    String msg = "{\"masterId\":\"1\",\"body\":{\"id\":\"invalidId\"}}";
    when(envDeviceServiceMock.getById("invalidId")).thenReturn(null);
    String result = kvmSwitcherCmdServer.getPosition(msg);
    Result<Object> resultObj = JsonUtils.decode(result, new TypeReference<>() {
    });
    assertEquals(400, resultObj.getCode().intValue());
    assertEquals("设备不存在", resultObj.getMessage());
  }

  @Test
  public void getPosition_NullRequestBody_ReturnsFailure() {
    String result = kvmSwitcherCmdServer.getPosition(null);
    Result<Object> resultObj = JsonUtils.decode(result, new TypeReference<>() {
    });
    assertEquals(400, resultObj.getCode().intValue());
  }

  private List<Property> createProperties(String... keyValuePairs) {
    List<Property> properties = new ArrayList<>();
    for (int i = 0; i < keyValuePairs.length; i += 2) {
      Property property = new Property();
      property.setPropertyKey(keyValuePairs[i]);
      property.setPropertyValue(keyValuePairs[i + 1]);
      properties.add(property);
    }
    return properties;
  }
}
