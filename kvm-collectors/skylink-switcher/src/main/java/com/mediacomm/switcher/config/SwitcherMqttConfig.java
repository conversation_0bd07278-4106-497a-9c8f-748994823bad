package com.mediacomm.switcher.config;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;

/**
 * SwitcherMqttConfig.
 */
@Configuration
public class SwitcherMqttConfig {

  @Value("${spring.mqtt.client.id}")
  private String clientId;

  @Value("${spring.mqtt.default.topic}")
  private String defaultTopic;

  @Value("${spring.mqtt.Qos}")
  private int defaultQos;

  @Value("${spring.mqtt.completionTimeout}")
  private int completionTimeout;

  @Value("${spring.mqtt.url}")
  private String hostUrl;

  @Value("${spring.mqtt.userName}")
  private String userName;

  @Value("${spring.mqtt.password}")
  private String password;

  @Resource
  private MqttPahoClientFactory mqttClientFactory;

  // 配置消息适配器，配置订阅客户端
  @Bean
  public MqttPahoMessageDrivenChannelAdapter switchInbound() {
    List<String> topicList = Arrays.asList(defaultTopic.trim().split(","));
    String[] topics = new String[topicList.size()];
    topicList.toArray(topics);
    MqttPahoMessageDrivenChannelAdapter
        adapter =
        new MqttPahoMessageDrivenChannelAdapter(getClientId("inbound"), mqttClientFactory,
            topics);
    adapter.setCompletionTimeout(completionTimeout);
    // 设置转换器，接收bytes
    DefaultPahoMessageConverter converter = new DefaultPahoMessageConverter();
    converter.setPayloadAsBytes(true);
    adapter.setConverter(converter);
    adapter.setQos(defaultQos);
    adapter.setOutputChannelName("mqttInputChannel");
    return adapter;
  }

//  @Bean
//  public MqttPahoClientFactory mqttClientFactory() {
//    MqttConnectOptions mqttConfig = new MqttConnectOptions();
//    mqttConfig.setServerURIs(new String[] {hostUrl});
//    mqttConfig.setUserName(userName);
//    mqttConfig.setPassword(password.toCharArray());
//    mqttConfig.setKeepAliveInterval(2);
//    mqttConfig.setAutomaticReconnect(true);
//    DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
//    factory.setConnectionOptions(mqttConfig);
//    return factory;
//  }


  private String getClientId(String method) {
    return this.clientId + "-" + method + "-" + System.currentTimeMillis();
  }
}
