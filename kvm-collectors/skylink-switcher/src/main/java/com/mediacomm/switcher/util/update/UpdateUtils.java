package com.mediacomm.switcher.util.update;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;

/**
 * UpdateUtils.
 */
@Slf4j
@NoArgsConstructor
public class UpdateUtils {
  
  // 版本信息键名常量
  public static final String PRODUCT = "product";
  public static final String VERSION_HEADER_SYS = "sys";
  public static final String VERSION_HEADER_APP = "app";
  public static final String FPGA_HDMI_TX = "fpga_hdmi_tx";
  public static final String FPGA_HDMI_RX = "fpga_hdmi_rx";
  // 文件和格式常量
  private static final String VERSION_FILE_NAME = "version.txt";
  private static final String KEY_VALUE_SEPARATOR = "=";

  /**
   * 获取更新包信息.
   */
  public static SwitcherVersion getUpdatePkgInfo(String filePath) throws IOException {
    log.info("Reading update package information from the file path: {}", filePath);
    try (InputStream fileInputStream = Files.newInputStream(Paths.get(filePath))) {
      return getUpdatePkgInfo(fileInputStream);
    } catch (IOException e) {
      log.error("Failed to read the update package file: {}", filePath, e);
      throw e;
    }
  }

  /**
   * 获取更新包信息.
   */
  public static SwitcherVersion getUpdatePkgInfo(InputStream fileInputStream) throws IOException {
    log.info("Reading update package information from input stream");
    try (CpioArchiveInputStream cpioIn = new CpioArchiveInputStream(fileInputStream)) {
      CpioArchiveEntry entry;
      while ((entry = cpioIn.getNextEntry()) != null) {
        if (VERSION_FILE_NAME.equals(entry.getName())) {
          log.info("Found version file: {}", VERSION_FILE_NAME);
          return parseVersionFile(cpioIn);
        }
      }
      log.warn("Version file not found in the update package");
    }
    return null;
  }

  /**
   * 解析版本文件内容.
   */
  private static SwitcherVersion parseVersionFile(InputStream inputStream) throws IOException {
    SwitcherVersion version = new SwitcherVersion();
    try (InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
         BufferedReader buff = new BufferedReader(reader)) {
      
      Map<String, String> versionInfoMap = readVersionInfoMap(buff);
      applyVersionInfo(version, versionInfoMap);
      
      return version;
    }
  }

  /**
   * 读取版本信息映射.
   */
  private static Map<String, String> readVersionInfoMap(BufferedReader reader) throws IOException {
    Map<String, String> versionInfoMap = new HashMap<>();
    String line;
    while ((line = reader.readLine()) != null) {
      if (!line.contains(KEY_VALUE_SEPARATOR)) {
        continue;
      }

      String[] splitResult = line.split(KEY_VALUE_SEPARATOR);
      if (splitResult.length != 2) {
        continue;
      }
      
      String key = splitResult[0].trim().toLowerCase(Locale.ENGLISH);
      String value = splitResult[1].trim().toLowerCase(Locale.ENGLISH);
      versionInfoMap.put(key, value);
      log.debug("Read version info item: {} = {}", key, value);
    }
    return versionInfoMap;
  }

  /**
   * 应用版本信息到版本对象.
   */
  private static void applyVersionInfo(SwitcherVersion version,
                                       Map<String, String> versionInfoMap) {
    versionInfoMap.forEach((key, value) -> {
      switch (key) {
        case PRODUCT -> version.setProduct(value);
        case VERSION_HEADER_SYS -> version.setSys(value);
        case VERSION_HEADER_APP -> version.setApp(value);
        case FPGA_HDMI_TX -> version.setFpgaHdmiTx(value);
        case FPGA_HDMI_RX -> version.setFpgaHdmiRx(value);
        default -> {
          // 忽略其他版本信息项
        }
      }
    });
  }
}
