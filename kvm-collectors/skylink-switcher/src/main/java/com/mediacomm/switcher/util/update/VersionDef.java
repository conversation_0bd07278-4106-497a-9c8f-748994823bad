package com.mediacomm.switcher.util.update;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * VersionDef.
 */
@Data
@EqualsAndHashCode
public class VersionDef {
  private int masterVersion;
  private int subVersion;
  private int year;
  private int month;
  private int day;

  @Override
  public String toString() {
    return String.format("%d.%d.%04d.%02d.%02d", masterVersion, subVersion, year, month, day);
  }
}
