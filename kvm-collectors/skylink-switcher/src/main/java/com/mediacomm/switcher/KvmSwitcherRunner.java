package com.mediacomm.switcher;

import com.mediacomm.entity.Result;
import com.mediacomm.switcher.controller.KvmSwitcherCmdServer;
import com.mediacomm.system.base.kvm.KvmRunner;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * KvmSwitcherRunner.
 */
@Slf4j
@Component
public class KvmSwitcherRunner extends KvmRunner implements CommandLineRunner {
  @Resource
  private KvmSwitcherCmdServer cmdServer;

  @Override
  public void run(String... args) {
    log.info("KvmSwitcher service running!");
  }

  /**
   * 指定消费者的线程数量,一个线程会打开一个Channel，
   * 一个队列上的消息只会被消费一次（不考虑消息重新入队列的情况）,下面的表示至少开启5个线程，最多10个。
   * 线程的数目需要根据你的任务来决定，如果是计算密集型，线程的数目就应该少一些.
   *
   * @param msg        负载信息
   * @param headers    头部信息
   * @param routingKey 路由信息
   */
  @RabbitListener(queues = MessageType.KVM_SWITCHER, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    return switch (routingKey) {
      case RoutingKey.KVM_SWITCHER_SCAN -> cmdServer.scanDevices(msg);
      case RoutingKey.KVM_SWITCHER_SET_IP -> cmdServer.setIp(msg);
      case RoutingKey.KVM_SWITCHER_REGISTER -> cmdServer.register(msg);
      case RoutingKey.KVM_SWITCHER_UNREGISTER -> cmdServer.unregister(msg);
      case RoutingKey.KVM_SWITCHER_CHECK_UPGRADE_PACKAGE -> cmdServer.checkUpgradePackage(msg);
      default -> Result.failureStr("KvmSwitcher no such command", ResponseCode.EX_NOTFOUND_404);
    };
  }
}
