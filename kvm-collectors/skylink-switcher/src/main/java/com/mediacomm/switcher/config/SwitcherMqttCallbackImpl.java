package com.mediacomm.switcher.config;

import com.mediacomm.config.rabbitmqtt.MqttMessageService;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;

/**
 * .
 */
@Configuration
public class SwitcherMqttCallbackImpl extends MqttMessageService {
  @Resource
  private MqttPahoMessageDrivenChannelAdapter switchInbound;


  /**
   * 订阅主题.
   */
  public void subscribe(String... topic) {
    this.switchInbound.addTopic(topic);
  }

  /**
   * 取消订阅主题.
   */
  public void unsubscribe(String... topic) {
    this.switchInbound.removeTopic(topic);
  }

  /**
   * 获取订阅主题.
   */
  public Collection<String> getTopics() {
    return Arrays.stream(switchInbound.getTopic()).toList();
  }
}
