package com.mediacomm.switcher.domain.body;

import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
public class SwitcherStatusBody {
  private String name;
  private String ip;
  private String mask;
  private String gateway;
  private String version;
  private double temperature;
  private double cpu_usage;
  private double memory_usage;
  private List<Integer> lan; // 各个网口的状态，0表示正常，1表示未接入
  private List<Integer> ac_power; // 各个交流电源的状态，0表示正常，1表示未供电，2表示异常
  private List<Integer> dc_power; // 各个直流电源的状态，0表示正常，1表示未供电，2表示异常
  private List<Double> fan; // 各个风扇的转速，单位为rpm
  private List<Integer> link; // 各个光纤链路的状态，0表示正常，1表示未链接
  private List<Integer> dp; // 各个DP口的状态，0表示正常，1表示未接入
  private List<Integer> usb; // 各个usb口的状态，0表示正常，1表示未接入
  private List<String> resolution; // 各个DP的分辨率
  private int upgrade_state; // 升级状态，0表示不在升级，1表示升级中，2表示升级失败
  private int upgrade_progress; // 升级进度百分比，当upgrade_state为1时有效
}
