package com.mediacomm.switcher.util.update;

import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.message.reqeust.SwitcherDevice;
import com.mediacomm.system.variable.sysenum.DeviceType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class InspectSwitcherUtil {

  /**
   * 根据 switcherDeviceType 设置 deviceModel.
   * @param device 云视中的切换器设备.
   * @param switcherDevice 未被转换的切换器信息.
   * @return 存在定义的 deviceModel时返回true.
   */
  public static boolean setDeviceModelFromSwitcherDeviceType(EnvDevice device, SwitcherDevice switcherDevice) {
    switch (switcherDevice.getDeviceType()) {
      case "switcher_tx" -> {
        device.setDeviceModel(DeviceType.SWITCHER_TX.getDeviceTypeId());
        return true;
      }
      case "switcher_rx" -> {
        device.setDeviceModel(DeviceType.SWITCHER_RX.getDeviceTypeId());
        return true;
      }
      default -> {
        log.error("unknown switcher device type: {}", switcherDevice);
        return false;
      }
    }
  }
}
