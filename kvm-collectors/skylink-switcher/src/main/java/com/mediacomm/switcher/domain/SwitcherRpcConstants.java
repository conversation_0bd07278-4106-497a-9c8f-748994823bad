package com.mediacomm.switcher.domain;

/**
 * SwitcherRpcConstants.
 */
public class SwitcherRpcConstants {
  public static final String SERVER = "server";
  public static final String RPC_UNREGISTER = "unregister";
  public static final String UPGRADE = "upgrade";
  /**
   * 获取外设状态.
   */
  public static final String GET_STATUS = "get_status";
  /**
   * 主动上次的外设状态.
   */
  public static final String STATUS = "status";
  public static final String GET_REGISTER_STATUS = "get_register_status";

  public static String extRpcRequest(String id, String method) {
    return String.format("rpc/req/switcher/%s/%s", id, method);
  }

  /**
   * .
   */
  public static String extServerRequest(String method) {
    return String.format("rpc/req/switcher/server/%s", method);
  }

  /**
   * 切换器远程回复主题.
   */
  public static String extRpcResponse(String id, String method) {
    return String.format("rpc/resp/switcher/%s/%s", id, method);
  }

  /**
   * 切换器通知.
   */
  public static String extNotify(String method) {
    return String.format("/notify/switcher/%s", method);
  }
}
