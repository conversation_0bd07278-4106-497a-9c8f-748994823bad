package com.mediacomm.gbgateway.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * GB Gateway设备列表查询请求实体类.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DeviceListRequest {
  /**
   * 平台ID.
   */
  private String platformId;

  /**
   * 页码，从1开始.
   */
  @Builder.Default
  private Integer page = 1;

  /**
   * 每页大小.
   */
  @Builder.Default
  private Integer pageSize = 100;
}
