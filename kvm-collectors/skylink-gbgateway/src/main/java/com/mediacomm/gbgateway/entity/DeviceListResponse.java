package com.mediacomm.gbgateway.entity;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * GB Gateway设备列表响应实体类.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class DeviceListResponse {
  /**
   * 响应状态码.
   */
  private Integer code;

  /**
   * 响应消息.
   */
  private String message;

  /**
   * 响应数据.
   */
  private DeviceListData data;

  /**
   * 设备列表数据.
   */
  @NoArgsConstructor
  @Data
  public static class DeviceListData {
    /**
     * 设备列表.
     */
    private List<GbGatewayDevice> devices;

    /**
     * 总数量.
     */
    private Integer total;

    /**
     * 当前页码.
     */
    private Integer page;

    /**
     * 每页大小.
     */
    private Integer pageSize;

    /**
     * 总页数.
     */
    private Integer totalPages;
  }
}
