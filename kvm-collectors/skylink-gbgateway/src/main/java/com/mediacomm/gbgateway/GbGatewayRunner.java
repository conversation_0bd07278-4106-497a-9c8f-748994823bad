package com.mediacomm.gbgateway;

import com.mediacomm.gbgateway.controller.GbGatewayCmdServer;
import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.RoutingKey;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.CommandLineRunner;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * GB Gateway运行器.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GbGatewayRunner implements CommandLineRunner {

  @Resource
  private GbGatewayCmdServer gbGatewayCmdServer;

  /**
   * 初始化.
   */
  @Override
  public void run(String... args) {
    log.info("GB Gateway Runner service running!");
  }

  /**
   * 定时刷新设备列表.
   * 每60秒执行一次设备列表刷新
   */
  @Scheduled(fixedRate = 60000)
  public void execute() {
    try {
      gbGatewayCmdServer.refreshDeviceList();
    } catch (Exception e) {
      log.error("Error during scheduled device list refresh", e);
    }
  }

  /**
   * 消息队列监听器.
   * 监听专门的GB Gateway消息队列
   *
   * @param msg        负载信息
   * @param headers    头部信息
   * @param routingKey 路由信息
   * @return 处理结果
   */
  @RabbitListener(queues = MessageType.GB_GATEWAY_SERVER, concurrency = "5-10")
  public String receiver(@Payload String msg,
                         @Headers Map<String, Object> headers,
                         @Header(value = AmqpHeaders.RECEIVED_ROUTING_KEY) String routingKey) {
    log.debug("GB Gateway receiver msg: {}", msg);
    log.debug("GB Gateway receiver routingKey: {}", routingKey);
    log.debug("GB Gateway receiver headers: {}", headers);

    return switch (routingKey) {
      case RoutingKey.GB_GATEWAY_SERVER_REFRESH_CONFIG -> gbGatewayCmdServer.refreshConfig(msg);
      default -> {
        log.warn("Unknown routing key for GB Gateway: {}", routingKey);
        yield "Unknown routing key";
      }
    };
  }
}
