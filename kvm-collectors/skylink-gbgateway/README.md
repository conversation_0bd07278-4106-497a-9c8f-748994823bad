# GB Gateway 采集模块

## 概述

本模块用于采集GB Gateway国标网关程序的下级资源（IPC摄像头设备），并将设备信息保存到数据库中的KvmAsset表中。

## 功能特性

- 自动发现和采集GB Gateway下的摄像头设备
- 支持分页查询大量设备
- 自动同步设备状态和属性信息
- 定时刷新设备列表（每60秒）
- 支持通过消息队列手动触发刷新

## 模块结构

```
skylink-gbgateway/
├── src/main/java/com/mediacomm/gbgateway/
│   ├── api/                    # API接口类
│   │   └── GbGatewayDeviceApi.java
│   ├── controller/             # 控制器
│   │   └── GbGatewayCmdServer.java
│   ├── entity/                 # 实体类
│   │   ├── GbGatewayDevice.java
│   │   ├── DeviceListRequest.java
│   │   └── DeviceListResponse.java
│   ├── util/mapper/            # 映射器
│   │   ├── GbGatewayEntityMapper.java
│   │   └── GbGatewayEntityMapperResolver.java
│   └── GbGatewayRunner.java    # 运行器
├── pom.xml
└── README.md
```

## 配置说明

### 主机配置

要使用GB Gateway采集功能，需要在KvmMaster（主机）的采集器属性中配置以下参数：

1. **gatewayType**: 设置为 "gb-gateway" 以标识这是一个GB Gateway主机
2. **protocol**: GB Gateway服务协议，默认为 "http"
3. **port**: GB Gateway服务端口，默认为 "8080"
4. **platformId**: GB Gateway平台ID，用于查询设备列表

### 配置示例

```json
{
  "collectorProperties": [
    {
      "key": "gatewayType",
      "value": "gb-gateway"
    },
    {
      "key": "protocol", 
      "value": "http"
    },
    {
      "key": "port",
      "value": "8080"
    },
    {
      "key": "platformId",
      "value": "your-platform-id"
    }
  ]
}
```

## API接口

### 获取设备列表

**接口地址**: `GET /api/v1/devices`

**请求参数**:
- `platform_id`: 平台ID（必填）
- `page`: 页码，从1开始（可选，默认1）
- `page_size`: 每页大小（可选，默认100）

### 健康检查

**接口地址**: `GET /api/v1/health`

用于测试GB Gateway服务是否可用。

## 设备映射

GB Gateway设备信息会映射到KvmAsset表中，映射关系如下：

| GB Gateway字段 | KvmAsset字段 | 说明 |
|---------------|-------------|------|
| deviceId | deviceId | 设备ID |
| name | name/alias | 设备名称/别名 |
| ip | deviceIp | 设备IP地址 |
| deviceId | hardcode | 硬件编码（格式：masterId.gbgateway.deviceId） |
| - | deviceModel | 设备类型（使用HIKVISION_SECURE_IPC） |

设备的详细属性（如厂商、型号、版本等）会保存在properties字段中。

## 运行机制

1. **定时任务**: 每60秒自动执行一次设备列表刷新
2. **消息队列**: 监听HIKVISION_SERVER队列，支持手动触发刷新
3. **设备同步**: 自动添加新设备，删除不存在的设备
4. **错误处理**: 连接失败时记录错误日志，不影响其他主机的采集

## 使用方法

1. 在系统中添加一个KvmMaster主机
2. 设置主机的设备类型为"海康安防主机"
3. 在主机的采集器属性中配置GB Gateway相关参数
4. 系统会自动开始采集该GB Gateway下的设备信息

## 注意事项

1. 确保GB Gateway服务正常运行且网络可达
2. 平台ID必须正确配置，否则无法获取设备列表
3. 目前使用海康IPC设备类型，后续可根据需要添加专门的GB Gateway设备类型
4. 设备硬件编码格式为：`masterId.gbgateway.deviceId`，确保唯一性

## 扩展说明

如需扩展功能，可以：

1. 在DeviceType枚举中添加专门的GB Gateway设备类型
2. 扩展设备属性映射，支持更多GB Gateway特有字段
3. 添加设备控制功能（如云台控制、视频流请求等）
4. 创建专门的消息队列和路由键
