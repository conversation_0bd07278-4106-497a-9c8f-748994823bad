package com.mediacomm.util.mapper;

import com.mediacomm.domain.h.KaitoHRequestBody;
import com.mediacomm.domain.request.KaitoRequestBody;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.variable.PropertyKeyConst;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * .
 */
public class KaitoInspectUtil {
  public static Map<String, Integer> getScreenOffsetValue(KvmVideoWall videoWall) {
    List<Property> list = videoWall.getProperties();
    Map<String, Integer> map = new HashMap<>();
    map.put(PropertyKeyConst.POS_X, Integer.parseInt(
            Property.findValueByKey(list, PropertyKeyConst.POS_X, "0")));
    map.put(PropertyKeyConst.POS_Y, Integer.parseInt(
            Property.findValueByKey(list, PropertyKeyConst.POS_Y, "0")));
    return map;
  }

  public static <P> KaitoRequestBody<P> buildKaitoReqBody(P param, KvmMaster master) {
    String pid = Property.findValueByKey(master.getCollectorProperties(), "projectId", "");
    String secretKey = Property.findValueByKey(master.getCollectorProperties(), "secretKey", "");
    KaitoRequestBody<P> body = new KaitoRequestBody<>();
    body.setBody(param);
    body.setPId(pid);
    body.setSign(secretKey);
    return body;
  }

  public static <P> KaitoHRequestBody<P> buildKaitoHReqBody(P param, KvmMaster master) {
    String pid = Property.findValueByKey(master.getCollectorProperties(), "projectId", "");
    KaitoHRequestBody<P> body = new KaitoHRequestBody<>();
    body.setBody(param);
    body.setPId(pid);
    body.setSign();
    return body;
  }

  public static String getExtendModel(KvmMaster kvmMaster) {
    return Property.findValueByKey(kvmMaster.getCollectorProperties(), "extendModel", "e");
  }
}
