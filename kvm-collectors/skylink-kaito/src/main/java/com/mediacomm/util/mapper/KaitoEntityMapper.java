package com.mediacomm.util.mapper;

import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInput;
import com.mediacomm.domain.KaitoIpcDetail;
import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.KaitoOutput;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.h.KaitoHIpc;
import com.mediacomm.domain.h.KaitoHLayer;
import com.mediacomm.domain.h.KaitoHScreenDetail;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import java.util.Collection;
import java.util.List;
import org.mapstruct.Builder;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;

/**
 * .
 */
@Mapper(componentModel = "spring", uses = KaitoEntityMapperResolver.class, builder = @Builder(disableBuilder = true),
        mappingControl = DeepClone.class)
public interface KaitoEntityMapper {

  @Mapping(source = "name", target = "alias")
  @Mapping(source = "inputId", target = "deviceId")
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "deviceIp", ignore = true)
  @Mapping(target = "masterId", ignore = true)
  @Mapping(target = "assetId", ignore = true)
  KvmAsset toKvmAsset(KaitoInput kaitoInput, @Context String masterId);

  @Mapping(source = "sourceName", target = "alias")
  @Mapping(source = "sourceName", target = "name")
  @Mapping(source = "sourceId", target = "deviceId")
  @Mapping(source = "ip", target = "deviceIp")
  KvmAsset toKvmAsset(KaitoHIpc kaitoHIpc, @Context String masterId);

  @Mapping(source = "sourceName", target = "alias")
  @Mapping(source = "sourceName", target = "name")
  @Mapping(source = "sourceId", target = "deviceId")
  @Mapping(source = "ip", target = "deviceIp")
  KvmAsset toKvmAsset(KaitoIpcDetail kaitoIpcDetail, @Context String masterId);

  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceId", ignore = true)
  KvmVideoWall toKvmVideoWall(KaitoScreenDetail screenDetail, @Context String masterId);

  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceId", ignore = true)
  KvmVideoWall toKvmVideoWall(KaitoHScreenDetail screenDetail, @Context String masterId);

  @Mapping(source = "outputId", target = "deviceId")
  @Mapping(source = "name", target = "alias")
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "deviceIp", ignore = true)
  @Mapping(target = "masterId", ignore = true)
  @Mapping(target = "assetId", ignore = true)
  KvmAsset toKvmAsset(KaitoOutput kaitoOutput, @Context String masterId);

  @Mapping(source = "layerId", target = "panelId")
  PanelRect toPanelRect(KaitoLayerDetail kaitoLayerDetail, @Context Integer videoWallId);

  @Mapping(source = "layerId", target = "panelId")
  PanelRect toPanelRect(KaitoHLayer kaitoHLayer, @Context Integer videoWallId);

  KaitoLayerDetail toKaitoLayerDetail(PanelRect panelRect, @Context Integer videoWallId);

  KaitoLayerLayoutReq.Layer toKaitoLayerLayoutReqLayer(PanelRect panelRect, @Context Integer videoWallId);

  @Mapping(source = "panelId", target = "layerId")
  KaitoWriteWindowReq toKaitoWriteWindowReq(PanelRect panelRect, @Context Integer videoWallId);

  @Mapping(source = "panelId", target = "layerId")
  KaitoWriteSourceReq toKaitoWriteSourceReq(PanelRect panelRect, @Context Integer videoWallId);

  @Mapping(target = "slotId", ignore = true)
  @Mapping(source = "slotId", target = "deviceId")
  KvmSlot toKvmSlot(KaitoDeviceDetail.Slot slot, @Context String masterId);

  List<KvmSlot> toKvmSlotList(Collection<KaitoDeviceDetail.Slot> slots, @Context String masterId);
}
