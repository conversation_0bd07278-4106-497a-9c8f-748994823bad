package com.mediacomm.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mediacomm.domain.KaitoLayer;
import com.mediacomm.domain.KaitoLayerDetail;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collection;

/**
 * 图层模板开窗请求消息体.
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoLayerLayoutReq {
  private int screenId;
  private int deviceId;
  private Collection<Layer> layers;

  public KaitoLayerLayoutReq(int screenId, int deviceId) {
    this.screenId = screenId;
    this.deviceId = deviceId;
    this.layers = new ArrayList<>();
  }

  @Data
  public static class Layer {
    private KaitoLayer.AudioStatus audioStatus;
    private General general;
    private KaitoLayerDetail.Source source;
    private KaitoLayerDetail.Window window;
  }

  @Data
  public static class General {
    private String name;
    @JsonProperty("zorder")
    private int zorder;
    private int layerId;
  }
}
