package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoLayerDetail {
  private KaitoLayer.AudioStatus audioStatus;
  private int deviceId;
  private General general;
  private int layerId;
  private int screenId;
  private Source source;
  private Window window;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class General {
    private String name;
    @JsonProperty("zorder")
    private int zorder;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Source {
    private int cropId; // 截取 Id，255 表示使用原始源
    private int inputId;
    private int sourceType; // 0：无源,1：输入类型, 255：IPC 类型
    private int ipcSourceId;
    private int streamId;
  }

  @Data
  public static class Window {
    private int height;
    private int width;
    private int x;
    private int y;
  }

  @Override
  public boolean equals(Object o) {
    if (o == null || getClass() != o.getClass()) return false;
    KaitoLayerDetail that = (KaitoLayerDetail) o;
    return Objects.equals(general, that.general) && Objects.equals(source, that.source)
            && Objects.equals(window, that.window);
  }

  @Override
  public int hashCode() {
    return Objects.hash(audioStatus, deviceId, general, layerId, screenId, source, window);
  }
}
