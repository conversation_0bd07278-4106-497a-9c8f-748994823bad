package com.mediacomm.domain.h;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import lombok.Data;
import org.apache.commons.codec.binary.Hex;

/**
 * .
 */
@Data
public class KaitoHRequestBody<T> {
  @JsonProperty("pId") // 防止被转成pid
  private String pId;
  private String timeStamp = String.valueOf(System.currentTimeMillis());
  private String sign;
  private T body;

  /**
   * 禁止加密,base64(md5(timeStamp + pid)).
   */
  public void setSign() {
    // 不建议这种方式，Base64编码的对象其实是原始MD5哈希的十六进制字符串的字节形式，而不是原始的MD5字节。这会导致最终结果不符合预期.
    // 正确的做法应该是：先计算MD5的摘要（digest()方法返回字节），然后直接对这个字节进行Base64编码，而不要使用hexdigest(),
    // hexdigest()会将字节转换为十六进制字符串，这一步是多余的，并且导致结果错误.
    try {
      // 计算MD5二进制哈希（16字节）
      MessageDigest md = MessageDigest.getInstance("MD5");
      byte[] digest = md.digest((timeStamp + pId).getBytes(StandardCharsets.UTF_8));
      // 将二进制哈希转成十六进制字符串（32字符）
      String hexString = Hex.encodeHexString(digest);
      // 将十六进制字符串转为字节（UTF-8编码，32字节）
      byte[] hexBytes = hexString.getBytes(StandardCharsets.UTF_8);
      // 对十六进制字符串的字节做Base64编码
      sign = Base64.getEncoder().encodeToString(hexBytes);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
