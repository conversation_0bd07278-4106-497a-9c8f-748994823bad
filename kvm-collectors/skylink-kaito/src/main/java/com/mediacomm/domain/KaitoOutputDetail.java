package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoOutputDetail {
  private KaitoOutput.AudioInterface audioInterface;
  private int cardCategory;
  private int connectCapacity;
  private General general;
  private int interfaceId;
  private int interfaceType;
  private int isEDIDSetting;
  private int isSupportAudio;
  private int modelId;
  private int outputId;
  private Resolution resolution;
  private int slotId;

  @Data
  public static class General {
    private int colorDepth;
    private int colorSpace;
    private String name;
    private int sampleRate;
  }

  @Data
  public static class Resolution {
    private int height;
    private int width;
    private int direction;
    private int refresh;
    private int outputCount;
  }
}
