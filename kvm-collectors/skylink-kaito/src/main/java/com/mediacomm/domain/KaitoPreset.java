package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Collection;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoPreset {
  private String createTime;
  private int deviceId;
  private Collection<KaitoLayerDetail> layers;
  private String name;
  private int presetId;
  private int screenId;
  private String startTime;
}
