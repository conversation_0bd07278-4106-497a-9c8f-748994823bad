package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import lombok.Data;

/**
 * 设备详情.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoDeviceDetail {
  @JsonProperty("MAC")
  private String mac;
  private CtrlCard ctrlCard;
  private int dataVersion;
  private int deviceId;
  private int ethernetStatus;
  private int extSignal;
  private Collection<Fan> fanList;
  private GenLock genLock;
  private int memory;
  private int modelId;
  private String name;
  private Collection<Power> powerList;
  private String protoVersion;
  private Collection<Slot> slotList;
  private int status;
  private int temp;
  private int volt;

  @Data
  public static class CtrlCard {
    private Collection<DeputyCardInterface> deputyCardInterfaces;
    private int deputySlotId;
    private int deputyState;
    private int mainSlotId;
    private int mainState;
  }

  @Data
  public static class DeputyCardInterface {
    private int iSignal; // 是否有信号
    private int interfaceId;
    private int interfaceType;
    private Collection<SubInterface> subInterfaces;
  }

  @Data
  public static class SubInterface {
    private int iSignal;
    private int interfaceId;
  }

  @Data
  public static class Fan {
    private int fanId;
    private int status;
  }

  @Data
  public static class GenLock {
    private int deviceId;
    private int enable;
    private int extSignal;
    private int inputId;
    private int type;
  }

  @Data
  public static class Power {
    @JsonProperty("iSignal")
    private int iSignal;
    private int powerId;
  }

  @Data
  public static class Slot {
    private int SenderColorDepth;
    private int cardCategory;
    private int cardType;
    private int ethdistance;
    private int funcCode;
    private Collection<Interface> interfaces;
    private int modelId;
    private Resolution resolution;
    private int slotId;
    private int status;
  }

  @Data
  public static class Interface {
    private KaitoInput.AudioInterface audioInterface;
    private int functionType;
    private int iSignal;
    private int interfaceId;
    private int interfaceType;
    private int isSupportAudio;
    private int isUsed;
    private int outputMode;
  }
}
