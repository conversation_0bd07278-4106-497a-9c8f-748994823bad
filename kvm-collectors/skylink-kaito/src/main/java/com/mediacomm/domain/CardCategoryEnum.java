package com.mediacomm.domain;

/**
 * .
 */
public enum CardCategoryEnum {
  NONE,
  VIDEO_INPUT_CARD,
  IPC_CARD,
  VIDEO_OUTPUT_CARD,
  UNDEFINE;

  public static CardCategoryEnum getCardCategory(Integer cardCategory) {
    if (cardCategory == null) {
      return UNDEFINE;
    }
    return switch (cardCategory) {
      case 0 -> NONE;
      case 2, 4, 10 -> VIDEO_INPUT_CARD;
      case 3 -> IPC_CARD;
      case 6, 7, 8 -> VIDEO_OUTPUT_CARD;
      default -> UNDEFINE;
    };
 }
}
