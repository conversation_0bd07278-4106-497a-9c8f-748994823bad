package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoIpcDetailList {
  private int deviceId;
  private int isMontage;
  private int nums;
  private int seqPageIndex;
  private int seqPageSize;
  private Collection<KaitoIpcDetail> sourceList;
}
