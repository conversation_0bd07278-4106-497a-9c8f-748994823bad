package com.mediacomm.domain.h;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mediacomm.domain.KaitoScreenDetail;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoHScreenDetail {
  @JsonProperty("Osd")
  private KaitoScreenDetail.Osd Osd;
  @JsonProperty("OsdImage")
  private KaitoScreenDetail.Osd OsdImage;
  private int brightness;
  private int deviceId;
  private int gamma;
  private KaitoScreenDetail.General general;
  private int mayUse;
  private KaitoScreenDetail.OutputMode outputMode;
  private int screenId;
}
