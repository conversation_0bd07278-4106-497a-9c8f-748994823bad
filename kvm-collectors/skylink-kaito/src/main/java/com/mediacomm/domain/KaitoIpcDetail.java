package com.mediacomm.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Collection;
import lombok.Data;

/**
 * .
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KaitoIpcDetail {
  private int channelCount;
  private String createTime;
  private int deviceId;
  private String ip;
  private int ipcType;
  private int slotId;
  private int sourceId;
  private String sourceName;
  private FirstChannel firstChannel;

  @Data
  public static class FirstChannel {
    private int channelId;
    private String channelName;
    private String createTime;
    private int decodeCapacity;
    private int slotId;
    private int sourceId;
    private String sourceName;
    private Collection<Stream> streamList;
  }
  @Data
  public static class Stream {
    private int connectCapacity;
    private int decodeId;
    private int inputId;
    private int layerCount;
    private int slotId;
    private int streamId;
    private int streamIndex; // 0主码流, 1子码流
    private int videoMode;
    private Protocol protocol;
  }

  @Data
  public static class Protocol {
    private int type;
    private Rtsp rtsp;
  }

  @Data
  public static class Rtsp {
    private String rtspIp;
    private int rtspPort;
    private int manufacturer;
    private String rtspUrl;
    private String rtspUsrName;
    private String rtspPassWord;
  }
}
