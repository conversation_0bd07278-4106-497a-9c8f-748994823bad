package com.mediacomm;

import com.mediacomm.config.KaitoCmdHandler;
import com.mediacomm.domain.CardCategoryEnum;
import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInputDetail;
import com.mediacomm.domain.KaitoOutputDetail;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.entity.dao.SignalValue;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmSlotVo;
import com.mediacomm.system.base.kvm.SignalCollector;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmSlotService;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.mapper.KaitoInspectUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * .
 */
@Slf4j
@Component
public class KaitoSignalsCollector extends SignalCollector {
  @Resource
  private KvmMasterService kvmMasterService;
  @Resource
  private KvmAssetService kvmAssetService;
  @Resource
  private KvmSlotService kvmSlotService;
  @Resource
  private KaitoCmdHandler cmdHandler;
  @Resource
  private KaitoCmdHandler kaitoCmdHandler;

  /**
   * 每10秒刷新一次主机状态.
   */
  @Scheduled(cron = "*/10 * * * * ?")
  @SchedulerLock(name = "kaitoServer", lockAtLeastFor = "PT4S", lockAtMostFor = "PT4S")
  public void reloadKaitoServer() {
    Collection<KvmMaster> kvmMasters = kvmMasterService.allByDeviceModel(
            DeviceType.KAITO02.getDeviceTypeId());
    Map<String, Map<String, String>> deviceSignalValueInCache = new HashMap<>();
    for (KvmMaster kvmMaster : kvmMasters) {
      Map<String, String> masterSignalValue = new HashMap<>();
      KaitoDeviceDetail detail = cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster))
              .getDeviceDetail(kvmMaster);
      if (detail != null) {
        // 主机状态
        addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
                new SignalValue(Boolean.FALSE), masterSignalValue);
        addDeviceSignalValue(kvmMaster, RedisSignalKey.TEMPERATURE,
                new SignalValue(detail.getTemp() == 1), masterSignalValue); // 0：正常，1：异常
        addDeviceSignalValue(kvmMaster, RedisSignalKey.VOLT_STATUS,
                new SignalValue(detail.getVolt() == 1), masterSignalValue); // 0：正常，1：异常

        detail.getFanList().forEach(fan -> {
          String fanRedisKey = "fanOnline." + (fan.getFanId() + 1);
          Signal fanSignal = getSignal(kvmMaster.getDeviceModel(), "fanOnline", fanRedisKey);
          DeviceSignalValue fanSignalValue = setValue(fanSignal,
                  new SignalValue(fan.getStatus() == 1), kvmMaster); // 0：正常，1：异常
          masterSignalValue.put(fanRedisKey, JsonUtils.encode(fanSignalValue));
        });
        detail.getPowerList().forEach(power -> {
          String powerRedisKey = "powerStatus." + (power.getPowerId() + 1);
          Signal powerSignal = getSignal(kvmMaster.getDeviceModel(), "powerStatus", powerRedisKey);
          DeviceSignalValue powerSignalValue = setValue(powerSignal,
                  new SignalValue(power.getISignal() == 0), kvmMaster); // 0：未接入电源，1：已接入
          masterSignalValue.put(powerRedisKey, JsonUtils.encode(powerSignalValue));
        });
        // 板卡状态
        Map<String, Map<String, String>> slotSignalValueInCache = new HashMap<>();
        Collection<KvmSlotVo> slots = new ArrayList<>();
        detail.getSlotList().forEach(slot -> {
          Map<String, String> slotSignalValueMap = new HashMap<>();
          Map<String, String> slotDescMap = new HashMap<>();
          KvmSlotVo slotVo = kvmSlotService.oneByDeviceId(slot.getSlotId(), kvmMaster.getMasterId());
          if (slotVo != null) {
            slots.add(slotVo);
            Signal soltSignal = getSignal(slotVo.getDeviceModel(), RedisSignalKey.SLOT_STATUS);
            DeviceSignalValue slotSignalValue = setValue(soltSignal,
                    new SignalValue(slot.getStatus() == 0), slotVo);
            slotSignalValueMap.put(RedisSignalKey.SLOT_STATUS,
                    JsonUtils.encode(slotSignalValue)); // 子卡在线状态1：false正常，0：true异常
            slotDescMap.put(RedisSignalKey.CARD_TYPE, String.valueOf(
                    CardCategoryEnum.getCardCategory(slot.getCardCategory()))); // 插槽插入的卡类型
            String slotRedisKey =
                    RedisSignalKey.getDeviceStatusKey(slotVo.getDeviceType(), slotVo.getSlotId());
            String slotDescRedisKey =
                    RedisSignalKey.getDeviceDecKey(slotVo.getDeviceType(), slotVo.getSlotId());
            slotSignalValueInCache.put(slotRedisKey, slotSignalValueMap);
            slotSignalValueInCache.put(slotDescRedisKey, slotDescMap);
          }
        });
        redisUtil.batchHashSet(slotSignalValueInCache);
        checkSlotSignalValue(slots);
        // 外设状态
        Collection<KvmAssetVo> assets = kvmAssetService.allByMasterId(kvmMaster.getMasterId());
        Map<String, Map<String, String>> assetSignalValueInCache = new HashMap<>();
        for (KvmAssetVo asset : assets) {
          Map<String, String> assetSignalValueMap = new HashMap<>();
          Map<String, String> assetDescMap = new HashMap<>();
          int slotDeviceId = -1;
          if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
            KaitoInputDetailReq detailReq = new KaitoInputDetailReq(asset.getDeviceId());
            KaitoInputDetail inputDetail = kaitoCmdHandler.getKaitoCmd(
                    KaitoInspectUtil.getExtendModel(kvmMaster)).getInputDetail(kvmMaster, detailReq);
            if (inputDetail != null) {
              if (inputDetail.isOnline()) {
                slotDeviceId = inputDetail.getSlotId();
                addDeviceSignalValue(asset, RedisSignalKey.LINK_STATUS,
                        new SignalValue(Boolean.FALSE), assetSignalValueMap);
                String resolution = String.format("%d x %d %dHz",
                        inputDetail.getResolution().getWidth(), inputDetail.getResolution().getHeight(),
                        inputDetail.getResolution().getRefresh());
                addDeviceSignalValue(asset, RedisSignalKey.RESOLUTION,
                        new SignalValue(resolution), assetSignalValueMap);
                addDeviceSignalValue(asset, RedisSignalKey.VIDEO_LINE_STATUS,
                        new SignalValue(inputDetail.getISignal()), assetSignalValueMap); // 0:无源；1:有源;2:掉源
              } else {
                addDeviceSignalValue(asset, RedisSignalKey.LINK_STATUS,
                        new SignalValue(Boolean.TRUE), assetSignalValueMap);
                log.error(String.format("Kaito input %s is offline", asset.getName()));
              }
            }
          } else if (Objects.equals(asset.getDeviceModel(), DeviceType.KAITO02_OUTPUT.getDeviceTypeId())) {
            KaitoOutputDetailReq detailReq = new KaitoOutputDetailReq(asset.getDeviceId());
            KaitoOutputDetail outputDetails = cmdHandler.getKaitoCmd(
                    KaitoInspectUtil.getExtendModel(kvmMaster)).getOutputDetail(kvmMaster, detailReq);
            if (outputDetails != null) {
              slotDeviceId = outputDetails.getSlotId();
              assetDescMap.put(RedisSignalKey.CARD_TYPE, String.valueOf(
                      CardCategoryEnum.getCardCategory(outputDetails.getCardCategory())));
              String resolution = String.format("%d x %d %dHz",
                      outputDetails.getResolution().getWidth(), outputDetails.getResolution().getHeight(),
                      outputDetails.getResolution().getRefresh());
              addDeviceSignalValue(asset, RedisSignalKey.RESOLUTION,
                      new SignalValue(resolution), assetSignalValueMap);
            }
          }
          if (slotDeviceId != -1) {
            KvmSlotVo slotVo = kvmSlotService.oneByDeviceId(slotDeviceId, kvmMaster.getMasterId());
            assetDescMap.put(RedisSignalKey.CARD_POSITION, slotVo.getSlotId()); // 插入插槽的位置
          }
          if (!DeviceType.KAITO02_IPC.getDeviceType().equals(asset.getDeviceType())) {
            String assetRedisKey = RedisSignalKey.getDeviceStatusKey(asset.getDeviceType(),
                    asset.getAssetId());
            String assetDescRedisKey = RedisSignalKey.getDeviceDecKey(asset.getDeviceType(),
                    asset.getAssetId());
            assetSignalValueInCache.put(assetRedisKey, assetSignalValueMap);
            assetSignalValueInCache.put(assetDescRedisKey, assetDescMap);
          }
        }
        redisUtil.batchHashSet(assetSignalValueInCache);
        checkAssetSignalValue(assets);
      } else {
        addDeviceSignalValue(kvmMaster, RedisSignalKey.LINK_STATUS,
                new SignalValue(Boolean.TRUE), masterSignalValue);
      }
      String masterSignalRk = RedisSignalKey.getDeviceStatusKey(DeviceType.KAITO02.getDeviceType(),
              kvmMaster.getMasterId());
      deviceSignalValueInCache.put(masterSignalRk, masterSignalValue);
    }
    redisUtil.batchHashSet(deviceSignalValueInCache);
    checkMasterSignalValue(kvmMasters, DeviceType.KAITO02.getSubSystem());
  }
}
