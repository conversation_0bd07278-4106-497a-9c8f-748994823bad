package com.mediacomm.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.config.KaitoCmdHandler;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.VideoPanels;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ObjectId;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.SwapVwPanelLayerRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.mapper.KaitoInspectUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

/**
 * .
 */
@Slf4j
@Controller
public class KaitoCmdController extends CmdServer {
  @Resource
  private RedisUtil redisUtil;
  @Resource
  private KaitoCmdHandler cmdHandler;

  /**
   * 重新读取配置.
   */
  public String refreshConfig(String msg) {
    MqRequest<Void> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).refreshConfig(kvmMaster);
    return Result.okStr();
  }

  /**
   * 关窗.
   */
  public String closeVwPanel(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).closePanel(kvmMaster, wall, request.getBody());
    return Result.okStr();
  }

  /**
   * 读取横幅.
   */
  public String getOsdData(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    return Result.okStr(cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).getOsd(kvmMaster, wall));
  }

  /**
   * 设置横幅.
   */
  public String setOsdData(String msg) {
    MqRequest<VwEnableRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    return Result.okStr(cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).enableOsd(kvmMaster, wall, request.getBody()));
  }

  /**
   * 关闭所有窗口.
   */
  public String closeAllVwPanels(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).closeAllPanels(kvmMaster, wall);
    return Result.okStr();
  }

  /**
   * 移动单个窗.
   */
  public String moveVwPanel(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    PanelRect requestPanelRect = request.getBody().getPanelRect();
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).moveVwPanel(kvmMaster, wall, request.getBody());
    return Result.okStr(requestPanelRect);
  }

  @Override
  public String openVwPanels(String msg) {
    MqRequest<OpenVwPanelsRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    VideoPanels videoPanels = new VideoPanels();
    videoPanels.setLayoutData(request.getBody().getLayoutData());
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).openPanels(kvmMaster, wall, request.getBody());
    videoPanels.setPanels(cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).getCurrentPanels(kvmMaster, wall));
    redisUtil.set(getLayoutDataKeyFromRedis(kvmMaster.getMasterId()),
            JsonUtils.encode(videoPanels.getLayoutData()));
    return Result.okStr(videoPanels);
  }

  /**
   * 开窗.
   */
  public String openVwPanel(String msg) {
    MqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    PanelRect panelRect = cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).openPanel(kvmMaster, request.getBody());
    if (panelRect == null) {
      return Result.failureStr("Kaito openVwPanel failed!", ResponseCode.EX_FAILURE_500);
    } else {
      return Result.okStr(panelRect);
    }
  }

  /**
   * 读取窗口.
   */
  public String getVwPanels(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    VideoPanels panels = new VideoPanels();
    panels.setPanels(cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).getCurrentPanels(kvmMaster, wall));
    if (panels.getPanels() == null) {
      return Result.failureStr("Kaito getVwPanels failed!", ResponseCode.EX_FAILURE_500);
    }
    panels.setLayoutData(redisUtil.getStr(getLayoutDataKeyFromRedis(kvmMaster.getMasterId()))
            .map(strData -> JsonUtils.decode(strData, LayoutData.class)).orElse(new LayoutData()));
    return Result.okStr(panels);
  }

  /**
   * 获取预览图.
   */
  public String getTxSnapshot(String msg) {
    MqRequest<ObjectIds> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    return Result.okStr(cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster))
            .getSnapshotStatus(kvmMaster, request.getBody().getIds()));
  }

  /**
   * 交换窗口层级.
   */
  public String swapVwPanelLayer(String msg) {
    MqRequest<SwapVwPanelLayerRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall == null) {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_NOTFOUND_404);
    }
    cmdHandler.getKaitoCmd(KaitoInspectUtil.getExtendModel(kvmMaster)).swapVwPanelLayer(kvmMaster, wall, request.getBody());
    return Result.okStr();
  }

  private String getLayoutDataKeyFromRedis(String masterId) {
    return RedisUtil.redisKey(RedisKey.KVM, "layout.data", masterId);
  }

}
