package com.mediacomm.service;

import com.mediacomm.controller.KaitoHFeignClientApi;
import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInitStatus;
import com.mediacomm.domain.KaitoInput;
import com.mediacomm.domain.KaitoInputDetail;
import com.mediacomm.domain.KaitoInputList;
import com.mediacomm.domain.KaitoIpcDetail;
import com.mediacomm.domain.KaitoLayerDetail;
import com.mediacomm.domain.KaitoOutput;
import com.mediacomm.domain.KaitoOutputDetail;
import com.mediacomm.domain.KaitoOutputList;
import com.mediacomm.domain.KaitoScreen;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.KaitoScreenList;
import com.mediacomm.domain.KaitoVideoServerInfo;
import com.mediacomm.domain.h.KaitoHIpc;
import com.mediacomm.domain.h.KaitoHIpcDetail;
import com.mediacomm.domain.h.<PERSON>HIpcList;
import com.mediacomm.domain.h.<PERSON>er;
import com.mediacomm.domain.h.KaitoHLayerDetailList;
import com.mediacomm.domain.h.KaitoHRequestBody;
import com.mediacomm.domain.h.KaitoHResponseBody;
import com.mediacomm.domain.h.KaitoHScreenDetail;
import com.mediacomm.domain.h.KaitoHWriteOsdReq;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoIpcSourceChanelListReq;
import com.mediacomm.domain.request.KaitoLayerClearReq;
import com.mediacomm.domain.request.KaitoLayerDetailReq;
import com.mediacomm.domain.request.KaitoLayerLayoutReq;
import com.mediacomm.domain.request.KaitoLayerReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.domain.request.KaitoSourceIdReq;
import com.mediacomm.domain.request.KaitoWriteSourceReq;
import com.mediacomm.domain.request.KaitoWriteWindowReq;
import com.mediacomm.domain.request.KaitoZOrderReq;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.body.ObjectId;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.mapper.KaitoEntityMapper;
import com.mediacomm.util.mapper.KaitoInspectUtil;
import jakarta.annotation.Resource;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class KaitoHCmdService extends KaitoCmd {
  private static final int PORT = 8000;
  @Resource
  private KaitoHFeignClientApi cli;
  @Resource
  private KaitoEntityMapper entityMapper;

  @Override
  public String getExtendModel() {
    return "h";
  }

  @Override
  public URI getUri(String masterIp) {
    return URI.create(String.format("http://%s:%d", masterIp, PORT));
  }

  @Override
  public KaitoVideoServerInfo getServerInfo(KvmMaster master) {
    KaitoHResponseBody<KaitoVideoServerInfo> videoServerInfo = cli.getVideoServerInfo(
            getUri(master.getDeviceIp()), KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master));
    if (videoServerInfo.getBody() == null) {
      log.error("{} {}", "Kaito-H getServerInfo failed!", videoServerInfo);
    }
    return videoServerInfo.getBody();
  }

  @Override
  public List<PanelRect> getCurrentPanels(KvmMaster master, KvmVideoWall wall) {
    KaitoLayerReq requestParam = new KaitoLayerReq(0, wall.getDeviceId());
    KaitoHResponseBody<KaitoHLayerDetailList> responseBody = cli.getLayerList(
            getUri(master.getDeviceIp()), KaitoInspectUtil.buildKaitoHReqBody(requestParam, master));
    List<PanelRect> panels = new ArrayList<>();
    if (responseBody.getBody() != null) {
      for (KaitoHLayer screenLayer : responseBody.getBody().getScreenLayers()) {
        PanelRect panelRect = entityMapper.toPanelRect(screenLayer, wall.getWallId());
        panels.add(panelRect);
      }
    }
    return panels;
  }

  @Override
  public PanelRect openPanel(KvmMaster master, PanelRectRequestBody requestBody) {
    KaitoLayerDetail requestParam =
            entityMapper.toKaitoLayerDetail(requestBody.getPanelRect(), requestBody.getId());
    KaitoHResponseBody<ObjectId> responseBody = cli.createLayer(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(requestParam, master));
    requestBody.getPanelRect().setPanelId(responseBody.getBody().getId());
    return requestBody.getPanelRect();
  }

  @Override
  public void openPanels(KvmMaster master, KvmVideoWall wall, OpenVwPanelsRequestBody requestBody) {
    KaitoLayerLayoutReq detailListReq = new KaitoLayerLayoutReq(wall.getDeviceId(), 0);
    detailListReq.setScreenId(wall.getDeviceId());
    for (PanelRect panel : requestBody.getPanelData().getPanels()) {
      KaitoLayerLayoutReq.Layer layer =
              entityMapper.toKaitoLayerLayoutReqLayer(panel, wall.getWallId());
      detailListReq.getLayers().add(layer);
    }
    URI uri = getUri(master.getDeviceIp());
    cli.createLayers(uri, KaitoInspectUtil.buildKaitoHReqBody(detailListReq, master));
  }

  @Override
  public void moveVwPanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    List<PanelRect> currentPanels = getCurrentPanels(master, wall);
    for (PanelRect currentPanel : currentPanels) {
      // 换源
      if (currentPanel.getXpos() == requestBody.getPanelRect().getXpos()
              && currentPanel.getYpos() == requestBody.getPanelRect().getYpos()
              && currentPanel.getWidth() == requestBody.getPanelRect().getWidth()
              && currentPanel.getHeight() == requestBody.getPanelRect().getHeight()
              && !currentPanel.getVideoSrcId().equals(requestBody.getPanelRect().getVideoSrcId())) {
        KaitoWriteSourceReq kaitoWriteSourceReq =
                entityMapper.toKaitoWriteSourceReq(requestBody.getPanelRect(),
                        wall.getWallId());
        cli.writeSource(uri, KaitoInspectUtil.buildKaitoHReqBody(kaitoWriteSourceReq, master));
        return;
      }
    }
    KaitoWriteWindowReq kaitoWriteWindowReq = entityMapper.toKaitoWriteWindowReq(
            requestBody.getPanelRect(),requestBody.getId());
    cli.writeWindow(uri, KaitoInspectUtil.buildKaitoHReqBody(kaitoWriteWindowReq, master));
  }

  @Override
  public void closeAllPanels(KvmMaster master, KvmVideoWall wall) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerClearReq clearReq = new KaitoLayerClearReq(0, wall.getDeviceId());
    cli.clearLayer(uri, KaitoInspectUtil.buildKaitoHReqBody(clearReq, master));
  }

  @Override
  public void closePanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerDetailReq kaitoLayerDetailReq = new KaitoLayerDetailReq(0,
            wall.getDeviceId(), requestBody.getPanelRect().getPanelId());
    cli.deleteLayer(uri, KaitoInspectUtil.buildKaitoHReqBody(kaitoLayerDetailReq, master));
  }

  @Override
  public KaitoScreenDetail.Osd enableOsd(KvmMaster master, KvmVideoWall wall, VwEnableRequestBody requestBody) {
    URI uri = getUri(master.getDeviceIp());
    KaitoHWriteOsdReq requestParam = new KaitoHWriteOsdReq(0, wall.getDeviceId(),
            requestBody.isEnable() ? 1 : 0, 0);
    KaitoHResponseBody<KaitoScreenDetail.Osd> responseBody = cli.writeOsd(uri,
            KaitoInspectUtil.buildKaitoHReqBody(requestParam, master));
    KaitoScreenDetail.Osd osd = responseBody.getBody();
    if (osd == null) {
      log.error("Write osd data failed! {}", JsonUtils.encode(responseBody));
      return null;
    }
    return osd;
  }

  @Override
  public KaitoScreenDetail.Osd getOsd(KvmMaster master, KvmVideoWall wall) {
    URI uri = getUri(master.getDeviceIp());
    KaitoLayerReq requestParam = new KaitoLayerReq(0, wall.getDeviceId());
    KaitoHResponseBody<KaitoHScreenDetail> responseBody =
            cli.getScreenReadDetail(uri, KaitoInspectUtil.buildKaitoHReqBody(requestParam, master));
    KaitoHScreenDetail screenDetail = responseBody.getBody();
    if (screenDetail != null) {
      // 需要计算上偏移量
      int posX = screenDetail.getOutputMode().getSize().getX();
      int posY = screenDetail.getOutputMode().getSize().getY();
      screenDetail.getOsd().setX(screenDetail.getOsd().getX() - posX);
      screenDetail.getOsd().setY(screenDetail.getOsd().getY() - posY);
      return screenDetail.getOsd();
    } else {
      log.error("Get osd data failed! {}", JsonUtils.encode(responseBody));
      return null;
    }
  }

  @Override
  public KaitoInitStatus getInitStatus(KvmMaster master) {
    return cli.getInitStatus(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master)).getBody();
  }

  @Override
  public KaitoDeviceDetail getDeviceDetail(KvmMaster master) {
    return cli.getDeviceDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master)).getBody();
  }

  @Override
  public List<KvmAsset> getAssets(KvmMaster master) {
    URI uri = getUri(master.getDeviceIp());
    // input
    KaitoHResponseBody<KaitoInputList> inputList = cli.getInputReadList(uri,
            KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master));
    List<KvmAsset> assets = new ArrayList<>();
    if (inputList.getBody() != null) {
      for (KaitoInput input : inputList.getBody().getInputs()) {
        if (input.isOnline()) {
          assets.add(entityMapper.toKvmAsset(input, master.getMasterId()));
        }
      }
    }
    // output
    KaitoHResponseBody<KaitoOutputList> outputList = cli.getOutputReadList(uri,
            KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master));
    if (outputList.getBody() != null) {
      for (KaitoOutput output : outputList.getBody().getOutputs()) {
        assets.add(entityMapper.toKvmAsset(output, master.getMasterId()));
      }
    }
    // ipc
    KaitoHResponseBody<KaitoHIpcList> ipcSourceList = cli.getIpcSourceList(uri,
                    KaitoInspectUtil.buildKaitoHReqBody(new KaitoIpcSourceChanelListReq(), master));
    if (ipcSourceList.getBody() != null) {
      for (KaitoHIpc ipc : ipcSourceList.getBody().getSourceList()) {
        KvmAsset asset = entityMapper.toKvmAsset(ipc, master.getMasterId());
        KaitoHResponseBody<KaitoHIpcDetail> kaitoIpcDetail = cli.getIpcChannelList(uri,
                KaitoInspectUtil.buildKaitoHReqBody(new KaitoSourceIdReq(ipc.getSourceId()), master));
        List<Property> property = new ArrayList<>();
        for (KaitoIpcDetail.Stream stream : kaitoIpcDetail.getBody().getStreamList()) {
          KaitoIpcDetail.Rtsp rtsp = stream.getProtocol().getRtsp();
          String fullPath = String.format("rtsp://%s:%s@%s", rtsp.getRtspUsrName(), rtsp.getRtspPassWord(),
                  rtsp.getRtspUrl().substring(rtsp.getRtspUrl().indexOf(":") + 3));
          switch (stream.getStreamIndex()) {
            case 0 -> {
              property.add(new Property("mainStreamId", String.valueOf(stream.getStreamId())));
              property.add(new Property("mainStreamUrl", fullPath));
            }
            case 1 -> {
              property.add(new Property("subStreamId", String.valueOf(stream.getStreamId())));
              property.add(new Property("subStreamUrl", fullPath));
            }
            default -> log.warn("Undefined kaito stream index: {}", stream.getStreamIndex());
          }
        }
        asset.setProperties(property);
        assets.add(asset);
      }
    }
    return assets;
  }

  @Override
  public List<KvmVideoWall> getVideoWalls(KvmMaster master) {
    KaitoHResponseBody<KaitoScreenList> screenList = cli.getScreenReadList(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(DEVICE_ID_REQUEST, master));
    KaitoScreenList screens = screenList.getBody();
    List<KvmVideoWall> videoWalls = new ArrayList<>();
    if (screens != null) {
      for (KaitoScreen screen : screens.getScreens()) {
        KaitoLayerReq layerReq = new KaitoLayerReq(0, screen.getScreenId());
        KaitoHRequestBody<KaitoLayerReq> layerReqBody = KaitoInspectUtil.buildKaitoHReqBody(layerReq, master);
        KaitoHResponseBody<KaitoHScreenDetail> screenDetail =
                cli.getScreenReadDetail(getUri(master.getDeviceIp()), layerReqBody);
        KaitoHScreenDetail kaitoScreenDetail = screenDetail.getBody();
        if (kaitoScreenDetail != null) {
          KvmVideoWall wall =
                  entityMapper.toKvmVideoWall(kaitoScreenDetail, master.getMasterId());
          videoWalls.add(wall);
        }
      }
    }
    return videoWalls;
  }

  @Override
  public List<KvmSlot> getSlots(KvmMaster master) {
    KaitoDeviceDetail detail = getDeviceDetail(master);
    if (detail != null) {
      return entityMapper.toKvmSlotList(detail.getSlotList(), master.getMasterId());
    }
    return new ArrayList<>();
  }

  @Override
  public KaitoInputDetail getInputDetail(KvmMaster master, KaitoInputDetailReq requestBody) {
    KaitoHResponseBody<KaitoInputDetail> deviceDetail = cli.getInputDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(requestBody, master));
    return deviceDetail.getBody();
  }

  @Override
  public KaitoOutputDetail getOutputDetail(KvmMaster master, KaitoOutputDetailReq requestBody) {
    return cli.getOutputDetail(getUri(master.getDeviceIp()),
            KaitoInspectUtil.buildKaitoHReqBody(requestBody, master)).getBody();
  }

  @Override
  public void writeZIndex(KvmMaster master, KaitoZOrderReq requestBody) {
    cli.writeZIndex(getUri(master.getDeviceIp()), KaitoInspectUtil.buildKaitoHReqBody(requestBody, master));
  }
}
