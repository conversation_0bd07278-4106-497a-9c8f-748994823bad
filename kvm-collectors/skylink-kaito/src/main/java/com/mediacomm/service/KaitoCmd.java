package com.mediacomm.service;

import com.mediacomm.domain.KaitoDeviceDetail;
import com.mediacomm.domain.KaitoInitStatus;
import com.mediacomm.domain.KaitoInputDetail;
import com.mediacomm.domain.KaitoOutputDetail;
import com.mediacomm.domain.KaitoScreenDetail;
import com.mediacomm.domain.KaitoVideoServerInfo;
import com.mediacomm.domain.request.KaitoDeviceIdReq;
import com.mediacomm.domain.request.KaitoInputDetailReq;
import com.mediacomm.domain.request.KaitoOutputDetailReq;
import com.mediacomm.domain.request.KaitoZOrderReq;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.message.reqeust.body.SwapVwPanelLayerRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmSlotService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.SnapshotType;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class KaitoCmd {
  protected static final KaitoDeviceIdReq DEVICE_ID_REQUEST = new KaitoDeviceIdReq(0);

  @Resource
  private KvmAssetService assetService;
  @Resource
  private KvmMasterService kvmMasterService;
  @Resource
  private KvmVideoWallService videoWallService;
  @Resource
  private KvmSlotService kvmSlotService;

  public abstract String getExtendModel();

  protected abstract URI getUri(String masterIp);

  protected abstract KaitoVideoServerInfo getServerInfo(KvmMaster master);

  public abstract List<PanelRect> getCurrentPanels(KvmMaster master, KvmVideoWall wall);

  public abstract PanelRect openPanel(KvmMaster master, PanelRectRequestBody requestBody);

  public abstract void openPanels(KvmMaster master, KvmVideoWall wall, OpenVwPanelsRequestBody requestBody);

  public abstract void moveVwPanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody);

  public abstract void closeAllPanels(KvmMaster master, KvmVideoWall wall);

  public abstract void closePanel(KvmMaster master, KvmVideoWall wall, PanelRectRequestBody requestBody);

  public abstract KaitoScreenDetail.Osd enableOsd(KvmMaster master, KvmVideoWall wall, VwEnableRequestBody requestBody);

  public abstract KaitoScreenDetail.Osd getOsd(KvmMaster master, KvmVideoWall wall);

  public abstract KaitoInitStatus getInitStatus(KvmMaster master);

  public abstract KaitoDeviceDetail getDeviceDetail(KvmMaster master);

  public abstract List<KvmAsset> getAssets(KvmMaster master);

  public abstract List<KvmVideoWall> getVideoWalls(KvmMaster master);

  public abstract List<KvmSlot> getSlots(KvmMaster master);

  public abstract KaitoInputDetail getInputDetail(KvmMaster master, KaitoInputDetailReq requestBody);

  public abstract KaitoOutputDetail getOutputDetail(KvmMaster master, KaitoOutputDetailReq requestBody);

  protected abstract void writeZIndex(KvmMaster master, KaitoZOrderReq requestBody);

  public void swapVwPanelLayer(KvmMaster master, KvmVideoWall wall, SwapVwPanelLayerRequestBody requestBody) {
    KaitoZOrderReq zOrderReq = new KaitoZOrderReq();
    if (requestBody.getSeq1() > requestBody.getSeq2()) {
      zOrderReq.setLayersZOrderAct(1);
    } else {
      zOrderReq.setLayersZOrderAct(2);
    }
    zOrderReq.setLayerId(requestBody.getPanelId1());
    zOrderReq.setDeviceId(0);
    zOrderReq.setScreenId(wall.getDeviceId());
    writeZIndex(master, zOrderReq);
  }

  public List<SnapshotStatus> getSnapshotStatus(KvmMaster master, @NotNull List<String> deviceIds) {
    KaitoVideoServerInfo info = getServerInfo(master);
    List<SnapshotStatus> statuses = new ArrayList<>();
    if (info != null) {
      URI rtsp = URI.create(info.getMvrUrl());
      String wsMvr = "ws://" + rtsp.getHost() + ":" + (rtsp.getPort() - 10);
      for (String id : deviceIds) {
        KvmAsset input = assetService.getById(id);
        SnapshotStatus status;
        if (input != null && info.getVideoStatus() == 1
                && Objects.equals(input.getDeviceModel(),
                DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
          String path = String.format("kaito?mvr=%s&config=%s&id=%d", wsMvr,
                  info.getConfigUrl(), input.getDeviceId());
          status = SnapshotStatus.builder()
                  .id(id)
                  .path(path)
                  .type(SnapshotType.KAITO02)
                  .masterId(input.getMasterId())
                  .linkStatus(true).build();
        } else if (input != null
                && Objects.equals(input.getDeviceModel(), DeviceType.KAITO02_IPC.getDeviceTypeId())) {
          String path;
          String streamType = Property.findValueByKey(
                  input.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "mainStreamId");
          if ("subStreamId".equals(streamType)) {
            path = Property.findValueByKey(input.getProperties(), "subStreamUrl", "");
          } else {
            path = Property.findValueByKey(input.getProperties(), "mainStreamUrl", "");
          }
          status = SnapshotStatus.builder()
                  .id(id)
                  .path(path)
                  .type(SnapshotType.RTSP)
                  .masterId(input.getMasterId())
                  .linkStatus(true).build();
        } else {
          status = SnapshotStatus.builder()
                  .id(id)
                  .type(SnapshotType.SKYLINK)
                  .linkStatus(false).build();
        }
        statuses.add(status);
      }
    }
    return statuses;
  }

  public void refreshConfig(KvmMaster kvmMaster) {
    KaitoInitStatus kaitoInitStatus = getInitStatus(kvmMaster);
    if (kaitoInitStatus != null && kaitoInitStatus.getInitStatus() == 1) { // 1 完成初始化
      KaitoDeviceDetail detail = getDeviceDetail(kvmMaster);
      if (detail != null) {
        List<Property> properties = new ArrayList<>();
        properties.add(new Property("mainModelId",
                String.valueOf(kaitoInitStatus.getMainModelId())));
        properties.add(new Property("totalFanNumber",
                String.valueOf(detail.getFanList().size())));
        properties.add(new Property("totalPowerNumber",
                String.valueOf(detail.getPowerList().size())));
        properties.add(new Property("totalSlotNumber",
                String.valueOf(detail.getSlotList().size())));
        properties.add(new Property("protoVersion",
                String.valueOf(detail.getProtoVersion())));
        properties.add(new Property("mac",
                String.valueOf(detail.getMac())));
        kvmMaster.setVersion(kaitoInitStatus.getSoftwareVersion());
        kvmMaster.setAlias(detail.getName());
        kvmMaster.setProperties(properties);
        kvmMasterService.updateById(kvmMaster);
        List<KvmSlot> slots = getSlots(kvmMaster);
        kvmSlotService.saveOrUpdateBatchByDeviceIdAndMasterId(slots);
      }
    } else {
      log.error("Kaito device is not ready!");
      return;
    }
    for (KvmAsset asset : getAssets(kvmMaster)) {
      assetService.saveOrUpdateKvmAsset(asset, kvmMaster.getMasterId());
    }
    for (KvmVideoWall videoWall : getVideoWalls(kvmMaster)) {
      videoWallService.saveOrUpdateByUniqueSearchKey(videoWall);
    }
  }
}
