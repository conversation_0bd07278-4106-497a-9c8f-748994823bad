#!/bin/bash

set -eux #指令返回值不为0则退出，执行未定义变量显示错误信息

version=""
COMPILE_VERSION="0.0.1"

if [[ $EUID -ne 0 ]]; then
    echo "Error:This script must be run as root!" 1>&2
    exit 1
fi

function help() {
    echo "用法:"
    echo "build.sh -v [version] [-arg]"
    echo "必须先指定版本再继续其他操作"
    echo "选项:"
    echo "    -h,help."
    echo "    -a,构建所有."
    echo "    -c,容器化 DCIM."
    echo "    -r,移除指定版本的安装包."
    echo "    -s,编译打包 DCIM."
    echo "    -p,推送DCIM镜像到仓库."
    exit
}

function buildImage() {
  echo 'Build the image.'
  docker build --force-rm --squash --build-arg VERSION="${version}" -t mediacomm/skylink-server:"${version}" .
}

function buildUpgradePatch() {
  echo 'Build the upgrade patch.'
  if test ! -n "$(docker images -q mediacomm/skylink-server-compile:"${COMPILE_VERSION}")"; then
      echo "mediacomm/skylink-server-compile:${COMPILE_VERSION} does not exist"
      echo "please pull baseImage from remote registry,or build one"
      echo "build cmd: docker build --squash --force-rm -f Dockerfile.compile -t mediacomm/skylink-server-compile:${COMPILE_VERSION} ."
      exit 1
  fi
  docker run -it --rm  -w "/install" -v "$(pwd)":/install -v /home/<USER>/.m2/repository/:/root/.m2/repository/ mediacomm/skylink-server-compile:"${COMPILE_VERSION}" ./package.sh "${version}"
}
