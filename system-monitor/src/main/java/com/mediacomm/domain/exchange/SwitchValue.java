package com.mediacomm.domain.exchange;

import com.mediacomm.domain.DeviceValue;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SwitchValue extends DeviceValue {
  private Map<Integer, Integer> fanTypes = new HashMap<>();
  private Map<Integer, Integer> powerTypes = new HashMap<>();
  private Map<Integer, Integer> temSensorTypes = new HashMap<>();
  private Map<Integer, Double> portSpeeds = new HashMap<>();
  private Map<Integer, String> portDesc = new HashMap<>();
  private Map<Integer, Integer> portTypes = new HashMap<>();
  private String desc;
}
