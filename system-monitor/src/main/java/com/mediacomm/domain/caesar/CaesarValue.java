package com.mediacomm.domain.caesar;

import com.mediacomm.domain.DeviceValue;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * .
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CaesarValue extends DeviceValue {
  private String descr;
  private Integer totalRxNumber;
  private Integer totalTxNumber;
  private Integer onlineRxNumber;
  private Integer onlineTxNumber;
  private Map<String, CaesarExtender> extenderMap = new HashMap<>();
  private long updateTime;
}
