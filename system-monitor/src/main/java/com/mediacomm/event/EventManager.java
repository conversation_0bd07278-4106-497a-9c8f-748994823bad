package com.mediacomm.event;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.alarm.AlarmEventManger;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.SignalsAlarmEventRequestBody;
import com.mediacomm.event.listener.EvenListener;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Slf4j
@Component
public class EventManager implements CommandLineRunner {
  @Resource
  AutowireCapableBeanFactory beanFactory;
  @Resource
  DefaultListableBeanFactory defaultListableBeanFactory;
  @Resource
  AlarmEventManger alarmEventManger;
  @Resource
  private RedisUtil redisUtil;

  private Map<DeviceType, EvenListener> listeners = new HashMap<>();

  @Override
  public void run(String... args) throws Exception {
    ClassPathScanningCandidateComponentProvider provider =
            new ClassPathScanningCandidateComponentProvider(false);
    provider.addIncludeFilter(new AssignableTypeFilter(EvenListener.class));
    Set<BeanDefinition> components =
            provider.findCandidateComponents(EvenListener.class.getPackageName());
    for (BeanDefinition component : components) {
      Class<?> cls = Class.forName(component.getBeanClassName());
      EvenListener listener = (EvenListener) cls.getDeclaredConstructor().newInstance();
      String className = component.getBeanClassName();
      if (StringUtils.isNotEmpty(className)) {
        defaultListableBeanFactory.registerSingleton(className, listener);
        beanFactory.autowireBean(listener);
        listeners.put(listener.getDeviceType(), listener);
      }
    }
  }

  public String getValue(String msg) {
    MqRequest<DeviceType> req = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (req != null
            && StringUtils.isNotBlank(req.getMasterId())
            && req.getBody() != null) {
      EvenListener listener = listeners.get(req.getBody());
      if (listener == null) {
        return Result.failureStr("No such listener:" + req.getBody(),
                ResponseCode.EX_FAILURE_400);
      }
      return listener.getValue(msg);
    } else {
      return Result.failureStr("Incorrect request format", ResponseCode.EX_FAILURE_400);
    }
  }

  public String buildAlarm(String msg) {
    MqRequest<SignalsAlarmEventRequestBody> req = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (req != null && req.getBody() != null) {
      Collection<DeviceSignalValue> values = new ArrayList<>();
      Optional<Map<String, String>> res = redisUtil.hmget(req.getBody().getRedisKey());
      res.ifPresent(map -> map.forEach((k, v) -> {
        DeviceSignalValue value = JsonUtils.decode(v, new TypeReference<>() {
        });
        if (value != null) {
          values.add(value);
        }
      }));
      alarmEventManger.doEvent(values, req.getBody().getSubSystemType());
      return Result.okStr();
    } else {
      return Result.failureStr("Incorrect request format", ResponseCode.EX_FAILURE_400);
    }
  }
}
