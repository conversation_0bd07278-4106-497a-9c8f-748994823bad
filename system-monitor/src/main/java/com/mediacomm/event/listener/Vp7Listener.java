package com.mediacomm.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * .
 */
public class Vp7Listener extends EvenListener {
  @Resource
  protected KvmAssetService assetService;
  @Resource
  RedisUtil redisUtil;

  @Override
  public String getValue(String request) {
    MqRequest req = JsonUtils.decode(request, new TypeReference<>() {
    });
    // 载入设备型号的信号
    Collection<DeviceSignalValue> values = new ArrayList<>();
    KvmAssetVo vp7 = assetService.oneById(req.getMasterId());
    if (vp7 != null) {
      String rk = RedisSignalKey
              .getDeviceStatusKey(vp7.getDeviceType(), vp7.getAssetId());
      Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(rk);
      deviceSignalValue.ifPresent(stringStringMap -> addDeviceSignalValue(stringStringMap, values));
    }
    if (values.isEmpty()) {
      return Result.failureStr("Data not ready.", ResponseCode.EX_NOTFOUND_404);
    }
    return Result.okStr(values);
  }

  @Override
  public DeviceType getDeviceType() {
    // 需求以大屏进行查询对应的大屏绑定的所有vp7
    return DeviceType.CAESAR_VP7;
  }
}
