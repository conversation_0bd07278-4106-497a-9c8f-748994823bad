package com.mediacomm.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.domain.exchange.NetgearSignalKeyFromCache;
import com.mediacomm.domain.exchange.SwitchValue;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EnvDevice;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.system.service.EnvDeviceService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class NetgearListener extends EvenListener {
  @Resource
  EnvDeviceService envDeviceService;
  @Resource
  RedisUtil redisUtil;

  /**
   * key:deviceId,value:SwitchValue.
   */
  @Override
  public String getValue(String request) {
    MqRequest req = JsonUtils.decode(request, new TypeReference<>() {
    });
    String deviceId = req.getMasterId();
    EnvDevice device = envDeviceService.getById(deviceId);
    if (device == null) {
      return Result.failureStr("Netgear device no exist.", ResponseCode.EX_FAILURE_400);
    }
    String rk = RedisSignalKey.getDeviceStatusKey(DeviceType.NETGEAR.getDeviceType(), device.getId());
    String descRk = RedisSignalKey.getDeviceDecKey(DeviceType.NETGEAR.getDeviceType(), device.getId());
    Optional<Map<String, String>> deviceSignalValue =
            redisUtil.hmget(rk);
    Optional<Map<String, String>> deviceDesc = redisUtil.hmget(descRk);
    SwitchValue switchValue = new SwitchValue();
    List<DeviceSignalValue> ds = new ArrayList<>();
    if (deviceDesc.isPresent() && deviceSignalValue.isPresent()) {
      addDeviceSignalValue(deviceSignalValue.get(), ds);
      deviceDesc.get().forEach((k, v) -> {
        switch (k) {
          case NetgearSignalKeyFromCache.DESC -> switchValue.setDesc(v);
          case NetgearSignalKeyFromCache.PORT_DESC ->
                  switchValue.setPortDesc(JsonUtils.decode(v, new TypeReference<>() {}));
          case NetgearSignalKeyFromCache.PORT_TYPE ->
                  switchValue.setPortTypes(JsonUtils.decode(v, new TypeReference<>() {}));
          case NetgearSignalKeyFromCache.PORT_SPEED ->
                  switchValue.setPortSpeeds(JsonUtils.decode(v, new TypeReference<>() {}));
          case NetgearSignalKeyFromCache.FAN_TYPE ->
                  switchValue.setFanTypes(JsonUtils.decode(v, new TypeReference<>() {}));
          case NetgearSignalKeyFromCache.POWER_TYPE ->
                  switchValue.setPowerTypes(JsonUtils.decode(v, new TypeReference<>() {}));
          case NetgearSignalKeyFromCache.TEMP_SENSOR_TYPE ->
                  switchValue.setTemSensorTypes(JsonUtils.decode(v, new TypeReference<>() {}));
          default -> log.warn("Enc description field that was not processed {}", k);
        }
      });
    }
    deviceSignalValue.ifPresent(stringStringMap -> stringStringMap.forEach((k, v) -> {

    }));
    switchValue.getSignalValues().put(device.getId(), ds);
    return Result.okStr(switchValue);
  }

  @Override
  public DeviceType getDeviceType() {
    return DeviceType.NETGEAR;
  }
}
