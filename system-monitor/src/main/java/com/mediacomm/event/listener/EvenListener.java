package com.mediacomm.event.listener;

import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import java.util.Collection;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class EvenListener {

  public abstract String getValue(String request);

  public abstract DeviceType getDeviceType();

  /**
   * 将从redis中取出从信号量缓存重新序列化后添加到list中.
   */
  public void addDeviceSignalValue(Map<String, String> src, Collection<DeviceSignalValue> target) {
    for (Map.Entry<String, String> entry : src.entrySet()) {
      DeviceSignalValue v = JsonUtils.decode(entry.getValue(), DeviceSignalValue.class);
      if (v != null) {
        target.add(v);
      } else {
        log.error("Error device signal value format: {}", entry.getValue());
      }
    }
  }
}
