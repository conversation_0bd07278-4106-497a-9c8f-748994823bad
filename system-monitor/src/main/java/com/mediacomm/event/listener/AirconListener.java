package com.mediacomm.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.domain.DeviceValue;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * .
 */
public class AirconListener extends EvenListener {
  @Resource
  protected KvmMasterService kvmMasterService;
  @Resource
  protected KvmAssetService assetService;
  @Resource
  RedisUtil redisUtil;

  @Override
  public String getValue(String request) {
    MqRequest req = JsonUtils.decode(request, new TypeReference<>() {
    });
    // 载入设备型号的信号
    KvmMasterVo master = kvmMasterService.oneById(req.getMasterId());
    if (master == null) {
      return Result.failureStr("Aircon device no exist.", ResponseCode.EX_FAILURE_400);
    }
    String rk = RedisSignalKey
            .getDeviceStatusKey(master.getDeviceType().getDeviceType(), master.getMasterId());
    Optional<Map<String, String>> deviceSignalValue = redisUtil.hmget(rk);
    if (deviceSignalValue.isPresent()) {
      DeviceValue currentValue = new DeviceValue();
      currentValue.setMasterId(master.getMasterId());
      List<DeviceSignalValue> ds = new ArrayList<>();
      addDeviceSignalValue(deviceSignalValue.get(), ds);
      currentValue.getSignalValues().put(master.getMasterId(), ds); // 主机信号
      // 批量读取assets signal
      Collection<KvmAssetVo> assets = assetService.allByMasterId(req.getMasterId());
      Collection<String> cacheKeys = new ArrayList<>();
      assets.forEach(asset ->
              cacheKeys.add(RedisSignalKey
                      .getDeviceStatusKey(asset.getDeviceType(), asset.getAssetId())));
      Map<String, Map<String, String>> assetSignalAndDescMap = redisUtil.batchHashGet(cacheKeys);
      assets.forEach(asset -> {
        List<DeviceSignalValue> assetSignalValues = new ArrayList<>();
        addDeviceSignalValue(assetSignalAndDescMap.get(RedisSignalKey
                .getDeviceStatusKey(asset.getDeviceType(), asset.getAssetId())), assetSignalValues);
        currentValue.getSignalValues().put(asset.getAssetId(), assetSignalValues);
      });
      return Result.okStr(currentValue);
    }
    return Result.failureStr("Data not ready.", ResponseCode.EX_NOTFOUND_404);
  }

  @Override
  public DeviceType getDeviceType() {
    return DeviceType.AIRCON;
  }
}
