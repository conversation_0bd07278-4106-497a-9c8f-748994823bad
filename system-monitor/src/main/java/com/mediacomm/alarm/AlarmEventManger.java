package com.mediacomm.alarm;

import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.dao.DisabledAlarm;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.system.service.AlarmService;
import com.mediacomm.system.service.DisabledAlarmService;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.AlarmEntityMapper;
import com.mediacomm.util.mq.RabbitSender;
import jakarta.annotation.Resource;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * .
 */
@Component
@Slf4j
public class AlarmEventManger {
  @Resource
  AlarmService alarmService;
  @Resource
  DisabledAlarmService disabledAlarmService;
  @Resource
  AlarmEntityMapper AlarmEntityMapperImpl;
  @Resource
  RabbitSender sender;

  public boolean isDisabled(DeviceSignalValue value) {
    DisabledAlarm alarm = disabledAlarmService
        .oneByMasterIdAndDeviceIdAndSignalId(value.getMasterId(), value.getDeviceId(),
            value.getSignalId());
    return alarm != null ? Boolean.TRUE : Boolean.FALSE;
  }

  /**
   * 执行告警事件,信号量恢复正常将告警结束,信号量异常生成告警.
   *
   * @param value 设备信号值.
   * @param subSystemType 子系统类型.
   */
  public void doEvent(DeviceSignalValue value, SubSystemType subSystemType) {
    if (value.getDeviceId() == null || value.getDeviceId().isEmpty() || value.getSignalId() == null
        || value.getSignalId().isEmpty()) {
      return;
    }
    if (isDisabled(value)) {
      return;
    }
    Alarm alarm = alarmService.oneByMasterIdAndDeviceIdAndSignalIdAndNoEnd(
            value.getMasterId(), value.getDeviceId(), value.getSignalId());
    boolean alarmChanged = false;
    if (value.getAlarmLevel() == SubSystemType.AlarmLevel.LEVEL_0) {
      if (alarm != null) {
        alarm.setEnded(true);
        alarm.setEndTime(System.currentTimeMillis());
        alarmService.updateById(alarm);
        alarmChanged = true;
      }
    } else if (alarm == null || alarm.isEnded()) {
      alarm = AlarmEntityMapperImpl.toAlarm(value);
      //alarm.setAlarmId(alarmService.getMaxId() + 1);
      alarm.setSubSystem(subSystemType);
      alarm.setBeginTime(System.currentTimeMillis());
      alarmService.save(alarm);
      alarmChanged = true;
    }
    if (alarmChanged) {
      MqRequest<Alarm> request = new MqRequest<>();
      request.setMasterId(alarm.getMasterId());
      request.setBody(alarm);
      sender.asyncSend(RoutingKey.WEB_SERVER_CHANGE_ALARM_STATUS, request);
    }
  }

  public void doEvent(Collection<DeviceSignalValue> values, SubSystemType subSystemType) {
    if (values != null) {
      for (DeviceSignalValue value : values) {
        doEvent(value, subSystemType);
      }
    }
  }
}
