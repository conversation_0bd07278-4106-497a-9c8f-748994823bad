package com.mediacomm.log;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * .
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@Slf4j
@SpringBootTest
public class LogTest {
  @Test
  public void test() {
    final LocalDateTime now = LocalDateTime.now();
    final String format = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss,SSS"));

    log.info("now -> {}", format);
    log.debug("debug ...");
    log.info("info ...");
    log.warn("warn ...");
    log.error("error ...");
  }
}
