package com.mediacomm.token;

import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.pojo.PayloadDto;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.util.JwtUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * .
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TokenTest {
  @Autowired
  AccountService service;

  @Test
  public void testCreateToken() {
    AccountVo account = service.oneByName("admin");
    PayloadDto payloadDto = JwtUtils.getDefaultPayloadDto(account);
    String token = JwtUtils.generateKey(payloadDto);
  }
}
