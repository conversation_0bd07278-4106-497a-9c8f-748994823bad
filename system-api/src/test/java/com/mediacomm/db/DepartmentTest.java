package com.mediacomm.db;

import com.mediacomm.entity.dao.Department;
import com.mediacomm.system.service.DepartmentService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * .
 *
 * @author: <PERSON><PERSON>e<PERSON><PERSON>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
//在开发环境中执行单元测试时，使用dev配置文件
//@ActiveProfiles("dev")
public class DepartmentTest {
  @Autowired
  DepartmentService departmentService;

  @Test
  public void testGetOne() {
    Department department = departmentService.getById(1);
    Assert.assertNotNull(department);
  }

  @Test
  public void testSaveOne() {
    Department department = new Department();
    department.setDepartmentName("Test部门");
    department.setDepartmentDesc("测试项目");
    departmentService.saveDepartment(department);
  }
}
