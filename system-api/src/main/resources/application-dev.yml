spring:
  data:
    redis:
      host: **************
      port: 6379
      database: 0
      #password: 123456
      client-type: lettuce
      lettuce:
        pool:
          enabled: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *********************************************************************
    #kingbase8
    #username: skylink
    #password: 123456
    #url: ********************************************************************
    #driver-class-name: com.kingbase8.Driver
    druid:
      initial-size: 10 # 初始连接池大小
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      validation-query-timeout: 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      filter:
        slf4j:
          enabled: true
          result-set-log-enabled: true
          connection-log-enabled: false
          data-source-log-enabled: false
          statement-executable-sql-log-enable: true
          statement-log-enabled: true
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: 123456
    listener:
      simple:
        acknowledge-mode: auto # 自定确定
        prefetch: 1 # 消费者端每次拉去的消息数
        default-requeue-rejected: false # 消费被拒绝时,true为重回队列
        retry:
          enabled: false # 是否支持重试
          #max-attempts: 3 # 重试最大次数
          #max-interval: 1000ms # 重试最大间隔时间
  mqtt:
    url: tcp://**************:1883
    client:
      id: stock-producer
    default:
      topic: stock-topic
    completionTimeout: 3000
    Qos: 2
    userName: admin
    password: 123456
  cloud:
    nacos:
      server-addr: **************
      discovery:
        server-addr: **************
        service: ${spring.application.name}
        enabled: true
        instance-enabled: true
  flyway:
    enabled: false

# 用来指定调试是备份数据库的目标
DATABASE_PORT: 3306
DATABASE_HOST: **************
