spring:
  application:
    name: WEB_SERVER
  profiles:
    active: prod
  system:
    config:
      company: "MediaComm美凯"
      sysName: "可视化集控平台"
      title: "可视化集控平台"
      copyright: "Copyright © 2023 MediaComm. All rights reserved."
  thread:
    core: 10 # 核心线程数
    seconds: 5 # 除核心线程外线程存活时间
    prefix: ${spring.application.name} # 线程名称前缀
  mvc:
    throw-exception-if-no-handler-found: true #出现错误时, 直接抛出异常
  web:
    resources:
      add-mappings: false #不要为工程中的资源文件建立映射
  servlet:
    multipart:
      max-file-size: 500MB # 上传文件的最大大小
      max-request-size: 500MB
  jackson:
    deserialization:
      fail-on-unknown-properties: false
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启sql日志
      map-underscore-to-camel-case: true # 带下划线的表字段映射成驼峰实体属性
springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    path: /v3/api-docs

server:
  port: 8181
  udp-broadcast-ip: ***************


