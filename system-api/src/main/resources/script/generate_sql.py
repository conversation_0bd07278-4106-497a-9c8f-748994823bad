import json
import os

# --- 配置 ---
JSON_FILE_PATH = 'china_administrative_divisions.json'
SQL_OUTPUT_PATH = 'china_administrative_divisions.sql'
TABLE_NAME = 'china_administrative_divisions'
DATABASE_NAME = 'skylink'  # 指定要使用的数据库，如果不需要则设为 None
# --- 配置结束 ---

def determine_level(code: str) -> int:
    """
    根据行政区划代码判断层级。
    - xx0000: 省级 (Level 1)
    - xxxx00: 市级 (Level 2)
    - xxxxxx: 区/县级 (Level 3)
    """
    if code.endswith('0000'):
        return 1
    elif code.endswith('00'):
        return 2
    else:
        return 3

def parse_divisions(nodes: list, parent_code: str | None, all_records: list):
    """
    递归解析行政区划数据。
    :param nodes: 当前层级的节点列表 (list of dicts)。
    :param parent_code: 父级节点的代码。
    :param all_records: 用于收集所有记录的列表。
    """
    for node in nodes:
        code = node.get('code')
        name = node.get('name')

        if not code or not name:
            continue

        level = determine_level(code)

        # 为SQL语句中的字符串值转义单引号
        escaped_name = name.replace("'", "''")

        record = {
            'code': code,
            'name': escaped_name,
            'parent_code': parent_code,
            'level': level
        }
        all_records.append(record)

        if 'children' in node and node['children']:
            parse_divisions(node['children'], code, all_records)

def generate_sql_from_json():
    """
    主函数，读取JSON文件并生成SQL插入语句。
    """
    if not os.path.exists(JSON_FILE_PATH):
        print(f"错误: JSON文件未找到 -> '{JSON_FILE_PATH}'")
        return

    print(f"正在从 '{JSON_FILE_PATH}' 读取数据...")
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式无效 -> {e}")
        return
    except Exception as e:
        print(f"读取文件时发生未知错误: {e}")
        return

    all_records = []
    parse_divisions(data, None, all_records)

    if not all_records:
        print("警告: 未从JSON文件中解析出任何数据。")
        return

    print(f"成功解析 {len(all_records)} 条行政区划数据。")
    print(f"正在生成SQL文件 -> '{SQL_OUTPUT_PATH}'...")

    try:
        with open(SQL_OUTPUT_PATH, 'w', encoding='utf-8') as f:
            # -- 添加 USE 数据库语句 --
            if DATABASE_NAME:
                f.write(f"USE `{DATABASE_NAME}`;\n\n")

            f.write(f"-- Auto-generated SQL from {JSON_FILE_PATH}\n")
            f.write(f"INSERT INTO `{TABLE_NAME}` (`code`, `name`, `parent_code`, `level`) VALUES\n")

            value_clauses = []
            for record in all_records:
                # 处理 parent_code 可能为 None 的情况
                parent_code_sql = f"'{record['parent_code']}'" if record['parent_code'] is not None else "NULL"

                clause = (
                    f"('{record['code']}', '{record['name']}', "
                    f"{parent_code_sql}, {record['level']})"
                )
                value_clauses.append(clause)

            # 将所有 VALUES 子句用逗号连接，并在最后添加分号
            f.write(',\n'.join(value_clauses))
            f.write(';\n')

        print(f"SQL文件生成成功: '{SQL_OUTPUT_PATH}'")

    except IOError as e:
        print(f"写入SQL文件时出错: {e}")


if __name__ == '__main__':
    generate_sql_from_json()