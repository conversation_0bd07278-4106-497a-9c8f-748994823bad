# 中国行政区划数据 SQL 生成器

本项目包含一个 Python 脚本 (`china_administrative_divisions_json_to_sql.py`)，用于解析特定格式的中国行政区划 JSON 数据，并将其转换为可直接导入到 MySQL 数据库中的 SQL `INSERT` 语句。

## 功能特性

- **数据转换**: 将层级化的 JSON 数据扁平化，并生成 SQL `INSERT` 语句。
- **自动层级判断**: 根据行政区划代码 (`code`) 自动判断省(1)、市(2)、区/县(3)三个层级。
- **自动关联父级**: 通过 JSON 的嵌套关系，自动为每个行政区划设置 `parent_code`。
- **高可配置性**: 可以在脚本顶部轻松修改数据库名、表名、输入和输出文件名。

## 数据来源

本脚本使用的数据来源于开源项目 **[uiwjs/province-city-china](https://github.com/uiwjs/province-city-china)**。

具体使用的是该仓库中的 `china_administrative_divisions.json` 文件，该文件包含了**省、市、区/县**三级行政区划数据。

## 环境要求

- Python 3.6 或更高版本

## 数据库表结构

生成的 SQL 语句旨在将数据插入到具有以下结构的表中。请在导入数据前确保已在数据库中创建此表。

```sql
CREATE TABLE `china_administrative_divisions`
(
    `code`        VARCHAR(12) NOT NULL COMMENT '行政区划代码 (GB/T 2260)',
    `name`        VARCHAR(50) NOT NULL COMMENT '名称',
    `parent_code` VARCHAR(12) COMMENT '父级代码',
    `level`       TINYINT COMMENT '层级 (1:省/直辖市, 2:市, 3:区/县)',
    PRIMARY KEY (`code`),
    INDEX `idx_name` (`name`),
    INDEX `idx_parent_code` (`parent_code`),
    INDEX `idx_level` (`level`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT ='中国行政区划表';
```

## 使用说明

请按照以下步骤操作，以生成并导入数据：

### 1. 准备文件

-   将 `generate_sql.py` 脚本保存到您的项目目录中。
-   从 [uiwjs/province-city-china 仓库](https://github.com/uiwjs/province-city-china/blob/master/data/china_administrative_divisions.json)下载 `china_administrative_divisions.json` 文件。
-   **确保 `china_administrative_divisions.json` 文件与 `generate_sql.py` 脚本位于同一个目录下。**

目录结构应如下所示：

```
.
├── generate_sql.py
└── china_administrative_divisions.json
```

### 2. 执行脚本

打开终端或命令行，导航到文件所在的目录，然后运行 Python 脚本：

```bash
python generate_sql.py
```

### 3. 获取结果

脚本执行成功后，会在同一目录下生成一个名为 `insert_divisions.sql` 的新文件。这个文件包含了所有行政区划数据的 `INSERT` 语句。

### 4. 导入数据库

使用您喜欢的 MySQL 客户端工具（如 `mysql` 命令行、Navicat、DataGrip、DBeaver 等）连接到您的数据库，然后执行 `insert_divisions.sql` 文件。

如果您使用 `mysql` 命令行工具，可以使用以下命令：

```bash
# 语法: mysql -u [用户名] -p[密码] [数据库名] < [sql文件名]
mysql -u your_username -pyour_password your_database_name < insert_divisions.sql
```
> **提示**: 如果密码中包含特殊字符，建议使用 `-p` 参数后直接回车，再输入密码。
