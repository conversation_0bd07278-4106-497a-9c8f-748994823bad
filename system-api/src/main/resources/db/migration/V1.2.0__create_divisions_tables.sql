USE `skylink` ;

CREATE TABLE IF NOT EXISTS `skylink`.`china_administrative_divisions`
(
    `code`        VARCHAR(12) NOT NULL COMMENT '行政区划代码 (GB/T 2260)',
    `name`        VARCHAR(50) NOT NULL COMMENT '名称',
    `parent_code` VARCHAR(12) COMMENT '父级代码',
    `level`       TINYINT COMMENT '层级 (1:省/直辖市, 2:市, 3:区/县)',
    PRIMARY KEY (`code`),
    INDEX `idx_name` (`name`),
    INDEX `idx_parent_code` (`parent_code`),
    INDEX `idx_level` (`level`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT ='中国行政区划表';

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (3001, 'GB_GATEWAY', '[]', 'MediaComm', 10, '国标网关', '[]', 'KVM', '[]');

-- -----------------------------------------------------
-- Add GB Gateway IPC Device Model
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (3101, 'GB_GATEWAY_IPC', '[]', 'MediaComm', 10, '国标网关摄像头', '[]', 'PER_EQUIPMENT_OTHER', '[]');
