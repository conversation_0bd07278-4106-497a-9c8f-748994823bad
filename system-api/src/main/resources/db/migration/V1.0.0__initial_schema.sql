-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- Thu Mar 23 11:09:15 2023
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- -----------------------------------------------------
-- Schema skylink
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema skylink
-- CREATE SCHEMA IF NOT EXISTS `skylink` DEFAULT CHARACTER SET utf8 ;
-- -----------------------------------------------------
USE `skylink` ;

-- -----------------------------------------------------
-- Table `skylink`.`department`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`department` (
  `department_id` INT NOT NULL AUTO_INCREMENT COMMENT '部门id，唯一',
  `department_desc` VARCHAR(32) DEFAULT '' COMMENT '部门描述',
  `department_name` VARCHAR(32) NOT NULL DEFAULT '' UNIQUE COMMENT '部门名称',
  PRIMARY KEY (`department_id`),
  UNIQUE INDEX `department_id_UNIQUE` (`department_id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`personnel`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`personnel` (
  `personnel_id` INT NOT NULL AUTO_INCREMENT COMMENT '人员Id',
  `job_number` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '工号',
  `personnel_create_time` BIGINT UNSIGNED COMMENT '人员创建时间',
  `personnel_desc` VARCHAR(32) DEFAULT '' COMMENT '人员描述',
  `personnel_enable` TINYINT NOT NULL DEFAULT 0 COMMENT '是否启动',
  `personnel_mail` VARCHAR(32) DEFAULT '' COMMENT '邮箱',
  `personnel_name` VARCHAR(32) NOT NULL COMMENT '人员名称',
  `personnel_tel` VARCHAR(16) NULL COMMENT '手机号',
  `department` INT NULL COMMENT '所属部门Id',
  PRIMARY KEY (`personnel_id`, `personnel_name`),
  UNIQUE INDEX `personnel_id_UNIQUE` (`personnel_id` ASC) INVISIBLE,
  INDEX `department_idx` (`department` ASC) VISIBLE,
  UNIQUE INDEX `personnel_name_UNIQUE` (`personnel_name` ASC) VISIBLE,
  CONSTRAINT `department`
    FOREIGN KEY (`department`)
    REFERENCES `skylink`.`department` (`department_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`account` (
  `account_create_time` BIGINT UNSIGNED COMMENT '账号创建时间',
  `account_creator` VARCHAR(32) DEFAULT '' COMMENT '账号创建者',
  `account_desc` VARCHAR(32) DEFAULT '' COMMENT '账号描述',
  `account_enable` TINYINT NOT NULL DEFAULT 0 COMMENT '是否启动',
  `account_home_page` TINYINT NOT NULL DEFAULT 41 COMMENT '账号主页',
  `account_name` VARCHAR(32) NOT NULL COMMENT '账号',
  `account_password` VARCHAR(32) NOT NULL COMMENT '账号密码',
  `business_auth` JSON NOT NULL COMMENT '业务权限',
  `account_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '账号Id',
  `personnel` INT NULL COMMENT '关联人员Id',
  PRIMARY KEY (`account_id`),
  UNIQUE INDEX `account_id_UNIQUE` (`account_id` ASC) VISIBLE,
  UNIQUE INDEX `account_name_UNIQUE` (`account_name` ASC) VISIBLE,
  INDEX `fk_account_personnel1_idx` (`personnel` ASC) VISIBLE,
  CONSTRAINT `personnel`
    FOREIGN KEY (`personnel`)
    REFERENCES `skylink`.`personnel` (`personnel_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`operate_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`operate_log` (
  `operator` VARCHAR(32) NOT NULL COMMENT '操作人员名称',
  `title` VARCHAR(15) NOT NULL COMMENT '操作标题',
  `type` ENUM('DELETE', 'SELECT', 'UPDATE', 'INSERT', 'OTHER') NOT NULL  DEFAULT 'OTHER' COMMENT '操作类型',
  `uri` VARCHAR(256) NOT NULL COMMENT 'uri',
  `operate_time` BIGINT UNSIGNED COMMENT '操作时间',
  `operator_ip` VARCHAR(15) NOT NULL COMMENT '操作端IP',
  `operate_body` JSON NULL COMMENT '操作消息体（输出、结果代码、错误码、事务ID、应用/组件/服务/对象/方法、应用版本信息）-JSON',
  PRIMARY KEY (`operate_time`))
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`position_level`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`position_level` (
  `level_name` ENUM('SEAT_GROUP', 'MEETING_ROOM', 'COMMAND_HALL', 'SUPPORTING_ROOM', 'OFFICE') NOT NULL DEFAULT 'MEETING_ROOM' COMMENT '\'SEAT_GROUP\' - 坐席组, \'MEETING_ROOM\' - 会议室 , \'COMMAND_HALL\' - 指挥大厅, \'SUPPORTING_ROOM\' - 配套机房, \'OFFICE\' - 办公室',
  `level_title` VARCHAR(15) NOT NULL DEFAULT '会议室' COMMENT '对应级别枚举值的名称',
  `meeting` TINYINT NULL DEFAULT 1 COMMENT '是否可用于会议',
  `kvm` TINYINT NULL DEFAULT 1 COMMENT '是否可用于kvm',
  PRIMARY KEY (`level_name`),
  UNIQUE INDEX `level_id_UNIQUE` (`level_name` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`position_group`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`position_group` (
  `position_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '房间Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '房间名称',
  `parent_id` INT NOT NULL DEFAULT 0 COMMENT '父级Id',
  `seq_in_parent` INT NULL COMMENT '父级中的顺序',
  `position_level` ENUM('SEAT_GROUP', 'MEETING_ROOM', 'COMMAND_HALL', 'SUPPORTING_ROOM', 'OFFICE') NOT NULL COMMENT '房间等级',
  `max_meet_personnel` INT NOT NULL DEFAULT 20 COMMENT '最大人数',
  `start_use_time` BIGINT UNSIGNED NULL COMMENT '启用时间',
  `finish_use_time` BIGINT UNSIGNED NULL COMMENT '结束使用时间',
  PRIMARY KEY (`position_id`),
  UNIQUE INDEX `position_id_UNIQUE` (`position_id` ASC) VISIBLE,
  INDEX `position_level_idx` (`position_level` ASC) VISIBLE,
  CONSTRAINT `position_level`
    FOREIGN KEY (`position_level`)
    REFERENCES `skylink`.`position_level` (`level_name`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`device_model`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`device_model` (
  `model_id` INT UNSIGNED AUTO_INCREMENT NOT NULL,
  `device_type` VARCHAR(32) NULL,
  `model_name` VARCHAR(32) NULL,
  `manufacturer` VARCHAR(15) NULL,
  `max_use_age` INT NULL DEFAULT 10,
  `model_desc` VARCHAR(32) NULL DEFAULT '',
  `properties` JSON NOT NULL COMMENT '配置项的key-value',
  `ext_properties` JSON NOT NULL COMMENT '型号主机表达的可配置属性',
  `ext_option` JSON NOT NULL COMMENT '型号扩展的可配置项',
  `sub_system` ENUM('SELF_DIAGNOSIS', 'KVM', 'CENTER_CONTROL', 'SWITCH_DEVICE', 'BUSINESS_TERMINAL', 'PER_EQUIPMENT_OTHER', 'PER_EQUIPMENT_TX', 'PER_EQUIPMENT_RX', 'VISUALIZATION_TERMINAL', 'INTERNAL_BOARD', 'VIDEO_WALL') NOT NULL DEFAULT 'PER_EQUIPMENT_OTHER',
  PRIMARY KEY (`model_id`),
  UNIQUE INDEX `model_id_UNIQUE` (`model_id` ASC) VISIBLE,
  UNIQUE INDEX `device_type_UNIQUE` (`device_type` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`visualization_scene`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`visualization_scene` (
  `scene_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '可视化场景Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '场景名称',
  `device_model` INT UNSIGNED NULL COMMENT '场景对应的设备类型',
  `room_id` INT UNSIGNED NULL COMMENT '所属房间Id',
  `data` JSON NOT NULL COMMENT '场景内的页面数据',
  `scene_type` ENUM('GUIDE_PAGE', 'OPERATION_PAGE') NOT NULL DEFAULT 'OPERATION_PAGE' COMMENT '场景用途',
  `config_time` BIGINT UNSIGNED NULL COMMENT '更新时间',
  PRIMARY KEY (`scene_id`),
  UNIQUE INDEX `scene_id_UNIQUE` (`scene_id` ASC) VISIBLE,
  INDEX `room_id_idx` (`room_id` ASC) VISIBLE,
  INDEX `fk_visualization_scene_device_model1_idx` (`device_model` ASC) VISIBLE,
  CONSTRAINT `visualization_scene_device_model`
    FOREIGN KEY (`device_model`)
    REFERENCES `skylink`.`device_model` (`model_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`kvm_asset_group`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_asset_group` (
  `asset_group_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'tx分组Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '分组名称',
  `room_id` INT UNSIGNED NULL COMMENT '所属房间Id',
  `group_type` ENUM('TX_GROUP', 'RX_GROUP') NOT NULL DEFAULT 'TX_GROUP',
  `background_color` VARCHAR(9) NULL DEFAULT '' COMMENT 'RGBA',
  `typeface_color` VARCHAR(9) NULL DEFAULT '' COMMENT 'RGBA',
  `parent_group_id` INT UNSIGNED NULL COMMENT '父分组Id',
  `seq` INT UNSIGNED NULL DEFAULT 10 COMMENT '分组内子分组排序',
  PRIMARY KEY (`asset_group_id`),
  UNIQUE INDEX `asset_group_id_UNIQUE` (`asset_group_id` ASC) VISIBLE,
  INDEX `room_id_idx` (`room_id` ASC) VISIBLE,
  CONSTRAINT `room_id_x1`
    FOREIGN KEY (`room_id`)
    REFERENCES `skylink`.`position_group` (`position_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`asset_group_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`asset_group_merge` (
  `aid` VARCHAR(33) NOT NULL COMMENT '外设Id',
  `gid` INT UNSIGNED NOT NULL COMMENT '外设分组Id',
  `seq_in_group` INT UNSIGNED NOT NULL DEFAULT 10 COMMENT '分组内外设序号',
  INDEX `asset_group_aid_UNIQUE` (`aid` ASC) VISIBLE,
  INDEX `asset_group_gid_UNIQUE` (`gid` ASC) VISIBLE,
  CONSTRAINT `asset_group_aid`
   FOREIGN KEY (`aid`)
     REFERENCES `skylink`.`kvm_asset` (`asset_id`)
     ON DELETE CASCADE
     ON UPDATE NO ACTION,
  CONSTRAINT `asset_group_gid`
   FOREIGN KEY (`gid`)
     REFERENCES `skylink`.`kvm_asset_group` (`asset_group_id`)
     ON DELETE CASCADE
     ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`device_group`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`device_group` (
  `group_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设备分组Id',
  `group_seq` INT NOT NULL DEFAULT 10 COMMENT '分组序号',
  `group_name` VARCHAR(32) NOT NULL COMMENT '分组名称',
  `icon_size` INT NOT NULL DEFAULT 3 COMMENT '显示图标大小',
  `col_count` INT NOT NULL DEFAULT 10 COMMENT '列数',
  PRIMARY KEY (`group_id`),
  UNIQUE INDEX `group_id_UNIQUE` (`group_id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`kvm_master`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_master` (
  `master_id` VARCHAR(33) NOT NULL COMMENT '主机Id',
  `alias` VARCHAR(32) NULL DEFAULT '' COMMENT '别名',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
  `collector_properties` JSON NOT NULL COMMENT '采集扩展属性',
  `device_group` INT UNSIGNED NULL COMMENT '所属设备分组',
  `device_ip` VARCHAR(15) NOT NULL COMMENT '设备ip',
  `device_model` INT UNSIGNED NULL COMMENT '设备类型Id',
  `properties` JSON NOT NULL COMMENT '设备扩展属性',
  `version` VARCHAR(15) NULL DEFAULT '',
  PRIMARY KEY (`master_id`),
  UNIQUE INDEX `master_id_UNIQUE` (`master_id` ASC) VISIBLE,
  INDEX `device_group_idx` (`device_group` ASC) VISIBLE,
  INDEX `device_model_idx` (`device_model` ASC) VISIBLE,
  UNIQUE INDEX `name_UNIQUE` (`name` ASC) VISIBLE,
  CONSTRAINT `device_group`
    FOREIGN KEY (`device_group`)
    REFERENCES `skylink`.`device_group` (`group_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `device_model`
    FOREIGN KEY (`device_model`)
    REFERENCES `skylink`.`device_model` (`model_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`kvm_asset`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_asset` (
  `asset_id` VARCHAR(33) NOT NULL COMMENT '设备Id',
  `alias` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '别名',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
  `collector_properties` JSON NOT NULL COMMENT '采集扩展属性',
  `device_ip` VARCHAR(15) NULL COMMENT '设备ip',
  `device_model` INT UNSIGNED NULL COMMENT '设备类型Id',
  `properties` JSON NOT NULL COMMENT '设备扩展属性',
  `version` VARCHAR(32) NOT NULL DEFAULT '',
  `hardcode` VARCHAR(128) NULL COMMENT '设备硬件编码',
  `master_id` VARCHAR(33) NULL COMMENT '所属主机Id',
  `device_id` INT NOT NULL COMMENT '对应接口中设备的实际id',
  PRIMARY KEY (`asset_id`, `device_id`),
  UNIQUE INDEX `asset_id_UNIQUE` (`asset_id` ASC) VISIBLE,
  UNIQUE INDEX `asset_hardcode_UNIQUE` (`hardcode` ASC) VISIBLE,
  INDEX `master_id_idx` (`master_id` ASC) VISIBLE,
  INDEX `fk_asset_device_model1_idx` (`device_model` ASC) VISIBLE,
  CONSTRAINT `master_id`
    FOREIGN KEY (`master_id`)
      REFERENCES `skylink`.`kvm_master` (`master_id`)
      ON DELETE CASCADE -- 主机被删除时同时删除外设信息
      ON UPDATE NO ACTION,
  CONSTRAINT `device_model_x1`
    FOREIGN KEY (`device_model`)
      REFERENCES `skylink`.`device_model` (`model_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`kvm_slot`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_slot` (
  `slot_id` VARCHAR(33) NOT NULL COMMENT '设备Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
  `collector_properties` JSON NOT NULL COMMENT '采集扩展属性',
  `device_model` INT UNSIGNED NULL COMMENT '设备类型Id',
  `properties` JSON NOT NULL COMMENT '设备扩展属性',
  `version` VARCHAR(15) NOT NULL DEFAULT '',
  `master_id` VARCHAR(33) NULL COMMENT '所属主机Id',
  `device_id` INT NOT NULL COMMENT '对应接口中设备的实际id',
  PRIMARY KEY (`slot_id`, `device_id`),
  UNIQUE INDEX `slot_id_UNIQUE` (`slot_id` ASC) VISIBLE,
  INDEX `master_id_idx` (`master_id` ASC) VISIBLE,
  INDEX `kvm_slot_device_model1_idx` (`device_model` ASC) VISIBLE,
  CONSTRAINT `kvm_slot_master_id`
    FOREIGN KEY (`master_id`)
      REFERENCES `skylink`.`kvm_master` (`master_id`)
      ON DELETE CASCADE -- 主机被删除时同时删除外设信息
      ON UPDATE NO ACTION,
  CONSTRAINT `kvm_slot_device_model_x1`
    FOREIGN KEY (`device_model`)
      REFERENCES `skylink`.`device_model` (`model_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION)
  ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`env_device`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`env_device` (
  `id` VARCHAR(33) NOT NULL COMMENT '设备Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
  `device_ip` VARCHAR(15) NULL COMMENT '设备ip',
  `version` VARCHAR(32) NULL COMMENT '设备版本',
  `device_model` INT UNSIGNED NULL COMMENT '设备类型Id',
  `hardcode` VARCHAR(64) NULL COMMENT '设备硬件编码',
  `properties` JSON NOT NULL COMMENT '设备扩展属性',
  `collector_properties` JSON NOT NULL COMMENT '采集扩展属性',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `env_id_UNIQUE` (`id` ASC) VISIBLE,
  UNIQUE INDEX `env_hardcode_UNIQUE` (`hardcode` ASC) VISIBLE,
  CONSTRAINT `env_device_model_x1`
    FOREIGN KEY (`device_model`)
      REFERENCES `skylink`.`device_model` (`model_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`env_kvm_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`net_link_merge` (
  `net_id` VARCHAR(33) NOT NULL COMMENT '交换机Id',
  `asso_device_name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '对端设备名称',
  `net_port` INT NOT NULL COMMENT '交换机连接端口',
  INDEX `net_id_UNIQUE` (`net_id` ASC) VISIBLE,
  UNIQUE INDEX `net_id_and_net_port_UNIQUE` (`net_port`, `net_id` ASC) VISIBLE,
  CONSTRAINT `net_link_merge_net_id`
    FOREIGN KEY (`net_id`)
    REFERENCES `skylink`.`env_device` (`id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`visualization_device`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`visualization_device` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设备Id',
  `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '名称',
  `device_ip` VARCHAR(15) NULL COMMENT '设备ip',
  `version` VARCHAR(15) NULL COMMENT '程序版本',
  `device_model` INT UNSIGNED NULL COMMENT '设备类型Id',
  `hardcode` VARCHAR(64) NOT NULL COMMENT '设备硬件编码',
  `room_id` INT UNSIGNED NULL COMMENT '所属房间Id',
  `default_scene` INT UNSIGNED NULL COMMENT '默认场景Id',
  `properties` JSON NOT NULL COMMENT '设备扩展属性',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `env_id_UNIQUE` (`id` ASC) VISIBLE,
  UNIQUE INDEX `env_hardcode_UNIQUE` (`hardcode` ASC) VISIBLE,
  CONSTRAINT `visualization_device_default_scene`
    FOREIGN KEY (`default_scene`)
      REFERENCES `skylink`.`visualization_scene` (`scene_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION,
  CONSTRAINT `visualization_device_position_id`
    FOREIGN KEY (`room_id`)
      REFERENCES `skylink`.`position_group` (`position_id`)
      ON DELETE CASCADE
      ON UPDATE NO ACTION,
  CONSTRAINT `visualization_device_model_x1`
    FOREIGN KEY (`device_model`)
      REFERENCES `skylink`.`device_model` (`model_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`kvm_switch_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_switch_log` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志Id',
  `master_id` VARCHAR(33) NOT NULL COMMENT '对应设备Id',
  `con` VARCHAR(32) NULL COMMENT 'rx端名称',
  `cpu` VARCHAR(32) NULL COMMENT 'tx端名称',
  `switch_time` BIGINT UNSIGNED COMMENT '切换时间',
  `from_user` VARCHAR(32) NULL COMMENT '对应主机内用户名称',
  `switch_mode` ENUM('VIDEO_ONLY', 'STANDARD', 'PRIVATE', 'UNKNOWN') NOT NULL DEFAULT 'UNKNOWN' COMMENT '切换模式',
  `disconnect` TINYINT NULL COMMENT '连接或断开',
  PRIMARY KEY (`id`),
  CONSTRAINT `master_id_x1`
    FOREIGN KEY (`master_id`)
      REFERENCES `skylink`.`kvm_master` (`master_id`)
      ON DELETE CASCADE
      ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`kvm_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_user` (
  `user_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户Id',
  `index_in_device` INT NOT NULL COMMENT 'kvm主机内用户Id',
  `master_id` VARCHAR(33) NULL COMMENT '对应主机Id',
  `user_name` VARCHAR(32) NULL COMMENT '用户名称',
  `user_level` VARCHAR(45) NULL COMMENT '用户全称',
  `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `active` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已激活',
  PRIMARY KEY (`user_id`),
  INDEX `kvm_user_master_id_idx` (`master_id` ASC) VISIBLE,
  CONSTRAINT `kvm_user_master_id`
    FOREIGN KEY (`master_id`)
    REFERENCES `skylink`.`kvm_master` (`master_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`kvm_seat`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_seat` (
  `seat_id` INT NOT NULL AUTO_INCREMENT COMMENT '坐席Id',
  `master_id` VARCHAR(33) NULL COMMENT '所属主机id',
  `device_id` INT NULL COMMENT '对应接口中坐席的实际Id',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '坐席名称',
  `opt_model_enable` TINYINT NULL DEFAULT 0 COMMENT '是否启动操作模式',
  `row_count` INT NULL COMMENT '行数',
  `col_count` INT NULL COMMENT '列数',
  `position_id` INT UNSIGNED NULL COMMENT '所属房间内坐席分组Id',
  `seq_in_position` INT NOT NULL COMMENT '分组中序号',
  `decoders` JSON NOT NULL COMMENT '屏幕信息',
  PRIMARY KEY (`seat_id`),
  UNIQUE INDEX `seat_id_UNIQUE` (`seat_id` ASC) VISIBLE,
  INDEX `master_id_idx` (`master_id` ASC) VISIBLE,
  INDEX `fk_kvm_seat_position_group1_idx` (`position_id` ASC) VISIBLE,
  CONSTRAINT `master_id_x2`
    FOREIGN KEY (`master_id`)
    REFERENCES `skylink`.`kvm_master` (`master_id`)
      ON DELETE CASCADE
    ON UPDATE NO ACTION,
  CONSTRAINT `position_id`
    FOREIGN KEY (`position_id`)
    REFERENCES `skylink`.`position_group` (`position_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`kvm_video_wall`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_video_wall` (
  `wall_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '电视墙Id',
  `master_id` VARCHAR(33) NOT NULL COMMENT '所属主机Id',
  `device_id` INT NOT NULL COMMENT '主机内部的Id',
  `room_id` INT UNSIGNED NULL COMMENT '所属房间的Id',
  `name` VARCHAR(33) NULL COMMENT '电视墙名称',
  `seq_in_room` INT NULL COMMENT '房间内序号',
  `row_count` INT NULL COMMENT '行数',
  `col_count` INT NULL COMMENT '列数',
  `single_w` INT NULL COMMENT '分辨率宽',
  `single_h` INT NULL COMMENT '分辨率高',
  `unique_search_key` VARCHAR(128) NOT NULL COMMENT '唯一检索电视墙的关键字',
  `decoders` JSON NOT NULL COMMENT '屏幕信息',
  `polling_scenes_enable` TINYINT NULL DEFAULT 0 COMMENT '轮询预案',
  `audio_control` TINYINT NULL DEFAULT 0 COMMENT '音频控制',
  `banner_type` ENUM('NON_SUPPORT', 'BANNER_KAITO','BANNER_VP7') NOT NULL DEFAULT 'NON_SUPPORT' COMMENT '横幅类型',
  `device_model` INT UNSIGNED NULL COMMENT '电视墙对应的设备类型',
  `collector_properties` JSON NOT NULL COMMENT '来自型号的扩展属性',
  `polling_interval_time` INT NULL DEFAULT 10 COMMENT '轮询预案时间间隔',
  `properties` JSON NOT NULL,
  PRIMARY KEY (`wall_id`),
  UNIQUE INDEX `wall_id_UNIQUE` (`wall_id` ASC) VISIBLE,
  UNIQUE INDEX `kvm_video_wall_unique_search_key` (`unique_search_key` ASC) VISIBLE,
  INDEX `master_id_idx` (`master_id` ASC) VISIBLE,
  INDEX `fk_kvm_video_wall_position_group1_idx` (`room_id` ASC) VISIBLE,
  CONSTRAINT `master_id_x3`
    FOREIGN KEY (`master_id`)
    REFERENCES `skylink`.`kvm_master` (`master_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION,
  CONSTRAINT `kvm_video_wall_device_model_x1`
    FOREIGN KEY (`device_model`)
      REFERENCES `skylink`.`device_model` (`model_id`)
      ON DELETE NO ACTION
      ON UPDATE NO ACTION,
  CONSTRAINT `room_id_x3`
    FOREIGN KEY (`room_id`)
    REFERENCES `skylink`.`position_group` (`position_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`visualization_layout`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`visualization_layout` (
  `layout_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '布局Id',
  `seq` INT NULL COMMENT '布局序号',
  `layout_type` ENUM('VIDEO_WALL_LAYOUT', 'SEAT_LAYOUT', 'GENERAL_LAYOUT') NOT NULL DEFAULT 'VIDEO_WALL_LAYOUT' COMMENT '布局类型',
  `name` VARCHAR(32) NULL COMMENT '布局名称',
  `layout_data` JSON NOT NULL COMMENT '布局数据',
  PRIMARY KEY (`layout_id`),
  UNIQUE INDEX `layout_id_UNIQUE` (`layout_id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`wall_layout_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`wall_layout_merge` (
  `wid` INT UNSIGNED NOT NULL COMMENT '电视墙Id',
  `lid` INT UNSIGNED NOT NULL COMMENT '布局Id',
  INDEX `wall_layout_wid_UNIQUE` (`wid` ASC) VISIBLE,
  INDEX `wall_layout_lid_UNIQUE` (`lid` ASC) VISIBLE,
  CONSTRAINT `wall_layout_wid`
    FOREIGN KEY (`wid`)
    REFERENCES `skylink`.`kvm_video_wall` (`wall_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION,
  CONSTRAINT `wall_layout_lid`
    FOREIGN KEY (`lid`)
    REFERENCES `skylink`.`visualization_layout` (`layout_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`seat_layout_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`seat_layout_merge` (
  `sid` INT NOT NULL COMMENT '坐席Id',
  `lid` INT UNSIGNED NOT NULL COMMENT '布局Id',
  INDEX `wall_layout_wid_UNIQUE` (`sid` ASC) VISIBLE,
  INDEX `wall_layout_lid_UNIQUE` (`lid` ASC) VISIBLE,
  CONSTRAINT `seat_layout_sid`
   FOREIGN KEY (`sid`)
     REFERENCES `skylink`.`kvm_seat` (`seat_id`)
     ON DELETE CASCADE
     ON UPDATE NO ACTION,
  CONSTRAINT `seat_layout_lid`
   FOREIGN KEY (`lid`)
     REFERENCES `skylink`.`visualization_layout` (`layout_id`)
     ON DELETE CASCADE
     ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`video_wall_scene`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`video_wall_scene` (
  `scene_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '预案Id',
  `polling_scenes_enable` TINYINT NULL DEFAULT 0 COMMENT '启动轮询预案',
  `name` VARCHAR(32) NULL,
  `wall_id` INT UNSIGNED NULL COMMENT '所属电视墙Id',
  `layout_data` JSON NULL COMMENT '布局信息',
  `panel_data` JSON NULL COMMENT '窗口信息',
  PRIMARY KEY (`scene_id`),
  UNIQUE INDEX `id_UNIQUE` (`scene_id` ASC) VISIBLE,
  INDEX `wall_id_idx` (`wall_id` ASC) VISIBLE,
  CONSTRAINT `wall_id`
    FOREIGN KEY (`wall_id`)
    REFERENCES `skylink`.`kvm_video_wall` (`wall_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `skylink`.`alarm`
-- --------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`alarm`(
  `alarm_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '告警Id',
  `master_id` VARCHAR(33) NULL COMMENT '主机Id或者其他设备Id',
  `device_id` VARCHAR(33) NULL COMMENT '外设Id或者其他设备Id',
  `signal_id` VARCHAR(32) NULL COMMENT '信号Id',
  `signal_name` VARCHAR(32) NULL COMMENT '信号名称',
  `device_name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '告警设备名称',
  `ended` TINYINT NULL DEFAULT 0 COMMENT '是否已结束',
  `end_time` BIGINT UNSIGNED NULL COMMENT '结束时间',
  `begin_time` BIGINT UNSIGNED NULL COMMENT '开始时间',
  `end_by_person` VARCHAR(32) NULL COMMENT '手动结束人员名称',
  `sub_system` ENUM('SELF_DIAGNOSIS', 'KVM', 'CENTER_CONTROL', 'SWITCH_DEVICE', 'BUSINESS_TERMINAL', 'PER_EQUIPMENT_OTHER', 'PER_EQUIPMENT_TX', 'PER_EQUIPMENT_RX', 'VISUALIZATION_TERMINAL', 'INTERNAL_BOARD') NOT NULL COMMENT '所属子系统（\'PER_EQUIPMENT\'-外围设备,\'KVM\'-KVM主机）',
  `alarm_level` ENUM('LEVEL_4', 'LEVEL_3', 'LEVEL_2', 'LEVEL_1') NOT NULL DEFAULT 'LEVEL_1' COMMENT '告警级别',
  `alarm_desc` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '告警描述',
  `signal_value` JSON NOT NULL COMMENT '信号量',
  `acked` TINYINT NULL DEFAULT 0 COMMENT '是否已确认',
  `ack_by_person` VARCHAR(32) NULL DEFAULT '' COMMENT '确认人员名称',
  `ack_desc` VARCHAR(32) NULL DEFAULT '' COMMENT '确认描述',
  `ack_time` BIGINT UNSIGNED NULL COMMENT '确认音时间',
  `mute_time` BIGINT UNSIGNED NULL COMMENT '静音时间',
  `mute_by_person` VARCHAR(32) NULL COMMENT '静音人员',
  `suggestion` TEXT NULL,
  PRIMARY KEY (`alarm_id`),
  UNIQUE INDEX `alarm_id_UNIQUE` (`alarm_id` ASC) VISIBLE,
  INDEX `fk_alarm_parent_idx` (`master_id` ASC) VISIBLE,
  INDEX `fk_alarm_asset_idx` (`device_id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`disabled_alarm`
-- --------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`disabled_alarm`(
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '屏蔽记录Id',
  `master_id` VARCHAR(33) NULL COMMENT '主机Id或者其他设备Id',
  `device_id` VARCHAR(33) NULL COMMENT '外设Id或者其他设备Id',
  `signal_id` VARCHAR(32) NULL COMMENT '信号Id',
  `device_name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '告警设备名称',
  `signal_name` VARCHAR(32) NULL COMMENT '信号名称',
  `operate_time` BIGINT UNSIGNED NULL COMMENT '屏蔽操作时间',
  `operator` VARCHAR(32) NULL COMMENT '屏蔽人员名称',
  `disable_desc` VARCHAR(45) NOT NULL DEFAULT '' COMMENT '描述',
  `sub_system` ENUM('SELF_DIAGNOSIS', 'KVM', 'CENTER_CONTROL', 'SWITCH_DEVICE', 'BUSINESS_TERMINAL', 'PER_EQUIPMENT_OTHER', 'PER_EQUIPMENT_TX', 'PER_EQUIPMENT_RX', 'VISUALIZATION_TERMINAL', 'INTERNAL_BOARD') NOT NULL COMMENT '所属子系统（\'PER_EQUIPMENT\'-外围设备,\'KVM\'-KVM主机）',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `disable_alarm_UNIQUE` (`id` ASC) VISIBLE,
  UNIQUE INDEX `master_id_device_id_signal_id_UNIQUE` (`master_id`, `device_id`, `signal_id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`device_model_signal`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`device_model_signal` (
  `device_model_id` INT UNSIGNED NOT NULL COMMENT '设备类型Id',
  `signals` JSON COMMENT '信号量',
  PRIMARY KEY (`device_model_id`),
  UNIQUE INDEX `device_model_id_UNIQUE` (`device_model_id` ASC) VISIBLE,
  CONSTRAINT `device_mode_id`
    FOREIGN KEY (`device_model_id`)
    REFERENCES `skylink`.`device_model` (`model_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`role`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`role` (
  `role_id` INT UNSIGNED AUTO_INCREMENT NOT NULL COMMENT '角色Id',
  `name` VARCHAR(15) NOT NULL DEFAULT '' COMMENT '角色名称',
  PRIMARY KEY (`role_id`),
  UNIQUE INDEX `role_name_UNIQUE` (`name` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`permission`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`permission` (
  `menu_id` INT UNSIGNED NOT NULL COMMENT '权限/菜单 Id' ,
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
  `title` VARCHAR(45) NOT NULL DEFAULT '' COMMENT '描述',
  `rights` JSON NOT NULL COMMENT 'url',
  `pid` INT UNSIGNED DEFAULT 0 COMMENT '父级权限Id',
  PRIMARY KEY (`menu_id`))
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`role_permission_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`role_permission_merge` (
  `rid` INT UNSIGNED NOT NULL COMMENT '角色Id',
  `pid` INT UNSIGNED NOT NULL COMMENT '权限Id',
  INDEX `role_permission_merge_role_idx` (`rid` ASC) VISIBLE,
  INDEX `role_permission_merge_permission_idx` (`pid` ASC) VISIBLE,
  CONSTRAINT `role_permission_rid`
  FOREIGN KEY (`rid`)
  REFERENCES `skylink`.`role` (`role_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
  CONSTRAINT `role_permission_pid`
  FOREIGN KEY (`pid`)
  REFERENCES `skylink`.`permission` (`menu_id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`account_role_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`account_role_merge` (
  `aid` INT UNSIGNED NOT NULL COMMENT '账号Id',
  `rid` INT UNSIGNED NOT NULL COMMENT '角色Id',
  INDEX `fk_account_role_account1_idx` (`aid` ASC) VISIBLE,
  INDEX `rid_idx` (`rid` ASC) VISIBLE,
  CONSTRAINT `account_role_aid`
    FOREIGN KEY (`aid`)
    REFERENCES `skylink`.`account` (`account_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `account_role_rid`
    FOREIGN KEY (`rid`)
    REFERENCES `skylink`.`role` (`role_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`alarm_repair_desc`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`alarm_repair_desc` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '',
  `alarm_id` INT UNSIGNED NOT NULL COMMENT '告警Id',
  `repair_time` BIGINT UNSIGNED NULL COMMENT '修复时间',
  `repair_by_person` VARCHAR(32) NULL COMMENT '修复人员名称',
  `repair_desc` VARCHAR(45) NULL DEFAULT '' COMMENT '修复描述',
  PRIMARY KEY (`id`),
  CONSTRAINT `alarm_repair_desc_alarm_id`
    FOREIGN KEY (`alarm_id`)
    REFERENCES `skylink`.`alarm` (`alarm_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`sync_video_wall`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`sync_wall_group` (
  `id`  INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '同屏分组Id',
  `name` VARCHAR(32) NOT NULL COMMENT '同屏分组名称',
  PRIMARY KEY (`id`))
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`sync_video_wall`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`sync_wall_group_merge` (
  `sid` INT UNSIGNED NOT NULL COMMENT '同屏分组Id',
  `wid` INT UNSIGNED NOT NULL COMMENT '电视墙Id',
  INDEX `sync_wall_group_idx` (`sid` ASC) VISIBLE,
  INDEX `video_wall_idx` (`wid` ASC) VISIBLE,
  CONSTRAINT `sync_wall_group_sid`
    FOREIGN KEY (`sid`)
      REFERENCES `skylink`.`sync_wall_group` (`id`)
      ON DELETE CASCADE
      ON UPDATE NO ACTION,
  CONSTRAINT `video_wall_wid`
    FOREIGN KEY (`wid`)
      REFERENCES `skylink`.`kvm_video_wall` (`wall_id`)
      ON DELETE CASCADE
      ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`encoder_asso`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`encoder_asso` (
   `id` INT UNSIGNED AUTO_INCREMENT NOT NULL COMMENT 'id',
   `port` INT UNSIGNED NOT NULL COMMENT '端口',
   `name` VARCHAR(32) NOT NULL COMMENT '名称',
   `ip` VARCHAR(15) NULL COMMENT '设备ip',
   `rtsp_url` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'url',
   `rx_id` VARCHAR(33) NOT NULL,
   `master_id` VARCHAR(33) NOT NULL,
   PRIMARY KEY (`id`),
   UNIQUE INDEX `encoder_asso_ip_UNIQUE` (`ip` ASC) VISIBLE,
   UNIQUE INDEX `encoder_asso_rtsp_url_UNIQUE` (`rtsp_url` ASC) VISIBLE,
   UNIQUE INDEX `encoder_asso_rx_id_UNIQUE` (`rx_id` ASC) VISIBLE,
   CONSTRAINT `fk_rx_id_x1`
     FOREIGN KEY (`rx_id`)
       REFERENCES `skylink`.`kvm_asset` (`asset_id`)
       ON DELETE CASCADE
       ON UPDATE NO ACTION,
   CONSTRAINT `fk_master_id_x1`
     FOREIGN KEY (`master_id`)
       REFERENCES `skylink`.`kvm_master` (`master_id`)
       ON DELETE CASCADE
       ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`picture`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`picture` (
  `id` BIGINT UNSIGNED NOT NULL COMMENT '图片Hash64',
  `content` MediumBlob COMMENT '图片数据',
  PRIMARY KEY (`id`),
  INDEX `picture_idx` (`id` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`default_picture_merge`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`default_picture` (
   `name` VARCHAR(32) NOT NULL COMMENT '名称',
   `stretch` TINYINT NULL DEFAULT 0 COMMENT '拉伸',
   `pic_hash` BIGINT UNSIGNED COMMENT '图片Hash64',
   PRIMARY KEY (`name`),
   INDEX `default_picture_merge_pic_hash` (`pic_hash` ASC) VISIBLE)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`picture_material`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`picture_material` (
  `id` INT UNSIGNED AUTO_INCREMENT NOT NULL COMMENT '图片或目录Id',
  `name` VARCHAR(32) NOT NULL COMMENT '名称',
  `image_id` BIGINT UNSIGNED COMMENT '图片数据的Id',
  `parent_id` INT UNSIGNED NULL COMMENT '父目录Id',
  `type` ENUM('IMAGE', 'FOLDER') NOT NULL DEFAULT 'FOLDER' COMMENT '图片或文件夹',
  PRIMARY KEY (`id`),
  INDEX `picture_material_idx` (`id` ASC) VISIBLE,
  CONSTRAINT `picture_material_image_id_x1`
   FOREIGN KEY (`image_id`)
     REFERENCES `skylink`.`picture` (`id`)
     ON DELETE CASCADE
     ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`meeting_room_scene`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`meeting_room_scene` (
  `id` INT UNSIGNED AUTO_INCREMENT NOT NULL,
  `room_id` INT UNSIGNED NULL COMMENT '所属房间的Id',
  `name` VARCHAR(32) NOT NULL,
  `scene_data` JSON NOT NULL COMMENT '场景数据',
  `timing_mode` ENUM('NO_TIMING', 'ONCE', 'REPEAT') NOT NULL DEFAULT 'ONCE' COMMENT '定时模式',
  `week` JSON NOT NULL COMMENT '星期',
  `start_time` BIGINT UNSIGNED COMMENT '开始时间',
  PRIMARY KEY (`id`),
  CONSTRAINT `meeting_room_scene_room_id`
      FOREIGN KEY (`room_id`)
          REFERENCES `skylink`.`position_group` (`position_id`)
          ON DELETE CASCADE
          ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`kvm_operation_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`kvm_operation_log`
(
    `id`                      INT UNSIGNED    NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `master_id`               VARCHAR(33)     NOT NULL COMMENT '主机ID',
    `operation_time`          BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
    `operation_type`          ENUM ('SEAT_OPT', 'VIDEO_WALL_OPT') COMMENT '操作类型',
    `operation_event`         VARCHAR(15)     NOT NULL DEFAULT '' COMMENT '操作事件',
    `operation_src_address`   VARCHAR(15)     NULL COMMENT '操作的来源IP地址',
    `operation_src_user`      VARCHAR(32)     NOT NULL DEFAULT '' COMMENT '操作的来源用户',
    `operation_src_device`    VARCHAR(32)     NOT NULL DEFAULT '' COMMENT '操作的来源设备',
    `operation_target_device` VARCHAR(32)     NOT NULL DEFAULT '' COMMENT '操作的目标设备',
    primary key (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`video_wall_banner`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`video_wall_banner` (
  `id` INT UNSIGNED AUTO_INCREMENT NOT NULL,
  `wall_id` INT UNSIGNED NOT NULL COMMENT '电视墙Id',
  `content` JSON NOT NULL COMMENT '场景数据',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '是否启用',
  PRIMARY KEY (`id`),
  CONSTRAINT `video_wall_banner_wall_id`
      FOREIGN KEY (`wall_id`)
          REFERENCES `skylink`.`kvm_video_wall` (`wall_id`)
          ON DELETE CASCADE
          ON UPDATE NO ACTION)
ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`seat_scene`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`seat_scene` (
    `scene_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '可视化场景Id',
    `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '场景名称',
    `unique_search_key` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '所属房间场景页面的Id',
    `data` JSON NOT NULL COMMENT '场景数据',
    PRIMARY KEY (`scene_id`)) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`visualization_ui_status`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `skylink`.`visualization_ui_status` (
  `id` VARCHAR(36) NOT NULL COMMENT '控件Id',
  `type` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '控件类型',
  `data` JSON NOT NULL COMMENT '控件数据',
  PRIMARY KEY (`id`)) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4;

-- -----------------------------------------------------
-- Table `skylink`.`peripheral_upgrade_package`
-- 创建外设升级包表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `peripheral_upgrade_package` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `file_path` varchar(255) NOT NULL COMMENT '文件路径',
    `md5` varchar(32) NOT NULL COMMENT '文件MD5',
    `device_type` varchar(32) NOT NULL COMMENT '设备类型',
    `version` varchar(50) NOT NULL COMMENT '版本号',
    `description` varchar(500) DEFAULT NULL COMMENT '描述信息',
    `file_size` bigint NOT NULL COMMENT '文件大小',
    `upload_time` bigint NOT NULL COMMENT '上传时间',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_md5` (`md5`),
KEY `idx_device_type` (`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外设升级包';

-- -----------------------------------------------------
-- Table `skylink`.`peripheral_upgrade_task`
-- 创建外设升级任务表
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `peripheral_upgrade_task` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `package_id` int NOT NULL COMMENT '升级包ID',
    `device_id` varchar(33) NOT NULL COMMENT '设备ID',
    `device_name` varchar(255) NOT NULL COMMENT '设备名称',
    `device_type` varchar(32) NOT NULL COMMENT '设备类型',
    `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-等待升级，1-升级中，2-升级成功，3-升级失败',
    `progress` int NOT NULL DEFAULT '0' COMMENT '升级进度(0-100)',
    `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
    `start_time` bigint NOT NULL COMMENT '开始时间',
    `end_time` bigint DEFAULT NULL COMMENT '结束时间',
    PRIMARY KEY (`id`),
    KEY `idx_package_id` (`package_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外设升级任务';

SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
