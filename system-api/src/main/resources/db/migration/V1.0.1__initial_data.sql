-- -----------------------------------------------------
-- Schema skylink
-- -----------------------------------------------------
USE `skylink` ;

-- -----------------------------------------------------
-- Table `skylink`.`department`
-- -----------------------------------------------------
INSERT INTO `department` (`department_id`, `department_desc`, `department_name`) VALUES
  (1, '管理员', '初始管理部门');

-- -----------------------------------------------------
-- Table `skylink`.`personnel`
-- -----------------------------------------------------
INSERT INTO `personnel` (`personnel_id`, `job_number`, `personnel_desc`, `personnel_enable`,
                         `personnel_name`, `department`, `personnel_create_time`)
VALUES (1, '001', '管理员', true, 'admin', 1, UNIX_TIMESTAMP());

-- -----------------------------------------------------
-- Table `skylink`.`account`
-- -----------------------------------------------------
INSERT INTO `account` (`account_creator`, `account_desc`, `account_enable`, `account_name`,
                       `account_password`, `business_auth`, `account_id`,
                       `personnel`, `account_create_time`)
VALUES ('admin', '管理员', true, 'admin', '123456', '{}', 1, 1, UNIX_TIMESTAMP());

-- -----------------------------------------------------
-- Table `skylink`.`operate_log`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`position_level`
-- -----------------------------------------------------
INSERT INTO `position_level` (`level_name`, `level_title`, `meeting`, `kvm`) VALUES
  ('MEETING_ROOM', '会议室', '1', '1');

INSERT INTO `position_level` (`level_name`, `level_title`, `meeting`, `kvm`) VALUES
  ('COMMAND_HALL', '指挥大厅', '1', '1');

INSERT INTO `position_level` (`level_name`, `level_title`, `meeting`, `kvm`) VALUES
  ('SEAT_GROUP', '坐席组', '0', '0');

INSERT INTO `position_level` (`level_name`, `level_title`, `meeting`, `kvm`) VALUES
  ('SUPPORTING_ROOM', '机房', '0', '0');

INSERT INTO `position_level` (`level_name`, `level_title`, `meeting`, `kvm`) VALUES
  ('OFFICE', '办公室', '1', '0');

-- -----------------------------------------------------
-- Table `skylink`.`position_group`
-- -----------------------------------------------------

INSERT INTO `position_group` (`position_id`, `name`, `parent_id`, `seq_in_parent`, `position_level`,
                              `max_meet_personnel`, `start_use_time`) VALUES
  (1, '指挥大厅', 0, 0, 'COMMAND_HALL', 500, UNIX_TIMESTAMP());

INSERT INTO `position_group` (`position_id`, `name`, `parent_id`, `seq_in_parent`, `position_level`,
                              `max_meet_personnel`, `start_use_time`) VALUES
    (2, '会议室', 0, 0, 'MEETING_ROOM', 500, UNIX_TIMESTAMP());

-- -----------------------------------------------------
-- Table `skylink`.`device_model`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Device Model Aircon
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (1001, 'AIRCON', '[]', 'MediaComm', 10, '云控主机', '[]', 'KVM', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (1101, 'AIRCON_ENCODER', '[{"name": "streamType", "unit": "", "internal": false, "required": true, "paramType": "ENUMERATION",
"displayName": "码流", "enumOptions": [{"title": "主码流", "value": "0"}, {"title": "子码流", "value": "1"}],
"defaultValue": "1", "enumSourceType": "STATIC"}, {"name": "rtspUrl", "unit": "", "internal": false, "required": false,
"paramType": "STRING", "displayName": "视频流路径"}]',
     'MediaComm', 10, '云控Tx', '[]', '[]', 'PER_EQUIPMENT_TX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_desc`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (1102, 'AIRCON_OTHER_SRC', '[]', 'MediaComm', 10, '', '云控IPC', '[]', '[]', 'PER_EQUIPMENT_TX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (1201, 'AIRCON_SEAT_RX', '[]', 'MediaComm', 10, '云控坐席Rx', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (1202, 'AIRCON_VIDEO_WALL_RX', '[]', 'MediaComm', 10, '云控大屏Rx', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (1301, 'AIRCON_VIDEO_WALL', '[]','MediaComm', 10, '云控视频墙', '[]', 'VIDEO_WALL', '[]');

-- -----------------------------------------------------
-- Device Model Kaito02
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4101, 'KAITO02', '[{"name": "projectId", "unit": "", "required": true, "paramType": "STRING", "displayName": "项目ID"},
{"name": "secretKey", "unit": "", "required": true, "paramType": "STRING", "displayName": "密钥"},
{"name":"extendModel","unit":"","internal":false,"required":true,"paramType":"ENUMERATION",
"displayName":"扩展型号","enumOptions":[{"title":"E","value":"e"},{"title":"H","value":"h"}],
"defaultValue":"e","enumSourceType":"STATIC"}]',
     'MediaComm', 10, 'Kaito-02处理器', '[]', 'KVM', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4201, 'KAITO02_INPUT', '[]','MediaComm', 10, 'Kaito-02处理器输入卡', '[]', 'PER_EQUIPMENT_TX', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4202, 'KAITO02_IPC', '[{"name": "streamType", "unit": "", "internal": false, "required": true,
"paramType": "ENUMERATION", "displayName": "码流", "enumOptions": [{"title": "主码流", "value": "mainStreamId"},
{"title": "子码流", "value": "subStreamId"}], "defaultValue": "mainStreamId", "enumSourceType": "STATIC"}]',
     'MediaComm', 10, 'Kaito-02处理器IPC', '[]', 'PER_EQUIPMENT_TX', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4301, 'KAITO02_OUTPUT', '[]','MediaComm', 10, 'Kaito-02处理器输出卡', '[]', 'PER_EQUIPMENT_RX', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4401, 'KAITO02_SLOT', '[]','MediaComm', 10, 'Kaito-02处理器插槽', '[]', 'INTERNAL_BOARD', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (4501, 'KAITO02_VIDEO_WALL', '[]','MediaComm', 10, 'Kaito-02视频墙', '[]', 'VIDEO_WALL', '[]');

-- -----------------------------------------------------
-- Device Model Caesar
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`,
                            `sub_system`, `ext_option`) VALUES
    (2101, 'CAESAR', '[{"name":"extendModel","unit":"","internal":false,"required":false,"paramType":"ENUMERATION",
      "enumSource":"/skylink-api/general/device-model/device-models?subSystem=KVM","displayName":"扩展型号",
      "defaultValue":"CAESAR","enumLabelField":"modelName","enumSourceType":"INTERNAL_API","enumValueField":"deviceType"},
      {"name":"virtualAddress","unit":"","internal":false,"required":false,"paramType":"STRING","displayName":"虚拟IP"},
      {"name":"associateHost","unit":"","internal":false,"required":false,"paramType":"ENUMERATION",
        "enumSource":"HOST_SAME_GROUP_SAME_TYPE","displayName":"冗余对端主机","enumSourceType":"PRESET"}]',
     'MediaComm', 10, '凯撒中文主机', '[]', 'KVM','[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2201, 'CAESAR_TX', '[{"name":"ptzPort","unit":"","internal":false,"required":false,
      "paramType":"INTEGER","displayName":"云台端口","defaultValue":"80"},{"name":"ptzIp","unit":"",
      "internal":false,"required":false,"paramType":"STRING","displayName":"云台地址"},{"name":"decodeIp",
      "unit":"","internal":false,"required":false,"paramType":"STRING","displayName":"解码终端IP"},{
      "name":"decodePort","unit":"","internal":false,"required":false,"paramType":"INTEGER",
      "displayName":"解码终端PORT","defaultValue":"80"},{"name":"protocol","unit":"","internal":false,
      "required":false,"paramType":"ENUMERATION","displayName":"云台协议","enumOptions":[{
        "title":"PELCO-D","value":"PELCO-D"},{"title":"PELCO-P","value":"PELCO-P"},{"title":"VISCA",
        "value":"VISCA"},{"title":"PELCO-HIK","value":"PELCO-HIK"}],"defaultValue":"PELCO-D",
      "enumSourceType":"STATIC"}]', 'MediaComm', 10, '凯撒中文Tx', '[]', '[]', 'PER_EQUIPMENT_TX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2301, 'CAESAR_RX', '[]', 'MediaComm', 10, '凯撒中文Rx', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2302, 'CAESAR_FOUR_SCREEN_RX', '[{"name": "previewAddress", "unit": "", "internal": false,
      "required": false, "paramType": "STRING", "displayName": "编码器IP", "defaultValue": ""},
      {"name": "previewIntervalTime", "unit": "毫秒", "range": {"lowerBound": "500", "upperBound": "2000"},
        "internal": false, "required": false, "paramType": "INTEGER", "displayName": "预览帧率",
        "defaultValue": "1500"},{"name":"streamType","unit":"","internal":false,"required":true,
        "paramType":"ENUMERATION","displayName":"码流","enumOptions":[{"title":"主码流","value":"0"},
        {"title":"子码流","value":"1"}],"defaultValue":"1","enumSourceType":"STATIC"}]',
     'MediaComm', 10, '凯撒中文四画面Rx', '[]', '[]',
     'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2303, 'CAESAR_KAITO_SECOND_CARD', '[]', 'MediaComm', 10, '凯撒中文嗨动子母卡', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2304, 'CAESAR_DECODE_DEVICE', '[{"name":"decodeIp",
      "displayName":"解码终端IP","paramType":"STRING","unit":"","required":false},{"name":"decodePort",
      "displayName":"解码终端PORT","paramType":"INTEGER","unit":"","required":false,"defaultValue":"80",
      "range":{"lowerBound":"1","upperBound":"65535"}}]', 'MediaComm', 10, '凯撒中文融合通信终端', '[]', '[]', 'PER_EQUIPMENT_TX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2305, 'CAESAR_R2P4F', '[]','MediaComm', 10, 'R2P4F', '[]', '[]','PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2401, 'CAESAR_VP6', '[]', 'MediaComm', 10, '凯撒中文VP6', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2402, 'CAESAR_VP7', '[]', 'MediaComm', 10, '凯撒中文VP7', '[]', '[]', 'PER_EQUIPMENT_RX');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2501, 'CAESAR_SLOT', '[]', 'MediaComm', 10, '凯撒中文光纤板卡', '[]', '[]', 'INTERNAL_BOARD');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2601, 'CAESAR_VP6_VIDEO_WALL', '[]', 'MediaComm', 10, '凯撒中文VP6视频墙', '[]', '[]', 'VIDEO_WALL');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2602, 'CAESAR_KAITO_VIDEO_WALL', '[]', 'MediaComm', 10, '凯撒中文全光拼接屏', '[]', '[]', 'VIDEO_WALL');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2603, 'CAESAR_R1C8_VIDEO_WALL', '[
      {"name":"previewAddress","displayName":"预览终端IP", "paramType":"STRING","required":true,"unit":"","defaultValue":""},
      {"name":"previewModel","displayName":"电视墙预案", "paramType":"STRING","required":true,"unit":"","defaultValue":""},
      {"name":"previewType", "displayName":"预览方式","paramType":"ENUMERATION","required":true,"unit":"","defaultValue":"NONE",
        "enumOptions":[{"title":"码流","value":"RTSP"},{"title":"无","value":"NONE"},{"title":"图片","value":"SNAPSHOT"}]}]',
     'MediaComm', 10, 'R1C8', '[]', '[]', 'VIDEO_WALL');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `ext_option`, `sub_system`) VALUES
    (2604, 'CAESAR_VP7_VIDEO_WALL', '[{"name":"bottomImageHash","unit":"","internal":false,"required":false,
    "paramType":"SKYLINK_IMAGE","displayName":"超高清底图"},{"name":"ntpAddress","unit":"","internal":false,
    "required":false,"paramType":"STRING","displayName":"ntp服务地址"},{"name":"ntpPort","unit":"","range":{},
    "internal":false,"required":false,"paramType":"INTEGER","displayName":"ntp服务端口","defaultValue":"123"}]',
     'MediaComm', 10, '凯撒中文VP7视频墙', '[]', '[]', 'VIDEO_WALL');

-- -----------------------------------------------------
-- Device Model Switcher
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (9001, 'SWITCHER_TX', '[{"name": "room", "unit": "", "internal": false, "required": true, "paramType": "STRING",
      "displayName": "房间", "defaultValue": "机房01"}, {"name": "group", "unit": "", "internal": false, "required": true,
      "paramType": "STRING", "displayName": "分组", "defaultValue": "分组01"}]', 'MediaComm', 10, 'SWITCHER_TX', '[]',
     'PER_EQUIPMENT_OTHER', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (9101, 'SWITCHER_RX', '[{"name": "room", "unit": "", "internal": false, "required": true, "paramType": "STRING",
      "displayName": "房间", "defaultValue": "管制坐席01"}, {"name": "group", "unit": "", "internal": false, "required": true,
      "paramType": "STRING", "displayName": "分组", "defaultValue": "分组01"}]', 'MediaComm', 10, 'SWITCHER_RX', '[]',
     'PER_EQUIPMENT_OTHER', '[]');

-- -----------------------------------------------------
-- Device Model Other
-- -----------------------------------------------------
INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (13, 'MONITOR_SERVER', '[]', 'MediaComm', 10, '监控服务器', '[]', 'SELF_DIAGNOSIS', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `max_use_age`,
                            `model_desc`, `model_name`, `properties`,
                            `sub_system`, `ext_option`) VALUES
    (6104, 'TM1709', '[]', 10, '平板', 'TM1709',
     '[{\"propertyKey\":\"deviceResolutionRatio\",\"propertyValue\":\"1920_1080\"},
       {\"propertyKey\":\"devicePixelRatio\",\"propertyValue\":\"1\"}]', 'VISUALIZATION_TERMINAL',
     '[{"name":"deviceResolutionRatio","displayName":"分辨率(px)","paramType":"ENUMERATION",
       "unit":"","defaultValue":"1920_1080","required":true,"enumOptionKey":"rationValue",
       "enumOptionValue":"rationTitle", "enumOptions": [{"value": "2388_1668", "title": "2388 X 1668"},
         {"value": "1368_912", "title": "1368 X 912"}, {"value": "2560_1440", "title": "2560 X 1440"},
         {"value": "3840_2160", "title": "3840 X 2160"}, {"value": "2732_2048", "title": "2732 X 2048"},
         {"value": "1112_834", "title": "1112 X 834"}, {"value": "2736_1824", "title": "2736 X 1824"},
         {"value": "2560_1600", "title": "2560 X 1600"}, {"value": "2160_1620", "title": "2160 X 1620"},
         {"value": "1920_1080", "title": "1920 X 1080"}, {"value": "2048_1536", "title": "2048 X 1536"},
         {"value": "2224_1668", "title": "2224 X 1668"}, {"value": "1024_768", "title": "1024 X 768"},
         {"value": "1920_1200", "title": "1920 X 1200"}, {"value": "1280_720", "title": "1280 X 720"}]},
       {"name":"devicePixelRatio","displayName":"物理像素和设备独立像素的比例(dpr)","paramType":"ENUMERATION",
         "unit":"","defaultValue":"1","required":true,"enumOptionKey":"rationValue",
         "enumOptionValue":"rationTitle", "enumOptions": [{"value": "1.5", "title": "1.5(hdpi [1.5倍])"}
       , {"value": "4", "title": "4(xxxhdpi [4倍])"}, {"value": "3", "title": "3(xxhdpi [3倍])"}
       , {"value": "2", "title": "2(xhdpi [2倍])"}, {"value": "2.5", "title": "2.5(华为M5 Pro)"},
         {"value": "1", "title": "1(mdpi [1倍])"}]}]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`,
                            `sub_system`, `ext_option`) VALUES
    (807, 'COMPUTER_BROWSER', '[]', 'MediaComm', 10, 'web可视化软件',
     '[{\"propertyKey\":\"deviceResolutionRatio\",\"propertyValue\":\"1920_1200\"},
{\"propertyKey\":\"devicePixelRatio\",\"propertyValue\":\"1\"}]', 'VISUALIZATION_TERMINAL',
     '[{"name":"deviceResolutionRatio","displayName":"分辨率(px)","paramType":"ENUMERATION",
"unit":"","defaultValue":"1920_1080","required":true,"enumOptionKey":"rationValue",
"enumOptionValue":"rationTitle", "enumOptions": [{"value": "2388_1668", "title": "2388 X 1668"},
{"value": "1368_912", "title": "1368 X 912"}, {"value": "2560_1440", "title": "2560 X 1440"},
{"value": "3840_2160", "title": "3840 X 2160"}, {"value": "2732_2048", "title": "2732 X 2048"},
{"value": "1112_834", "title": "1112 X 834"}, {"value": "2736_1824", "title": "2736 X 1824"},
{"value": "2560_1600", "title": "2560 X 1600"}, {"value": "2160_1620", "title": "2160 X 1620"},
{"value": "1920_1080", "title": "1920 X 1080"}, {"value": "2048_1536", "title": "2048 X 1536"},
{"value": "2224_1668", "title": "2224 X 1668"}, {"value": "1024_768", "title": "1024 X 768"},
{"value": "1920_1200", "title": "1920 X 1200"}, {"value": "1280_720", "title": "1280 X 720"}]},
{"name":"devicePixelRatio","displayName":"物理像素和设备独立像素的比例(dpr)","paramType":"ENUMERATION",
"unit":"","defaultValue":"1","required":true,"enumOptionKey":"rationValue",
"enumOptionValue":"rationTitle", "enumOptions": [{"value": "1.5", "title": "1.5(hdpi [1.5倍])"}
, {"value": "4", "title": "4(xxxhdpi [4倍])"}, {"value": "3", "title": "3(xxhdpi [3倍])"}
, {"value": "2", "title": "2(xhdpi [2倍])"}, {"value": "2.5", "title": "2.5(华为M5 Pro)"},
{"value": "1", "title": "1(mdpi [1倍])"}]}]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (7001, 'NETGEAR', '[]', '', 10, 'NETGEAR', '[]', 'SWITCH_DEVICE', '[]');

INSERT INTO `device_model` (`model_id`, `device_type`, `ext_properties`, `manufacturer`,
                            `max_use_age`, `model_name`, `properties`, `sub_system`, `ext_option`) VALUES
    (8001, 'TSINGLI', '[]', 'MediaComm', 10, '清立中控', '[]', 'KVM', '[]');

-- -----------------------------------------------------
-- Table `skylink`.`visualization_scene`
-- -----------------------------------------------------
INSERT INTO `visualization_scene` (`scene_id`, `name`, `device_model`, `room_id`, `data`, `config_time`, `scene_type`) VALUES
    (1, '引导页', 807, 0, '{}', UNIX_TIMESTAMP(), 'GUIDE_PAGE');

-- -----------------------------------------------------
-- Table `skylink`.`kvm_video_src`
-- -----------------------------------------------------
INSERT INTO `kvm_asset_group` (`name`, `room_id`, `group_type`) VALUES
    ('云控Tx分组', 1, 'TX_GROUP');

INSERT INTO `kvm_asset_group` (`name`, `room_id`, `group_type`) VALUES
    ('凯撒Tx分组', 1, 'TX_GROUP');

INSERT INTO `kvm_asset_group` (`name`, `room_id`, `group_type`) VALUES
    ('IPC分组', 1, 'TX_GROUP');

-- -----------------------------------------------------
-- Table `skylink`.`device_group`
-- -----------------------------------------------------
INSERT INTO `device_group` (`group_id`, `group_seq`, `group_name`, `icon_size`, `col_count`) VALUES
  (1, 1, 'KVM主机', 10, 10);

-- -----------------------------------------------------
-- Table `skylink`.`kvm_master`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`kvmAsset`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`kvm_switch_log`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`kvm_user`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`kvm_seat`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`kvm_video_wall`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`visualization_layout`
-- -----------------------------------------------------
INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (6, '{\"height\":1440,\"panels\":[{\"height\":360,\"seq\":4,\"width\":640,\"xpos\":1280,\"ypos\":1080},{\"height\":360,\"seq\":3,\"width\":640,\"xpos\":640,\"ypos\":1080},{\"height\":360,\"seq\":2,\"width\":640,\"xpos\":0,\"ypos\":1080},{\"height\":1080,\"seq\":1,\"width\":1920,\"xpos\":0,\"ypos\":0}],\"width\":1920}', '上1下3', 6);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`, `layout_type`) VALUES
    (7, '{\"height\":1080,\"panels\":[{\"height\":540,\"seq\":4,\"width\":960,\"xpos\":960,
\"ypos\":540},{\"height\":540,\"seq\":3,\"width\":960,\"xpos\":0,\"ypos\":540},{\"height\":540,
\"seq\":2,\"width\":960,\"xpos\":960,\"ypos\":0},{\"height\":540,\"seq\":1,\"width\":960,\"xpos\":0,
\"ypos\":0}],\"width\":1920}', '2行2列', 7, 'GENERAL_LAYOUT');

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (9, '{\"height\":1080,\"panels\":[{\"height\":720,\"seq\":1,\"width\":1280,\"xpos\":0,\"ypos\":0},{\"height\":360,\"seq\":2,\"width\":640,\"xpos\":0,\"ypos\":720},{\"height\":360,\"seq\":3,\"width\":640,\"xpos\":640,\"ypos\":720},{\"height\":360,\"seq\":4,\"width\":640,\"xpos\":1280,\"ypos\":0},{\"height\":360,\"seq\":5,\"width\":640,\"xpos\":1280,\"ypos\":360},{\"height\":360,\"seq\":6,\"width\":640,\"xpos\":1280,\"ypos\":720}],\"width\":1920}', '1大5小', 9);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (4, '{\"height\":1080,\"panels\":[{\"height\":1080,\"seq\":1,\"width\":1920,\"xpos\":0,\"ypos\":0},{\"height\":360,\"seq\":2,\"width\":480,\"xpos\":1440,\"ypos\":720}],\"width\":1920}', '画中画', 4);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (3, '{\"height\":1080,\"panels\":[{\"height\":540,\"seq\":1,\"width\":1920,\"xpos\":0,\"ypos\":0},{\"height\":540,\"seq\":2,\"width\":960,\"xpos\":0,\"ypos\":540},{\"height\":540,\"seq\":3,\"width\":960,\"xpos\":960,\"ypos\":540}],\"width\":1920}', '品字形', 3);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`, `layout_type`) VALUES
    (5, '{\"height\":1080,\"panels\":[{\"height\":360,\"seq\":4,\"width\":480,\"xpos\":1440,
\"ypos\":720},{\"height\":360,\"seq\":3,\"width\":480,\"xpos\":1440,\"ypos\":360},{\"height\":360,
\"seq\":2,\"width\":480,\"xpos\":1440,\"ypos\":0},{\"height\":1080,\"seq\":1,\"width\":1440,
\"xpos\":0,\"ypos\":0}],\"width\":1920}', '左1右3', 5, 'GENERAL_LAYOUT');

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (8, '{\"height\":1080,\"panels\":[{\"height\":540,\"seq\":1,\"width\":640,\"xpos\":0,\"ypos\":0},{\"height\":540,\"seq\":2,\"width\":640,\"xpos\":640,\"ypos\":0},{\"height\":540,\"seq\":3,\"width\":640,\"xpos\":1280,\"ypos\":0},{\"height\":540,\"seq\":4,\"width\":640,\"xpos\":0,\"ypos\":540},{\"height\":540,\"seq\":5,\"width\":640,\"xpos\":640,\"ypos\":540},{\"height\":540,\"seq\":6,\"width\":640,\"xpos\":1280,\"ypos\":540}],\"width\":1920}', '2行3列', 8);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`, `layout_type`) VALUES
    (2, '{\"height\":1080,\"panels\":[{\"height\":1080,\"seq\":1,\"width\":960,\"xpos\":0,
\"ypos\":0},{\"height\":1080,\"seq\":2,\"width\":960,\"xpos\":960,\"ypos\":0}],\"width\":1920}',
     '1行2列', 2, 'GENERAL_LAYOUT');

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`) VALUES
    (10, '{\"height\":2160,\"panels\":[{\"height\":1080,\"seq\":1,\"width\":1920,\"xpos\":0,\"ypos\":0},{\"height\":1080,\"seq\":2,\"width\":1920,\"xpos\":0,\"ypos\":1080},{\"height\":2160,\"seq\":3,\"width\":3840,\"xpos\":1920,\"ypos\":0},{\"height\":1080,\"seq\":4,\"width\":1920,\"xpos\":5760,\"ypos\":0},{\"height\":1080,\"seq\":5,\"width\":1920,\"xpos\":5760,\"ypos\":1080}],\"width\":7680}', '4+1', 12);

INSERT INTO `visualization_layout` (`layout_id`, `layout_data`, `name`, `seq`, `layout_type`) VALUES
    (1, '{\"height\":1080,\"panels\":[{\"height\":1080,\"seq\":1,\"width\":1920,\"xpos\":0,
\"ypos\":0}],\"width\":1920}', '全屏', 1, 'GENERAL_LAYOUT');

-- -----------------------------------------------------
-- Table `skylink`.`wall_layout`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`video_wall_scene`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`system_bundles`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`alarm`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`device_model_signal`
-- -----------------------------------------------------
INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (13, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查服务器网线",
"thresholdName":"掉线"},{"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"在线"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"middleware.mq.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"消息队列状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"","thresholdName":"异常"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"middleware.database.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"数据库状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"",
"thresholdName":"异常"}, {"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"正常"}],
"outputEnable":false, "signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"middleware.zookeeper.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"集群服务状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"",
"thresholdName":"异常"}, {"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"正常"}],
"outputEnable":false, "signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"module.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"采集分析模块状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"","thresholdName":"异常"}, {
"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"正常"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"cpu.status", "aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[], "normalDesc":"正常","signalName":"cpu占用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals", "aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"", "statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"mem.status","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常", "signalName":"内存占用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"disk.status","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘占用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"collector.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"采集服务状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"",
"thresholdName":"未运行"},{"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"正常"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (1001, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查主机网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常", "signalName":"电源状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源",
"thresholdName":"电源故障"}]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%",
"statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100, "diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%", "statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"",
"controlName":""},"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%", "statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"",
"controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (1101, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源", "thresholdName":"电源故障"}]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals",
"aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高",
"lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"},{
"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},
{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (1102, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (1201, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{
"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"},{
"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},
{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (1202, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常",
"signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,
"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],
"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"",
"thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"度","statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"},{
"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]},
{"signalId":"loginUser","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"登录用户","signalType":"STRING",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"mouseStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"鼠标线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"keyboardStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"键盘线状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"udiskStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"U盘状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"touchStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"触屏线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_3","thresholdDesc":"","thresholdName":"掉线"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2101, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查主机网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"",
"controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals",
"aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高",
"lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},
{"signalId":"fanOnline","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇在线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"fan","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_2","thresholdDesc":"检查风扇","thresholdName":"异常"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan",
"aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},
{"signalId":"slot.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"板卡状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"slot","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{
"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查板卡是否正确插入","thresholdName":"未插板卡"},{"diValue":false,
"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"板卡正常"}],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"port.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光模状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"ports","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{
"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查光模是否在线或联系厂家更换光模","thresholdName":"光模故障"},{
"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"光模正常"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS",
"inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{
"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"电源供电正常"},{"alarmLevel":"LEVEL_2",
"statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3",
"thresholdDesc":"检查电源","thresholdName":"电源故障"}]},
{"aiControl":{},"aiThresholds":[],"diControls":[],
"diThresholds":[{"diValue":true,"thresholdName":"链路中断","alarmLevel":"LEVEL_3","thresholdDesc":"检查设备与主机间接线状态"},{
"diValue":false,"thresholdName":"链路正常","alarmLevel":"LEVEL_0","thresholdDesc":""}],"statusControls":[],
"statusThresholds":[],"signalId":"lineStatus","signalName":"链路状态","signalGroup":"linkLine","signalType":"BOOL",
"inputEnable":true,"outputEnable":false,"signalAssoKey":"","measurementUnit":"","normalDesc":"正常","aiUpperLimit":null,
"aiLowerLimit":null}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2201, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查光纤链路",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常",
"signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,
"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],
"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"",
"thresholdName":"异常"},{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"}]},
{"signalId":"edidValid","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通道EDID是否有效",
"signalType":"BOOL","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"无效"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2301, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查光纤链路",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常",
"signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,
"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],
"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"",
"thresholdName":"未检测"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2401, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查光纤链路",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"},{
"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2402, '[{"signalId":"link.status","aiControl":{},"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4",
"thresholdDesc":"检查设备网线","thresholdName":"通信异常"}],"outputEnable":false},
{"signalId":"temperature","aiControl":{},"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度",
"thresholdName":"高温","lowerThreshold":85.0}],"outputEnable":false,"measurementUnit":"℃"},
{"signalId":"cpuRate", "aiControl":{},"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdName":"负荷过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,"measurementUnit":"%"},
{"signalId":"memRate","aiControl":{},"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdName":"过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,"measurementUnit":"%"},
{"signalId":"diskRate","aiControl":{},"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0.0,"aiThresholds":[{"alarmLevel":"LEVEL_2",
"thresholdSeq":1,"thresholdName":"过高","lowerThreshold":85.0}],"aiUpperLimit":100.0,"outputEnable":false,
"measurementUnit":"%"},
{"signalId":"fanSpeed","aiControl":{},"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,
"signalGroup":"fan","aiLowerLimit":0.0,"aiUpperLimit":12000.0,"outputEnable":false,"measurementUnit":"rpm"},
{"signalId":"powerStatus","aiControl":{},"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS",
"inputEnable":true,"signalGroup":"power","outputEnable":false,"statusThresholds":[{"alarmLevel":"LEVEL_0",
"statusValue":0,"thresholdName":"电源供电正常"},{"alarmLevel":"LEVEL_2","statusValue":1,"thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":2,"thresholdDesc":"检查电源","thresholdName":"电源故障"}]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"},{
"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"正常"}]},
{"signalId":"opticalStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光口状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{
"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (2604, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],
"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals",
"aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高",
"lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"diskRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"磁盘使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},
{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,
"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},
{"signalId":"fanOnline","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇在线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"fan","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{
"diValue":true,"alarmLevel":"LEVEL_2","thresholdDesc":"检查风扇","thresholdName":"异常"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan",
"aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS",
"inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{
"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"电源供电正常"},{"alarmLevel":"LEVEL_2",
"statusValue":"2","thresholdDesc":"检查电源线","thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_4","statusValue":"3",
"thresholdDesc":"检查电源","thresholdName":"电源故障"}]},
{"aiControl":{},"aiThresholds":[],"diControls":[],"diThresholds":[{"diValue":true,"thresholdName":"链路中断",
"alarmLevel":"LEVEL_3","thresholdDesc":"检查设备与主机间接线状态"},{"diValue":false, "thresholdName":"链路正常",
"alarmLevel":"LEVEL_0","thresholdDesc":""}],"statusControls":[],"statusThresholds":[], "signalId":"lineStatus",
"signalName":"链路状态","signalGroup":"linkLine","signalType":"BOOL","inputEnable":true,"outputEnable":false,
"signalAssoKey":"","measurementUnit":"","normalDesc":"正常","aiUpperLimit":null,"aiLowerLimit":null},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"","thresholdName":"异常"},{
"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"未检测"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (7001, '[{"signalId":"fan.status.0","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇1状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查风扇连接状态","thresholdName":"未安装"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_4",
"statusValue":"3","thresholdDesc":"风扇故障，更换风扇","thresholdName":"异常"},{"alarmLevel":"LEVEL_3","statusValue":"4",
"thresholdDesc":"更换风扇","thresholdName":"风扇异常"},{"alarmLevel":"LEVEL_1","statusValue":"5",
"thresholdDesc":"检查风扇电源线","thresholdName":"风扇未供电"},{"alarmLevel":"LEVEL_1","statusValue":"6",
"thresholdDesc":"检查风扇电源线","thresholdName":"风扇不供电"},{"alarmLevel":"LEVEL_1","statusValue":"7",
"thresholdDesc":"检查风扇型号是否匹配","thresholdName":"风扇不匹配"}]},
{"signalId":"fan.status.1","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇2状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查风扇连接状态","thresholdName":"未安装"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_4",
"statusValue":"3","thresholdDesc":"风扇故障，更换风扇","thresholdName":"异常"},{"alarmLevel":"LEVEL_3","statusValue":"4",
"thresholdDesc":"更换风扇","thresholdName":"风扇异常"},{"alarmLevel":"LEVEL_1","statusValue":"5",
"thresholdDesc":"检查风扇电源线","thresholdName":"风扇未供电"},{"alarmLevel":"LEVEL_1","statusValue":"6",
"thresholdDesc":"检查风扇电源线","thresholdName":"风扇不供电"},{"alarmLevel":"LEVEL_1","statusValue":"7",
"thresholdDesc":"检查风扇型号是否匹配","thresholdName":"风扇不匹配"}]},
{"signalId":"fan.speed.0","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇1转速","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"r/min","statusThresholds":[]},
{"signalId":"fan.speed.1","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇2转速","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"r/min","statusThresholds":[]},
{"signalId":"fan.speed.2","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇3转速","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"r/min","statusThresholds":[]},
{"signalId":"fan.status.2","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇3状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源是否安装","thresholdName":"未安装"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_4",
"statusValue":"3","thresholdDesc":"电源故障，更换电源","thresholdName":"异常"},{"alarmLevel":"LEVEL_3","statusValue":"4",
"thresholdDesc":"更换电源","thresholdName":"电源异常"},{"alarmLevel":"LEVEL_1","statusValue":"5","thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_1","statusValue":"6","thresholdDesc":"检查电源线",
"thresholdName":"电源不供电"},{"alarmLevel":"LEVEL_1","statusValue":"7","thresholdDesc":"检查电源型号是否匹配",
"thresholdName":"电源不匹配"}]},
{"signalId":"fan.duty.level.0","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇1使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"检查风扇排风是否正常","thresholdName":"使用过高","lowerThreshold":60,"upperThreshold":null}],
"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%",
"statusThresholds":[]},{"signalId":"fan.duty.level.1","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"",
"controlName":""},"diControls":[],"normalDesc":"正常","signalName":"风扇2使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"检查风扇排风是否正常","thresholdName":"使用过高","lowerThreshold":60,"upperThreshold":null}],
"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%",
"statusThresholds":[]},
{"signalId":"fan.duty.level.2","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"风扇3使用率","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,
"thresholdDesc":"检查风扇排风是否正常","thresholdName":"使用过高","lowerThreshold":60,"upperThreshold":null}],
"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],
"measurementUnit":"%","statusThresholds":[]},
{"signalId":"power.status.0","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源1状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_1","statusValue":"5","thresholdDesc":"检查温度传感器是否安装",
"thresholdName":"未安装"},{"alarmLevel":"LEVEL_1","statusValue":"1","thresholdDesc":"","thresholdName":"温度低"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_2",
"statusValue":"3","thresholdDesc":"检查环境温度","thresholdName":"温度高"},{"alarmLevel":"LEVEL_3","statusValue":"4",
"thresholdDesc":"检查环境温度","thresholdName":"温度过高"}]},
{"signalId":"power.status.1","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源2状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源是否安装","thresholdName":"未安装"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_4",
"statusValue":"3","thresholdDesc":"电源故障，更换电源","thresholdName":"异常"},{"alarmLevel":"LEVEL_3","statusValue":"4",
"thresholdDesc":"更换电源","thresholdName":"电源异常"},{"alarmLevel":"LEVEL_1","statusValue":"5","thresholdDesc":"检查电源线",
"thresholdName":"电源未供电"},{"alarmLevel":"LEVEL_1","statusValue":"6","thresholdDesc":"检查电源线",
"thresholdName":"电源不供电"},{"alarmLevel":"LEVEL_1","statusValue":"7","thresholdDesc":"检查电源型号是否匹配",
"thresholdName":"电源不匹配"}]},
{"signalId":"temp.sensor.status.0","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"温度1状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_1","statusValue":"5","thresholdDesc":"检查温度传感器是否安装",
"thresholdName":"未安装"},{"alarmLevel":"LEVEL_1","statusValue":"0","thresholdDesc":"","thresholdName":"温度低"},{
"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"正常工作"},{"alarmLevel":"LEVEL_2",
"statusValue":"2","thresholdDesc":"检查环境温度","thresholdName":"温度高"},{"alarmLevel":"LEVEL_3","statusValue":"3",
"thresholdDesc":"检查环境温度","thresholdName":"温度过高"},{"alarmLevel":"LEVEL_4","statusValue":"4",
"thresholdDesc":"检查安装未知是否正常","thresholdName":"温度超高关机"},{"alarmLevel":"LEVEL_3","statusValue":"6",
"thresholdDesc":"","thresholdName":"不可用"}]},
{"signalId":"temp.sensor.status.1","aiControl":{},"diControls":[],
"normalDesc":"正常","signalName":"温度2状态","signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals",
"aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"",
"statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_1","statusValue":"5",
"thresholdDesc":"检查温度传感器是否安装","thresholdName":"未安装"},{"alarmLevel":"LEVEL_1","statusValue":"0",
"thresholdDesc":"","thresholdName":"温度低"},{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"",
"thresholdName":"正常工作"},{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查环境温度",
"thresholdName":"温度高"},{"alarmLevel":"LEVEL_3","statusValue":"3","thresholdDesc":"检查环境温度",
"thresholdName":"温度过高"},{"alarmLevel":"LEVEL_4","statusValue":"4","thresholdDesc":"检查安装未知是否正常",
"thresholdName":"温度超高关机"},{"alarmLevel":"LEVEL_3","statusValue":"6","thresholdDesc":"","thresholdName":"不可用"}]},
{"signalId":"device.cpu","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"CPU使用率",
"signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%",
"statusThresholds":[]},
{"signalId":"device.mem.free","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"",
"controlName":""},"diControls":[],"normalDesc":"正常","signalName":"剩余内存","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"MB","statusThresholds":[]},
{"signalId":"device.mem.total","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"总内存大小","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"MB","statusThresholds":[]},
{"signalId":"temp.sensor.value.0","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度值1","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":150,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},
{"signalId":"temp.sensor.value.1","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},
"diControls":[],"normalDesc":"正常","signalName":"温度值2","signalType":"ANALOG","inputEnable":true,
"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":150,"diThresholds":[],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},
{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"采集通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查交换机是否关机",
"thresholdName":"通信中断"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"port.status.0","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"控制端口状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"启用"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"关闭"},{"alarmLevel":"LEVEL_0",
"statusValue":"3","thresholdDesc":"","thresholdName":"测试"},{"alarmLevel":"LEVEL_4","statusValue":"0",
"thresholdDesc":"检查网线状态","thresholdName":"线路异常"},{"alarmLevel":"LEVEL_0","statusValue":"4","thresholdDesc":"",
"thresholdName":"未知状态"},{"alarmLevel":"LEVEL_0","statusValue":"5","thresholdDesc":"","thresholdName":"休眠"},{
"alarmLevel":"LEVEL_3","statusValue":"6","thresholdDesc":"检查端口是否正常","thresholdName":"端口不存在"},{
"alarmLevel":"LEVEL_0","statusValue":"7","thresholdDesc":"","thresholdName":"底层中断"}]},
{"signalId":"port.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"端口设置状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"ports","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[],"outputEnable":false,"signalAssoKey":"","measurementUnit":"","statusControls":[],
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"启用"},{
"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"","thresholdName":"关闭"},{"alarmLevel":"LEVEL_1",
"statusValue":"3","thresholdDesc":"","thresholdName":"测试"},{"alarmLevel":"LEVEL_4","statusValue":"0",
"thresholdDesc":"检查网线状态","thresholdName":"线路异常"},{"alarmLevel":"LEVEL_0","statusValue":"4","thresholdDesc":"",
"thresholdName":"未知状态"},{"alarmLevel":"LEVEL_0","statusValue":"5","thresholdDesc":"","thresholdName":"休眠"},{
"alarmLevel":"LEVEL_3","statusValue":"6","thresholdDesc":"检查端口是否正常","thresholdName":"端口不存在"},{
"alarmLevel":"LEVEL_0","statusValue":"7","thresholdDesc":"","thresholdName":"底层中断"}]},
{"signalId":"port.line.status.0","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"控制端口连接状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"启用"},{
"alarmLevel":"LEVEL_0","statusValue":"2","thresholdDesc":"","thresholdName":"关闭"},{"alarmLevel":"LEVEL_0",
"statusValue":"3","thresholdDesc":"","thresholdName":"测试"},{"alarmLevel":"LEVEL_4","statusValue":"0",
"thresholdDesc":"检查网线状态","thresholdName":"线路异常"},{"alarmLevel":"LEVEL_1","statusValue":"4","thresholdDesc":"",
"thresholdName":"未知状态"},{"alarmLevel":"LEVEL_2","statusValue":"5","thresholdDesc":"","thresholdName":"休眠"},{
"alarmLevel":"LEVEL_3","statusValue":"6","thresholdDesc":"检查端口是否正常","thresholdName":"端口不存在"},{
"alarmLevel":"LEVEL_2","statusValue":"7","thresholdDesc":"","thresholdName":"底层中断"}]},
{"aiControl":{}, "aiThresholds":[],"diControls":[],"diThresholds":[],"statusControls":[],"statusThresholds":[{
"statusValue":"1", "thresholdName":"启用","alarmLevel":"LEVEL_0","thresholdDesc":""},{"statusValue":"2",
"thresholdName":"关闭", "alarmLevel":"LEVEL_0","thresholdDesc":""},{"statusValue":"3","thresholdName":"测试",
"alarmLevel":"LEVEL_1", "thresholdDesc":""},{"statusValue":"0","thresholdName":"线路异常","alarmLevel":"LEVEL_4",
"thresholdDesc":"检查网线状态"},{"statusValue":"4","thresholdName":"未知状态","alarmLevel":"LEVEL_1","thresholdDesc":""},{
"statusValue":"5", "thresholdName":"休眠","alarmLevel":"LEVEL_2","thresholdDesc":""},{"statusValue":"6",
"thresholdName":"端口不存在", "alarmLevel":"LEVEL_3","thresholdDesc":"检查端口是否正常"},{"statusValue":"7",
"thresholdName":"底层中断", "alarmLevel":"LEVEL_2","thresholdDesc":""}],"signalId":"port.line.status",
"signalName":"端口连接状态","signalGroup":"ports","signalType":"STATUS","inputEnable":true,"outputEnable":false,
"signalAssoKey":"","measurementUnit":"","normalDesc":"正常","aiUpperLimit":null,"aiLowerLimit":null}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (4101, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查主机网线",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"temperature","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_2","thresholdDesc":"检查温度","thresholdName":"异常"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"fanOnline","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"风扇在线状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"fan","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{
"diValue":true,"alarmLevel":"LEVEL_2","thresholdDesc":"检查风扇","thresholdName":"异常"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{
"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查电源线","thresholdName":"电源未供电"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},
{"signalId":"voltStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电压状态","signalType":"BOOL",
"inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_2","thresholdDesc":"检查温度","thresholdName":"异常"}],
"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (4401, '[{"signalId":"slot.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"板卡状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"slot","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,
"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查插槽是否正确插入板卡","thresholdName":"未插板卡"},{
"diValue":false,"alarmLevel":"LEVEL_0","thresholdDesc":"","thresholdName":"插槽已接入板卡"}],"outputEnable":false,
"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (4201, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态",
"signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查输入卡插入状态",
"thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"videoResolution","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频分辨率",
"signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[]},
{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态",
"signalType":"STATUS","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],
"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"",
"statusThresholds":[{"alarmLevel":"LEVEL_2","statusValue":"2","thresholdDesc":"检查到信号源掉线","thresholdName":"掉源"},{
"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"检测到信号源输入","thresholdName":"有源"},{"alarmLevel":"LEVEL_4",
"statusValue":"0","thresholdDesc":"检测到无信号源输入","thresholdName":"无源"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (4301, '[{"aiControl":{},"aiThresholds":[],"diControls":[],"diThresholds":[],"statusControls":[],
"statusThresholds":[],"signalId":"videoResolution","signalName":"视频分辨率","signalGroup":"generalSignals",
"signalType":"STRING","inputEnable":true,"outputEnable":false,"signalAssoKey":"","measurementUnit":"",
"normalDesc":"正常","aiUpperLimit":null,"aiLowerLimit":null}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (9001, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查设备通信","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},{"signalId":"netStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"网口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"net","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"opticalStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"optical","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]},{"signalId":"videoResolution","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频分辨率","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"usbStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"串口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]}]');

INSERT INTO `device_model_signal` (`device_model_id`, `signals`) VALUES
    (9101, '[{"signalId":"link.status","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"通信状态","signalType":"BOOL","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[{"diValue":true,"alarmLevel":"LEVEL_4","thresholdDesc":"检查设备通信","thresholdName":"通信异常"}],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"cpuRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"Cpu使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"负荷过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"memRate","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"内存使用率","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":0,"aiThresholds":[{"alarmLevel":"LEVEL_2","thresholdSeq":1,"thresholdDesc":"","thresholdName":"过高","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":100,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"%","statusThresholds":[]},{"signalId":"temperature","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"温度","signalType":"ANALOG","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[{"alarmLevel":"LEVEL_3","thresholdSeq":1,"thresholdDesc":"降低环境温度","thresholdName":"高温","lowerThreshold":85,"upperThreshold":null}],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"℃","statusThresholds":[]},{"signalId":"fanSpeed","aiControl":{"lowerLimit":null,"upperLimit":null,"controlDesc":"","controlName":""},"diControls":[],"normalDesc":"正常","signalName":"风扇转速","signalType":"ANALOG","inputEnable":true,"signalGroup":"fan","aiLowerLimit":0,"aiThresholds":[],"aiUpperLimit":12000,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"rpm","statusThresholds":[]},{"signalId":"netStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"网口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"net","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"powerStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"电源状态","signalType":"STATUS","inputEnable":true,"signalGroup":"power","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"供电正常"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"检查电源线","thresholdName":"未供电"},{"alarmLevel":"LEVEL_4","statusValue":"2","thresholdDesc":"检查电源","thresholdName":"电源故障"}]},{"signalId":"opticalStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"光口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"optical","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已链接"},{"alarmLevel":"LEVEL_4","statusValue":"1","thresholdDesc":"","thresholdName":"未链接"}]},{"signalId":"videoLineStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频口接线状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_2","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]},{"signalId":"videoResolution","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"视频分辨率","signalType":"STRING","inputEnable":true,"signalGroup":"generalSignals","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[]},{"signalId":"usbStatus","aiControl":{},"diControls":[],"normalDesc":"正常","signalName":"串口状态","signalType":"STATUS","inputEnable":true,"signalGroup":"channel","aiLowerLimit":null,"aiThresholds":[],"aiUpperLimit":null,"diThresholds":[],"outputEnable":false,"signalAssoKey":"","statusControls":[],"measurementUnit":"","statusThresholds":[{"alarmLevel":"LEVEL_0","statusValue":"0","thresholdDesc":"","thresholdName":"已接入"},{"alarmLevel":"LEVEL_0","statusValue":"1","thresholdDesc":"","thresholdName":"未接入"}]}]');


-- -----------------------------------------------------
-- Table `skylink`.`role`
-- -----------------------------------------------------
INSERT INTO `role` (`role_id`, `name`) VALUES
    (1, 'root');

-- -----------------------------------------------------
-- Table `skylink`.`permission`
-- -----------------------------------------------------
INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (1, 'monitorEvent', '监控事件分发模块', '[{"regexp":"/v1/skylink-api/history/alarm/alarms/active","methods":["GET"]},{"regexp":"/v1/skylink-api/history/alarm-repair-desc/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/history/disable-alarm/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/rpc/monitor/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (2, 'historyLog', '历史记录查询', '[{"regexp":"/v1/skylink-api/history/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (21, 'kvmSwitchLog', 'KVM切换日志', '[{"regexp":"/v1/skylink-api/history/switch-log/**","methods":["GET","POST","PUT","DELETE"]}]', 2);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (22, 'alarmEvent', '告警事件处理', '[{"regexp":"/v1/skylink-api/history/alarm/**","methods":["GET","POST","PUT","DELETE"]}]', 2);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (23, 'disabledAlarmEvent', '告警屏蔽记录', '[{"regexp":"/v1/skylink-api/history/disable-alarm/**","methods":["GET","POST","PUT","DELETE"]}]', 2);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (24, 'sysOperateLog', '系统操作日志', '[{"regexp":"/v1/skylink-api/history/operate-log/**","methods":["GET","POST","PUT","DELETE"]}]', 2);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (25, 'kvmOperateLog', 'KVM操作日志', '[{"regexp": "/history/kvm-operation-log/**", "methods": ["GET","POST","PUT","DELETE"]}]', 2);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (3, 'generalInfo', '基础信息配置', '[{"regexp":"/v1/skylink-api/general/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (31, 'deviceGroup', '设备分组配置', '[{"regexp":"/v1/skylink-api/v1/skylink-api/**","methods":["GET","POST","PUT","DELETE"]}]', 3);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (32, 'deviceModel', '设备型号配置', '[{"regexp":"/v1/skylink-api/general/device-model/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/general/device-model-signal/**","methods":["GET","POST","PUT","DELETE"]}]', 3);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (33, 'position', '房间位置配置', '[{"regexp":"/general/position-group/**","methods":["GET","POST","PUT","DELETE"]}]', 3);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (4, 'kvmConfig', '配置管理', '[{"regexp":"/v1/skylink-api/kvm/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (41, 'kvmMaster', 'KVM主机配置', '[{"regexp":"/v1/skylink-api/kvm/kvm-master/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (42, 'kvmSlot', 'KVM板卡配置', '[{"regexp":"/v1/skylink-api/kvm/kvm-master/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-slot/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (43, 'kvmAsset', 'KVM外设配置', '[{"regexp":"/v1/skylink-api/kvm/kvm-master/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-extend-device/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (44, 'txGroup', 'Tx分组配置', '[{"regexp":"/v1/skylink-api/kvm/kvm-extend-device/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-asset-group/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (45, 'rxGroup', 'Rx分组配置', '[{"regexp":"/v1/skylink-api/kvm/kvm-extend-device/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-asset-group/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (46, 'kvmUser', 'KVM用户管理', '[{"regexp":"/v1/skylink-api/kvm/kvm-master/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-user/**","methods":["GET","POST","PUT","DELETE"]}]', 4);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (5, 'deviceMonitorConfig', '监控设备配置', '[]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (51, 'serverDevice', '服务器配置', '[]', 5);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (52, 'terminalDevice', '监控终端配置', '[]', 5);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (53, 'switchDevice', '交换机配置', '[]', 5);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (54, 'systemOperation', '监控系统运维', '[]', 5);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (55, 'switcherDevice', '切换器配置', '[]', 5);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (6, 'visualizationConfig', '可视化配置', '[{"regexp":"/v1/skylink-api/visualization/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (61, 'visualizationDevice', '可视化终端', '[{"regexp":"/v1/skylink-api/visualization/visualization-device/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/general/position-group/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/visualization-scene/**","methods":["GET"]}]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (62, 'centerControl', '物联网设备管理', '[]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (63, 'seat', '坐席管理', '[{"regexp":"/v1/skylink-api/general/position-group/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/kvm-seat/**","methods":["GET","POST","PUT","DELETE"]}]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (64, 'videoWall', '大屏管理', '[{"regexp":"/v1/skylink-api/general/position-group/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/kvm-video-wall/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/visualization/visualization-layout/**","methods":["GET","POST","PUT","DELETE"]}]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (65, 'syncVideoWall', '同屏管理', '[{"regexp":"/v1/skylink-api/general/position-group/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/sync-video-wall/**","methods":["GET","POST","PUT","DELETE"]}]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (66, 'visualizationInterface', '可视化界面', '[{"regexp":"/v1/skylink-api/visualization/visualization-scene/**","methods":["GET","POST","PUT","DELETE"]}]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (67, 'meetingPattern', '会议室模式配置', '[]', 6);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (7, 'userManage', '用户管理', '[{"regexp":"/v1/skylink-api/user/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (71, 'department', '部门管理', '[{"regexp":"/v1/skylink-api/user/department/**","methods":["GET","POST","PUT","DELETE"]}]', 7);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (72, 'personnel', '人员管理', '[{"regexp":"/v1/skylink-api/user/department/**","methods":["GET"]},{"regexp":"/v1/skylink-api/user/personnel/**","methods":["GET","POST","PUT","DELETE"]}]', 7);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (73, 'role', '角色管理', '[{"regexp":"/v1/skylink-api/user/role/**","methods":["GET","POST","PUT","DELETE"]}]', 7);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (74, 'account', '账号管理', '[{"regexp":"/v1/skylink-api/user/account/**","methods":["GET","POST","PUT","DELETE"]},{"regexp":"/v1/skylink-api/user/role/**","methods":["GET"]},{"regexp":"/v1/skylink-api/user/personnel/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/visualization-scene/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/kvm-video-wall/**","methods":["GET"]},{"regexp":"/v1/skylink-api/general/position-group/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/kvm-asset-group/**","methods":["GET"]}]', 7);

INSERT INTO `permission` (`menu_id`, `name`, `title`, `rights`, `pid`) VALUES
    (8, 'visualizationControl', '可视化控制', '[{"regexp":"/v1/skylink-api/user/**","methods":["GET"]},{"regexp":"/v1/skylink-api/visualization/**","methods":["GET"]},{"regexp":"/v1/skylink-api/general/**","methods":["GET"]},{"regexp":"/v1/skylink-api/kvm/**","methods":["GET"]},{"regexp":"/v1/skylink-api/rpc/**","methods":["GET","POST","PUT","DELETE"]}]', 0);

-- -----------------------------------------------------
-- Table `skylink`.`account_role`
-- -----------------------------------------------------
INSERT INTO `account_role_merge` (`aid`, `rid`) VALUES
    (1, 1);

-- -----------------------------------------------------
-- Table `skylink`.`alarm_repair_desc`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`disabled_alarm`
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `skylink`.`role_permission_merge`
-- -----------------------------------------------------
INSERT INTO `role_permission_merge` (`rid`, `pid`) SELECT 1, `menu_id` FROM `permission`;
