alter table peripheral_upgrade_package
    add package_name varchar(64) null comment '包名';

alter table peripheral_upgrade_task
    add package_version <PERSON>SO<PERSON> not null comment '升级包的版本';

alter table kvm_asset
    modify version <PERSON><PERSON><PERSON> not null;

alter table peripheral_upgrade_package
    modify version <PERSON><PERSON><PERSON> not null comment '版本号';

drop index idx_device_id on peripheral_upgrade_task;

alter table peripheral_upgrade_task
    add constraint peripheral_upgrade_task_kvm_asset_id
        foreign key (device_id) references kvm_asset (asset_id)
            on delete cascade;
