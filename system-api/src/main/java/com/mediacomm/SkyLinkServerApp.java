package com.mediacomm;

import com.mediacomm.config.thread.ThreadPool;
import com.mediacomm.pojo.SystemInfoDto;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.EnableTimeSeries;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * .
 */
@SpringBootApplication
//@EnableDiscoveryClient
@EnableConfigurationProperties({ThreadPool.class, SystemInfoDto.class})
@EnableFeignClients
@EnableScheduling
@EnableTimeSeries
@EnableSchedulerLock(defaultLockAtMostFor = "PT30S")
public class SkyLinkServerApp {
  /**
   * .
   */
  public static void main(String[] args) {
    // 关闭nacos日志，避免本项目的日志不输出的问题
    //System.setProperty("nacos.logging.default.config.enabled", "false");
    SpringApplication.run(SkyLinkServerApp.class, args);
  }

  @Bean
  public MessageConverter messageConverter() {
    return new Jackson2JsonMessageConverter();
  }
}
