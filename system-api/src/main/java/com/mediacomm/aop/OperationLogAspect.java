package com.mediacomm.aop;

import com.mediacomm.entity.dao.OperateLog;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.system.service.OperateLogService;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 操作日志切面.
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {
  @Autowired
  private OperateLogService operateLogService;
  @Autowired
  private AccountService accountService;

  @Pointcut("@annotation(com.mediacomm.system.annotation.OperationLogRecord)")
  public void logPointCut() {}

  /**
   * 后置通知.
   *
   * @param joinPoint .
   */
  @AfterReturning(pointcut = "logPointCut()")
  public void doAfterReturning(JoinPoint joinPoint) {
    handleLog(joinPoint);
  }

  private void handleLog(final JoinPoint joinPoint) {
    OperationLogRecord record = getAnnotationLog(joinPoint); // 当前请求对象
    ServletRequestAttributes attributes =
        (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (record == null || attributes == null) {
      return;
    }
    HttpServletRequest request = attributes.getRequest();
    String ipAddress = request.getRemoteAddr();

    String token = JwtUtils.parseJwt(request);
    if (token == null) {
      return;
    }
    AccountVo account = accountService.oneByName(
        Objects.requireNonNull(JwtUtils.verifyTokenByHmac(token)).getUsername());
    OperateLog recordLog = OperateLog.builder()
        .operateTime(System.currentTimeMillis())
        .uri(request.getRequestURI())
        .operator(account.getPersonnelName())
        .operatorIp(ipAddress)
        .title(record.title())
        .type(record.operateType())
        .operateBody(getAnnotationValue(joinPoint, record.requestBody()))
        .build();
    operateLogService.save(recordLog);
  }

  private OperationLogRecord getAnnotationLog(JoinPoint joinPoint) {
    Signature signature = joinPoint.getSignature();
    MethodSignature methodSignature = (MethodSignature) signature;
    Method method = methodSignature.getMethod();
    if (method != null) {
      return method.getAnnotation(OperationLogRecord.class);
    }
    return null;
  }

  /**
   * 获取方法中切入点的主机参数的数值.
   *
   * @param joinPoint 切入点.
   * @param param "#{param1}".
   * @return .
   */
  private String getAnnotationValue(JoinPoint joinPoint, String param) {
    // 方法中的所有参数
    Map<String, Object> params = getParams(joinPoint);
    // 参数是否是动态 #{param}
    if (param.matches("^#\\{\\D*}")) {
      param = param.replace("#{", "").replace("}", "");
      // 处理request body，转json字符串保存
      StringBuilder builder = new StringBuilder();
      builder.append("[");
      String[] paramNames = param.split(",");
      for (String name : paramNames) {
        String val = JsonUtils.encode(getValue(params, name));
        builder.append(val);
      }
      builder.append("]");
      if (builder.toString().endsWith(",")) {
        return builder.substring(0, builder.length() - 1);
      } else {
        return builder.toString();
      }
    }
    return "[]";
  }

  private Map<String, Object> getParams(JoinPoint joinPoint) {
    Map<String, Object> params = new HashMap<>();
    Object[] array = joinPoint.getArgs();
    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    String[] names = signature.getParameterNames();
    for (int i = 0; i < array.length; i++) {
      params.put(names[i], array[i]);
    }
    return params;
  }

  private Object getValue(Map<String, Object> map, String param) {
    for (Map.Entry<String, Object> entry : map.entrySet()) {
      if (entry.getKey().equals(param)) {
        return entry.getValue();
      }
    }
    return null;
  }
}
