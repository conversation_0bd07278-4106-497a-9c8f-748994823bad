package com.mediacomm.pojo;

import com.mediacomm.entity.dao.Version;
import com.mediacomm.system.variable.sysenum.DeviceType;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class PeripheralUpgradePackageDto {
  private List<Version> version = new ArrayList<>();
  private DeviceType deviceType;
  private String description;

  public PeripheralUpgradePackageDto(DeviceType deviceType) {
    this.deviceType = deviceType;
  }
}
