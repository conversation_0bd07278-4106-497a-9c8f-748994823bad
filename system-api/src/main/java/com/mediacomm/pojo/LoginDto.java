package com.mediacomm.pojo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * .
 */
@Data
public class LoginDto {
  @NotNull(message = "The account name must be not null.")
  @NotEmpty(message = "The account name must be not empty.")
  private String username;
  @NotNull(message = "The account password must be not null.")
  @NotEmpty(message = "The account password must be not empty.")
  private String password;
}
