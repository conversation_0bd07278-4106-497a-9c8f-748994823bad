package com.mediacomm.pojo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 来自客户端的可视化终端设备信息.
 */
@Data
public class VisualizationDeviceInfoDto {
  @NotNull(message = "The sn must be not null.")
  @NotEmpty(message = "The sn must be not empty.")
  private String sn;
  private String version;
  @NotNull(message = "The deviceType must be not null.")
  @NotEmpty(message = "The deviceType must be not empty.")
  private String deviceType;
}
