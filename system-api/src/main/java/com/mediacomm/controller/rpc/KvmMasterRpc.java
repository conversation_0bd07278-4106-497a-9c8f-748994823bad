package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.config.websocket.client.KvmWebSocketClientManager;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "远程Kvm主机调用方法")
@RestController
@RequestMapping(ResUrlDef.KVM_RPC)
public class KvmMasterRpc extends SkyLinkController<KvmMaster, KvmMasterService> {

  @Autowired
  RpcSenderUtils senderUtils;
  @Autowired
  KvmWebSocketClientManager kvmWebSocketClientManager;

  @OperationLogRecord(title = "重新读取主机所有配置", operateType = OperateType.OTHER)
  @Operation(summary = "重新读取主机配置")
  @GetMapping ("/refresh/{id}")
  public Result<?> refreshKvm(@PathVariable String id) {
    kvmWebSocketClientManager.checkConnect(id);
    return doCmd(id, RoutingOperation.CONFIG_REFRESH);
  }

  @OperationLogRecord(title = "重新读取主机外设信息", operateType = OperateType.OTHER)
  @Operation(summary = "重新读取主机外设信息")
  @GetMapping("/refresh/extend/{id}")
  public Result<?> refreshKvmExtend(@PathVariable String id) {
    return doCmd(id, RoutingOperation.EXT_REFRESH);
  }

  private Result<?> doCmd(String id, String topicDto) {
    KvmMaster master = service.getById(id);
    return JsonUtils.decode(senderUtils.send(master.getMasterId(), topicDto,
        BuildMqRequestBodyUtils.buildKvmMasterMqBody(master)),
        new TypeReference<>() {});
  }
}
