package com.mediacomm.controller.rpc;

import static com.mediacomm.system.variable.sysenum.DeviceType.HIKVISION_SECURE_HOST;
import static com.mediacomm.system.variable.sysenum.DeviceType.HIKVISION_SECURE_IPC;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.domain.Operate;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ArrayList;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Slf4j
@Tag(name = "远程摄像头调用方法")
@RestController
@RequestMapping(ResUrlDef.PTZ_RPC)
public class PtzRpc extends SkyLinkController<KvmAsset, KvmAssetService> {
  @Autowired
  RpcSenderUtils senderUtils;
  @Autowired
  KvmMasterService masterService;

  @Operation(summary = "根据安防主机类型获取IPC列表")
  @GetMapping("/ptz-devices")
  @Parameter(name = "deviceType", description = "设备类型")
  public Result<Collection<KvmAssetVo>> getPtzTree(DeviceType deviceType) {
    if (HIKVISION_SECURE_HOST.equals(deviceType)) {
      Collection<KvmMaster> masters = masterService.allByDeviceModel(deviceType.getDeviceTypeId());
      Collection<KvmAssetVo> assets = new ArrayList<>();
      switch (deviceType) {
        case HIKVISION_SECURE_HOST -> masters.forEach((master) -> {
          assets.addAll(service.allByDeviceModelId(HIKVISION_SECURE_IPC.getDeviceTypeId(),
              master.getMasterId()));
        });
        default -> log.warn("This type was not found!" + deviceType);
      }
      return Result.ok(assets);
    }
    return Result.failure("No ipc type!", ResponseCode.EX_FAILURE_400);
  }

  @Operation(summary = "发送控制云台指令")
  @PostMapping("/{id}")
  public Result<?> ptzControl(@PathVariable String id, @RequestBody Operate opt) {
    opt.setTxId(id);
    return doCmd(id, RoutingKey.PTZ_CONTROL, opt);
  }

  private <T> Result<T> doCmd(String id, String topicDto, T requestBody) {
    KvmAsset asset = service.getById(id);
    if (asset == null) {
      return Result.failure("No active asset!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(topicDto,
        BuildMqRequestBodyUtils.buildKvmAssetMqBody(asset, requestBody)), new TypeReference<>() {
    });
  }

}
