package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.SeatScene;
import com.mediacomm.entity.message.reqeust.body.ChannelIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.DecoderIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxRequestBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxesRequestBody;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmSeatService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.KvmOperationUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "远程Kvm坐席调用方法")
@RestController
@RequestMapping(ResUrlDef.KVM_SEAT_RPC)
public class KvmSeatRpc extends SkyLinkController<KvmSeat, KvmSeatService> {
  @Autowired
  RpcSenderUtils senderUtils;

  @Operation(summary = "获取当前窗口")
  @PostMapping("/panels")
  @Parameter(name = "seatId", description = "坐席Id")
  public Result<?> getCurrentSeatPanels(@RequestParam("seatId") Integer seatId,
                                        @RequestBody ChannelIdRequestBody requestBody) {
    return doCmd(seatId, RoutingOperation.SEAT_PANELS_GET, requestBody);
  }

  @OperationLogRecord(title = "坐席屏幕打开单个窗口", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "指定坐席屏幕打开单个窗口")
  @PostMapping("/open-panel")
  @Parameter(name = "seatId", description = "坐席Id")
  public Result<?> openPanel(HttpServletRequest request,
                             @RequestParam("seatId") Integer seatId,
                             @RequestBody SeatOpenTxRequestBody requestBody) {
    return doCmd(seatId, RoutingOperation.SEAT_PANEL_OPEN, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  @Operation(summary = "指定坐席屏幕打开多个窗口")
  @PostMapping("/open-panels")
  public Result<?> openPanels(@RequestParam("seatId") Integer seatId,
                              @RequestBody SeatOpenTxesRequestBody requestBody) {
    return doCmd(seatId, RoutingOperation.SEAT_PANELS_OPEN, requestBody);
  }

  @PostMapping("/open-scene")
  public Result<String> seatsOpenPanels(HttpServletRequest request, @RequestBody SeatScene requestBody) {
    for (SeatOpenTxesRequestBody data : requestBody.getData()) {
      SeatOpenTxesRequestBody ots = SeatOpenTxesRequestBody.builder()
              .panels(data.getPanels())
              .layout(data.getLayout())
              .height(data.getHeight())
              .width(data.getWidth())
              .channelId(data.getChannelId())
              .decoderId(data.getDecoderId())
              .seatId(data.getSeatId())
              .build();
      doCmd(data.getSeatId(), RoutingOperation.SEAT_PANELS_OPEN, ots,
              KvmOperationUtils.getFrom(request));
    }
    return Result.ok();
  }

  @OperationLogRecord(title = "指定坐席屏幕关闭指定窗口", operateType = OperateType.OTHER, requestBody = "#{requestBody}")
  @Operation(summary = "指定坐席屏幕关闭指定窗口")
  @PostMapping("/close-panel")
  @Parameter(name = "seatId", description = "坐席Id")
  public Result<?> closePanel(HttpServletRequest request,
                              @RequestParam("seatId") Integer seatId,
                              @RequestBody DecoderIdRequestBody requestBody) {
    return doCmd(seatId, RoutingOperation.SEAT_PANEL_CLOSE, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "清除坐席屏幕所有窗口", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "清除指定坐席屏幕窗口")
  @PostMapping("/close-all-panels")
  @Parameter(name = "seatId", description = "坐席Id")
  public Result<?> closePanel(HttpServletRequest request,
                              @RequestParam("seatId") Integer seatId,
                              @RequestBody ChannelIdRequestBody requestBody) {
    return doCmd(seatId, RoutingOperation.SEAT_PANELS_CLOSE, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  private <T> Result<?> doCmd(Integer seatId, String topicDto, T requestBody) {
    KvmSeat seat = service.getById(seatId);
    if (seat != null) {
      return JsonUtils.decode(senderUtils.send(seat.getMasterId(), topicDto,
              BuildMqRequestBodyUtils.buildSeatMqBody(seat, requestBody)),
              new TypeReference<>(){});
    }
    return Result.failure("Seat not exist.", ResponseCode.EX_FAILURE_400);
  }

  private <T> Result<?> doCmd(Integer seatId, String topicDto, T requestBody,
                              KvmOperationFrom from) {
    KvmSeat seat = service.getById(seatId);
    if (seat != null) {
      return JsonUtils.decode(senderUtils.send(seat.getMasterId(), topicDto,
              BuildMqRequestBodyUtils.buildSeatMqBody(seat, requestBody, from)),
              new TypeReference<>(){});
    }
    return Result.failure("Seat not exist.", ResponseCode.EX_FAILURE_400);
  }
}
