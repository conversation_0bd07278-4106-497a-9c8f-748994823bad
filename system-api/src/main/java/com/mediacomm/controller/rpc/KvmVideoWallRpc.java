package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.caesar.domain.avgm.TxConnectRequest;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.PollingScenesStatusRequestBody;
import com.mediacomm.entity.message.reqeust.body.SwapVwPanelLayerRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwBannerRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.handler.PollingScenesHandler;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.service.VideoWallBannerService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.JwtUtils;
import com.mediacomm.util.KvmOperationUtils;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.WebSocketHandlerSendNoticeUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Slf4j
@Tag(name = "远程Kvm大屏调用方法")
@RestController
@RequestMapping(ResUrlDef.KVM_VIDEO_WALL_RPC)
public class KvmVideoWallRpc extends SkyLinkController<KvmVideoWall, KvmVideoWallService> {

  @Autowired
  private RpcSenderUtils senderUtils;
  @Autowired
  private WebSocketHandlerSendNoticeUtils noticeUtils;
  @Autowired
  private PollingScenesHandler scenesHandler;
  @Autowired
  private VideoWallBannerService bannerService;

  @Operation(summary = "获取当前窗口")
  @GetMapping("/panels")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> getCurrentVideoWallPanels(@RequestParam("videoWallId") Integer videoWallId) {
    return doCmd(videoWallId, RoutingOperation.VW_PANELS_GET);
  }

  @OperationLogRecord(title = "大屏打开单个窗口", operateType = OperateType.OTHER,
      requestBody = "#{panels}")
  @Operation(summary = "打开单个窗口")
  @PostMapping("/open-panel")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> openPanel(HttpServletRequest request,
                             @RequestParam("videoWallId") Integer videoWallId,
                             @RequestBody PanelRectRequestBody panels) {
    panels.setId(videoWallId);
    return doCmd(videoWallId, RoutingOperation.VW_PANEL_OPEN, panels,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "大屏打开多个窗口", operateType = OperateType.OTHER,
      requestBody = "#{panelsBody}")
  @Operation(summary = "批量打开多个窗口")
  @PostMapping("/open-panels")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> openPanels(HttpServletRequest request,
                              @RequestParam("videoWallId") Integer videoWallId,
                              @RequestBody OpenVwPanelsRequestBody panelsBody) {
    panelsBody.setId(videoWallId);
    return doCmd(videoWallId, RoutingOperation.VW_PANELS_OPEN, panelsBody,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "大屏移动单个窗口", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "移动窗口")
  @PostMapping("/move-panel")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> movePanel(HttpServletRequest request,
                             @RequestParam("videoWallId") Integer videoWallId,
                             @RequestBody PanelRectRequestBody requestBody) {
    requestBody.setId(videoWallId);
    return doCmd(videoWallId, RoutingOperation.VW_PANEL_MOVE, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "大屏交换窗口顺序", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "交换窗口顺序")
  @PostMapping("/swap-layer")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> swapLayer(HttpServletRequest request,
                             @RequestParam("videoWallId") Integer vwId,
                             @RequestBody SwapVwPanelLayerRequestBody requestBody) {
    requestBody.setId(vwId);
    return doCmd(vwId, RoutingOperation.VW_PANEL_LAYER_SWAP, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "大屏关闭单个窗口", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "关闭单个窗口")
  @PostMapping("/close-panel")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> closePanel(HttpServletRequest request,
                              @RequestParam("videoWallId") Integer vwId,
                              @RequestBody PanelRectRequestBody requestBody) {
    requestBody.setId(vwId);
    return doCmd(vwId, RoutingOperation.VW_PANEL_CLOSE, requestBody,
        KvmOperationUtils.getFrom(request));
  }

  @OperationLogRecord(title = "大屏关闭所有窗口", operateType = OperateType.OTHER,
      requestBody = "#{videoWallId}")
  @Operation(summary = "关闭所有窗口")
  @PostMapping("/close-all-panels")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> closeAllPanels(HttpServletRequest request,
                                  @RequestParam("videoWallId") Integer videoWallId) {
    return doCmd(videoWallId, RoutingOperation.VW_PANELS_CLOSE,
        KvmOperationUtils.getFrom(request));
  }

  @Operation(summary = "获取rtsp资源")
  @PostMapping("/open-streaming")
  @Parameter(name = "videoWallId", description = "大屏Id")
  public Result<?> openStream(@RequestParam("videoWallId") Integer videoWallId,
                              @RequestBody Set<String> txIds, HttpServletRequest request) {
    TxConnectRequest requestBody = new TxConnectRequest();
    String jti = Objects.requireNonNull(JwtUtils.verifyTokenByHmac(JwtUtils.parseJwt(request))).getJti();
    requestBody.setTxIds(txIds);
    requestBody.setClientId(jti);
    return doCmd(videoWallId, RoutingOperation.VS_OPEN,
            requestBody);
  }

  @OperationLogRecord(title = "开启/关闭大屏轮询", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "开启/关闭大屏轮询")
  @PostMapping("/polling")
  public Result<String> videoWallPollingEnable(
      @RequestBody PollingScenesStatusRequestBody requestBody) {
    KvmVideoWall wall = service.getById(requestBody.getVideoWallId());
    if (wall != null) {
      if (wall.isPollingScenesEnable() != requestBody.isPollingScenesEnable()) {
        service.setPollingScenesValue(requestBody.getVideoWallId(),
            requestBody.isPollingScenesEnable());
      }
      if (requestBody.isPollingScenesEnable()) {
        scenesHandler.addPollingScenesToTask(wall.getWallId());
      } else {
        scenesHandler.stopPollingScenesTask(wall.getWallId());
      }
      return Result.ok();
    } else {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
  }

  @Operation(summary = "获取大屏横幅信息")
  @GetMapping("/banner")
  public Result<?> getBanner(@RequestParam("videoWallId") Integer videoWallId) {
    return doCmd(videoWallId, RoutingOperation.BANNER_GET);
  }

  @OperationLogRecord(title = "开启或关闭大屏横幅信息", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "开启或关闭大屏横幅信息")
  @PostMapping("/banner/enable")
  public Result<?> enableBanner(HttpServletRequest request, @RequestBody VwEnableRequestBody requestBody) {
    Result<?> res = doCmd(requestBody.getId(), RoutingOperation.BANNER_ENABLE, requestBody,
            KvmOperationUtils.getFrom(request));
    noticeUtils.sendBannerChangeNotice(requestBody.getId());
    return res;
  }

  @OperationLogRecord(title = "开启或关闭大屏横幅背景色", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "开启或关闭大屏横幅背景色")
  @PostMapping("/banner/background-color/enable")
  public Result<?> enableBannerBgColor(HttpServletRequest request, @RequestBody VwEnableRequestBody requestBody) {
    Result<?> res = doCmd(requestBody.getId(), RoutingOperation.BANNER_BG_COLOR_ENABLE, requestBody,
            KvmOperationUtils.getFrom(request));
    noticeUtils.sendBannerChangeNotice(requestBody.getId());
    return res;
  }

  @OperationLogRecord(title = "运行或停止大屏的横幅配置", operateType = OperateType.OTHER, requestBody = "#{requestBody}")
  @Operation(summary = "运行大屏的横幅配置")
  @PostMapping("/banner/settings")
  public Result<?> runBanner(@RequestBody VwBannerRequestBody requestBody) {
    Result<?> res =  doCmd(requestBody.getWallId(), RoutingOperation.BANNER_SET, requestBody);
    noticeUtils.sendBannerChangeNotice(requestBody.getWallId());
    return res;
  }

  @OperationLogRecord(title = "开启或关闭显示底图", operateType = OperateType.INSERT, requestBody = "#{requestBody}")
  @Operation(summary = "开启或关闭显示底图")
  @PostMapping("/bottom-image/enable")
  public Result<?> enableBottomImage(HttpServletRequest request, @RequestBody VwEnableRequestBody requestBody) {
    return doCmd(requestBody.getId(), RoutingOperation.VW_BOTTOM_IMAGE_ENABLE, requestBody,
            KvmOperationUtils.getFrom(request));
  }

  private <T> Result<?> doCmd(Integer videoWallId, String topicDto, T requestBody,
                              KvmOperationFrom from) {
    KvmVideoWall videoWall = service.getById(videoWallId);
    return JsonUtils.decode(senderUtils.send(videoWall.getMasterId(), topicDto,
            BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall, requestBody, from)),
        new TypeReference<>() {
        });
  }


  private <T> Result<?> doCmd(Integer videoWallId, String topicDto, T requestBody) {
    KvmVideoWall videoWall = service.getById(videoWallId);
    if (videoWall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(videoWall.getMasterId(), topicDto,
            BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall, requestBody)),
        new TypeReference<>() {
        });
  }

  private Result<?> doCmd(Integer videoWallId, String topicDto) {
    KvmVideoWall videoWall = service.getById(videoWallId);
    if (videoWall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(videoWall.getMasterId(), topicDto,
        BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall)), new TypeReference<>() {
    });
  }

  private Result<?> doCmd(Integer videoWallId, String topicDto, KvmOperationFrom from) {
    KvmVideoWall videoWall = service.getById(videoWallId);
    if (videoWall == null) {
      return Result.failure("No exist videoWall!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(videoWall.getMasterId(), topicDto,
        BuildMqRequestBodyUtils.buildVideoWallMqBody(videoWall, from)), new TypeReference<>() {
    });
  }
}
