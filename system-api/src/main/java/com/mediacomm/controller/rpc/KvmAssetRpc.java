package com.mediacomm.controller.rpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.message.reqeust.body.ConnectTxRxRequestBody;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.TxIdRequestBody;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "远程Kvm外设调用方法")
@RestController
@RequestMapping(ResUrlDef.KVM_ASSET_RPC)
public class KvmAssetRpc extends SkyLinkController<KvmAsset, KvmAssetService> {
  @Autowired
  RpcSenderUtils senderUtils;

  @OperationLogRecord(title = "建立Tx与Rx的连接", operateType = OperateType.OTHER,
      requestBody = "#{requestBody}")
  @Operation(summary = "建立Tx与Rx的连接")
  @PostMapping("/tx-rx-connect")
  public Result<?> txRxConnect(@RequestBody ConnectTxRxRequestBody requestBody) {
    return doCmd(requestBody.getTxId(), RoutingOperation.TX_RX_CONNECT, requestBody);
  }

  /**
   * .
   */
  @Operation(summary = "发送需要预览的Tx设备，返回Tx预览路径")
  @PostMapping("/snapshot/txes")
  public Result<?> snapShotTxes(@RequestBody ObjectIds ids) {
    String oneTxId = ids.getActiveIds().get(0);
    if (Strings.isNullOrEmpty(oneTxId)) {
      return Result.failure("No active tx!", ResponseCode.EX_FAILURE_400);
    }
    return doCmd(oneTxId, RoutingOperation.SNAPSHOT_TX_GET, ids);
  }

  @Operation(summary = "建立Tx与编码器的连接")
  @Parameter(name = "txId", description = "tx外设的Id")
  @Parameter(name = "masterId", description = "主机Id")
  @PostMapping("/tx-encoder-connect")
  public Result<?> getInfo(@RequestParam(name = "txId") String txId) {
    TxIdRequestBody snapshotReqBody = new TxIdRequestBody();
    snapshotReqBody.setTxId(txId);
    return doCmd(txId, RoutingOperation.TX_ENCODER_CONNECT, snapshotReqBody);
  }

  private <T> Result<T> doCmd(String id, String topicDto, T requestBody) {
    KvmAsset asset = service.getById(id);
    if (asset == null) {
      return Result.failure("No exist asset!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(asset.getMasterId(), topicDto,
        BuildMqRequestBodyUtils.buildKvmAssetMqBody(asset, requestBody)), new TypeReference<>() {});
  }
}
