package com.mediacomm.controller.rpc;

import cn.hutool.core.util.HexUtil;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.UdpMsg;
import com.mediacomm.pojo.ProtocolCommandDto;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.util.TcpClient;
import com.mediacomm.util.UdpClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "自定义指令")
@RestController
@RequestMapping(ResUrlDef.PROTOCOL_COMMAND_RPC)
@Slf4j
public class ProtocolCommandRpc {
  private UdpClient udpClient;

  @Operation(summary = "发送自定义的指令")
  @PostMapping
  public Result<String> ptzControl(@RequestBody ProtocolCommandDto command) {
    sendCommand(command);
    return Result.ok();
  }

  private void sendCommand(ProtocolCommandDto command) {
    try {
      switch (command.getCommandType()) {
        case TCP -> {
          TcpClient client = new TcpClient(command.getAddress(), command.getPort());
          if (command.isHex()) {
            client.write(HexUtil.decodeHex(command.getCommand()));
          } else {
            client.write(command.getCommand().getBytes(StandardCharsets.UTF_8));
          }
          client.close();
        }
        case UDP -> {
          synchronized (ProtocolCommandRpc.class) {
            if (udpClient == null) {
              udpClient = new UdpClient();
            }
          }
          UdpMsg msg = UdpMsg.builder()
                  .address(command.getAddress())
                  .port(command.getPort())
                  .build();
          if (command.isHex()) {
            msg.setData(HexUtil.decodeHex(command.getCommand()));
          } else {
            msg.setData(command.getCommand().getBytes(StandardCharsets.UTF_8));
          }
          udpClient.send(msg);
        }
        default -> log.warn("Undefined command type.");
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }
}
