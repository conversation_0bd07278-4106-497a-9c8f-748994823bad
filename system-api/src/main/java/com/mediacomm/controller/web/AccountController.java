package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Account;
import com.mediacomm.entity.dao.Role;
import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.pojo.SecretKeyDto;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.system.service.RoleService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.JwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "账号管理", description = "账号配置的API")
@RestController
@RequestMapping(ResUrlDef.ACCOUNT)
public class AccountController extends SkyLinkController<Account, AccountService> {
  @Autowired
  RoleService roleService;

  @Operation(summary = "获取所有账号信息")
  @GetMapping("/accounts")
  public Result<Collection<AccountVo>> getAccounts() {
    return Result.ok(service.all());
  }

  @Operation(summary = "通过Id获取指定账号信息")
  @GetMapping("/{id}")
  public Result<AccountVo> getAccount(@PathVariable Integer id) {
    AccountVo accountVo = service.oneById(id);
    if (accountVo == null) {
      return Result.failure("Account not exist.", ResponseCode.EX_NOTFOUND_404);
    }
    accountVo.setAccountPassword(null);
    return Result.ok(accountVo);
  }

  /**
   * .
   *
   * @param id .
   * @param account .
   * @return .
   */
  @OperationLogRecord(title = "修改指定账号", operateType = OperateType.UPDATE,
      requestBody = "#{account}")
  @Operation(summary = "通过Id修改指定账号信息")
  @PutMapping("/{id}")
  public Result<?> updateAccount(@PathVariable Integer id,
                                 @RequestBody AccountVo account) {
    AccountVo oldAccount = service.oneById(id);
    if (oldAccount == null) {
      return Result.failure("Account not exist.", ResponseCode.EX_NOTFOUND_404);
    }
    service.setAccountBaseInfo(account);
    account.setAccountId(id);
    service.updateById(account);
    if (oldAccount.getRoles() != null && !oldAccount.getRoles().isEmpty()) {
      roleService.delBatchRoleByAccountId(id, oldAccount.getRoles().stream()
          .map(Role::getRoleId).collect(Collectors.toSet()));
    }
    if (account.getRoles() != null && !account.getRoles().isEmpty()) {
      roleService.saveBatchRoleByAccountId(id, account.getRoles().stream()
          .map(Role::getRoleId).collect(Collectors.toSet()));
    }
    return Result.ok();
  }

  @OperationLogRecord(title = "新增账号", operateType = OperateType.INSERT,
      requestBody = "#{account}")
  @Operation(summary = "新增账号")
  @PostMapping
  public Result<?> addAccount(@RequestBody Account account) {
    if (StringUtils.isEmpty(account.getAccountName()) || StringUtils.isEmpty(account.getAccountPassword())) {
      return Result.failure("The account name or password cannot be empty.", ResponseCode.EX_FAILURE_400);
    }
    AccountVo old = service.oneByName(account.getAccountName());
    if (old != null) {
      return Result.failure("The account name must be unique.", ResponseCode.EX_FAILURE_400);
    }
    service.save(account);
    return Result.ok();
  }

  @Operation(summary = "启用/停用账号")
  @Parameter(name = "enable", description = "是否启用", required = true)
  @PutMapping("/limit/{id}")
  public Result<String> enableAccount(@PathVariable Integer id,
                                      @RequestParam("enable") Boolean enable) {
    service.setAccountEnableValue(id, enable);
    return Result.ok();
  }

  @Operation(summary = "修改账号密码")
  @PostMapping("/secretKey/{id}")
  public Result<String> updateSecretKey(@PathVariable Integer id,
                                        @RequestBody SecretKeyDto secretKeyDto, HttpServletRequest request) {
    String token = JwtUtils.parseJwt(request);
    if (token == null) {
      return Result.failure("No token.", ResponseCode.EX_FAILURE_400);
    }
    AccountVo administrator = service.oneByName(
            Objects.requireNonNull(JwtUtils.verifyTokenByHmac(token)).getUsername());
    AccountVo targetAccount = service.oneById(id);
    if (targetAccount == null || administrator == null) {
      return Result.failure("Account not exist.", ResponseCode.EX_NOTFOUND_404);
    } else if (!Objects.equals(administrator.getAccountPassword(), secretKeyDto.getCurrentSecretKey())) {
      return Result.failure("The current password is incorrect.", ResponseCode.EX_FAILURE_400);
    } else if (!Objects.equals(secretKeyDto.getNewSecretKey(), secretKeyDto.getConfirmSecretKey())) {
      return Result.failure("The new password is not the same as the confirmed password.",
          ResponseCode.EX_FAILURE_400);
    }
    targetAccount.setAccountPassword(secretKeyDto.getNewSecretKey());
    service.updateById(targetAccount);
    return Result.ok();
  }
}
