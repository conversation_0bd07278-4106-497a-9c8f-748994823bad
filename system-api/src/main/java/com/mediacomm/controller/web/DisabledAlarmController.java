package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.dao.DisabledAlarm;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.AlarmService;
import com.mediacomm.system.service.DisabledAlarmService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 告警屏蔽.
 */
@Tag(name = "告警屏蔽记录")
@RestController
@RequestMapping(ResUrlDef.DISABLED_ALARM)
public class DisabledAlarmController extends SkyLinkController<DisabledAlarm, DisabledAlarmService> {
  @Autowired
  private AlarmService alarmService;
  @Operation(summary = "获取屏蔽告警记录")
  @GetMapping("/disable-alarms/page")
  public Result<PageResult<DisabledAlarm>> getByPage(@RequestParam("currentPage")
                                                       Integer currentPage,
                                                     @RequestParam("pageSize") Integer pageSize) {
    return Result.ok(service.allByPage(currentPage, pageSize));
  }

  @Operation(summary = "屏蔽告警")
  @PostMapping
  public Result<String> disableAlarm(@RequestParam("alarmId") Integer alarmId,
                                     @RequestBody DisabledAlarm disabledAlarm) {
    DisabledAlarm disabledAlarmInfo = service
            .oneByMasterIdAndDeviceIdAndSignalId(disabledAlarm.getMasterId(),
            disabledAlarm.getDeviceId(), disabledAlarm.getSignalId());
    if (disabledAlarmInfo == null) {
      disabledAlarm.setId(service.getMaxId() + 1);
      service.save(disabledAlarm);
      Alarm alarm = alarmService.getById(alarmId);
      if (alarm != null) {
        alarmService.delByMasterIdAndDeviceIdAndSignalId(alarm);
      }
    }
    return Result.ok();
  }

  @Operation(summary = "删除屏蔽告警")
  @DeleteMapping("/{id}")
  public Result<String> delDisabledAlarm(@PathVariable("id") Integer id) {
    service.removeById(id);
    return Result.ok();
  }
}
