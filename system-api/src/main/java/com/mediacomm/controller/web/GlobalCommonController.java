package com.mediacomm.controller.web;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.system.SystemUtil;
import com.mediacomm.config.VersionProperties;
import com.mediacomm.entity.Result;
import com.mediacomm.pojo.LogLevelDto;
import com.mediacomm.pojo.SystemAlarmSettingsDto;
import com.mediacomm.pojo.SystemInfoDto;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.ShellCmdUtil;
import com.mediacomm.util.SystemDefaultImageUtils;
import com.sun.management.OperatingSystemMXBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.management.ManagementFactory;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * .
 */
@Tag(name = "系统常规信息通用接口")
@RestController
@RequestMapping(ResUrlDef.GLOBAL_COMMON)
@Slf4j
public class GlobalCommonController {
  @Autowired
  SystemInfoDto defaultSystemInfoDto;
  @Autowired
  VersionProperties versionProperties;
  @Autowired
  RedisUtil redisUtil;
  @Autowired
  SystemDefaultImageUtils defaultImageUtils;

  private static String LOG_PATH = "/logs";

  @Operation(summary = "获取程序版本")
  @GetMapping("/version")
  public Result<Map<String, String>> getApplicationVersion() {
    Map<String, String> version = new HashMap<>();
    version.put("version", versionProperties.getAppVersion());
    version.put("apiVersion", versionProperties.getApiVersion());
    version.put("dbVersion", versionProperties.getDbVersion());
    version.put("redisVersion", versionProperties.getRedisVersion());
    return Result.ok(version);
  }

  @Operation(summary = "根据名称读取系统中的默认图")
  @GetMapping(value = "/{imageName}", produces = {MediaType.IMAGE_PNG_VALUE})
  public byte[] getDefaultImage(@PathVariable String imageName, HttpServletResponse response) {
    byte[] data = defaultImageUtils.getDefaultImage(imageName).getContent();
    if (data.length == 0) {
      response.setStatus(404);
    }
    return data;
  }

  @Operation(summary = "替换系统中的默认图")
  @PostMapping("/image")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
                  @SchemaProperty(
                          name = "multipartFile",
                          schema = @Schema(type = "string", format = "binary")),
                  @SchemaProperty(
                          name = "imageName",
                          schema = @Schema(type = "string")),
                  @SchemaProperty(
                          name = "stretch",
                          schema = @Schema(type = "boolean"))})})
  public Result<String> uploadImage(MultipartFile multipartFile, String imageName, boolean stretch) {
    return defaultImageUtils.saveDefaultImage(imageName, stretch, multipartFile);
  }

  @Operation(summary = "系统全局名称")
  @GetMapping("/system/info")
  public Result<SystemInfoDto> getSystemInfo() {
    Optional<String> info = redisUtil.getStr(RedisKey.SYSTEM_INFO);
    if (info.isPresent()) {
      SystemInfoDto newestInfo = JsonUtils.decode(info.get(), SystemInfoDto.class);
      if (newestInfo != null) {
        return Result.ok(newestInfo);
      }
    }
    return Result.ok(defaultSystemInfoDto);
  }

  @Operation(summary = "修改系统全局信息")
  @PostMapping("/system/info")
  public Result<SystemInfoDto> setSystemInfo(@RequestBody SystemInfoDto systemInfo) {
    redisUtil.set(RedisKey.SYSTEM_INFO, JsonUtils.encode(systemInfo));
    return Result.ok(systemInfo);
  }

  @Operation(summary = "告警设置")
  @PostMapping("/system/alarm/settings")
  public Result<SystemAlarmSettingsDto> setSystemAlarmSettings(@RequestBody SystemAlarmSettingsDto systemAlarmSettings) {
    Map<String, String> alarmSettings = new HashMap<>();
    alarmSettings.put("alarmWindowsEnable", String.valueOf(systemAlarmSettings.getAlarmWindowsEnable()));
    alarmSettings.put("alarmVoiceEnable", String.valueOf(systemAlarmSettings.getAlarmVoiceEnable()));
    redisUtil.hset(RedisKey.SYSTEM_ALARM_SETTINGS, alarmSettings);
    return Result.ok(systemAlarmSettings);
  }

  @Operation(summary = "获取告警设置")
  @GetMapping("/system/alarm/settings")
  public Result<SystemAlarmSettingsDto> setSystemAlarmSettings() {
    SystemAlarmSettingsDto defaultAlarmSettings =
            new SystemAlarmSettingsDto();
    Optional<String> alarmWindowsEnable = redisUtil.hget(RedisKey.SYSTEM_ALARM_SETTINGS, "alarmWindowsEnable");
    Optional<String> alarmVoiceEnable = redisUtil.hget(RedisKey.SYSTEM_ALARM_SETTINGS, "alarmVoiceEnable");
    if (alarmWindowsEnable.isPresent() && alarmVoiceEnable.isPresent()) {
      defaultAlarmSettings.setAlarmWindowsEnable(Boolean.parseBoolean(alarmWindowsEnable.get()));
      defaultAlarmSettings.setAlarmVoiceEnable(Boolean.parseBoolean(alarmVoiceEnable.get()));
    }
    return Result.ok(defaultAlarmSettings);
  }

  @Operation(summary = "获取系统分级信息")
  @GetMapping("/system/level")
  public Result<SubSystemType[]> getSystemLevel() {
    SubSystemType[] sys = SubSystemType.values();
    return Result.ok(sys);
  }

  @OperationLogRecord(title = "系统升级", operateType = OperateType.UPDATE)
  @Operation(summary = "系统升级")
  @PostMapping("/system/upgrade")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
                  @SchemaProperty(
                          name = "multipartFile",
                          schema = @Schema(type = "string", format = "binary")
                  )}
  )})
  public Result<String> systemUpgrade(MultipartFile multipartFile) {
    try {
      if (multipartFile != null && !multipartFile.isEmpty()) {
        String fileName = multipartFile.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) || !fileName.split("\\.")[1].equals("run")) {
          return Result.failure("System upgrade failed!", ResponseCode.EX_FAILURE_500);
        }
        FileWriter writer = new FileWriter("/tmp/" + fileName);
        writer.write(multipartFile.getBytes(), 0, multipartFile.getBytes().length);
        ShellCmdUtil.callScript(fileName, null, "/tmp");
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return Result.failure("Upload file failed!", ResponseCode.EX_FAILURE_500);
    }
    return Result.ok("System upgrade success!");
  }

  @Operation(summary = "获取服务器状态")
  @GetMapping("/system/status")
  public Result<Map<String, String>> getSystemStatus() {
    Map<String, String> status = new HashMap<>();
    OperatingSystemMXBean osmxb = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
    status.put("osName", SystemUtil.getOsInfo().getName());
    status.put("arch", SystemUtil.getOsInfo().getArch());
    status.put("hostName", SystemUtil.getHostInfo().getName());
    status.put("address", SystemUtil.getHostInfo().getAddress());
    status.put("serverPID", String.valueOf(SystemUtil.getCurrentPID()));
    status.put("maxMemory", new DecimalFormat("#.##")
            .format(osmxb.getTotalMemorySize() / 1024.0));
    status.put("freeMemory", new DecimalFormat("#.##")
            .format(osmxb.getFreeMemorySize() / 1024.0));
    status.put("usedMemory", new DecimalFormat("#.##")
            .format((osmxb.getTotalMemorySize() - osmxb.getFreeMemorySize()) / 1024.0));
    return Result.ok(status);
  }

  @Operation(summary = "下载系统日志")
  @GetMapping("/system/log")
  public void downloadSystemLog(HttpServletResponse response) {
    String fileName = "/skylink-server-log" + System.currentTimeMillis() + ".zip";
    File folder = new File(LOG_PATH);
    File file = new File(fileName);
    if (folder.exists() && folder.isDirectory()) {
      ZipUtil.zip(folder.getPath(), fileName);
    }
    if (file.exists()) {
      try (InputStream inputStream = new FileInputStream(file);
           OutputStream outputStream = response.getOutputStream()) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/" + FileUtil.getSuffix(fileName));
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());
        byte[] buffer = new byte[inputStream.available()];
        outputStream.write(buffer, 0, inputStream.read(buffer));
        outputStream.flush();
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      } finally {
        file.deleteOnExit();
      }
    }
  }

  @Operation(summary = "修改日志输出级别")
  @Parameter(name = "level", description = "日志输出级别")
  @PutMapping("/system/log/level/settings")
  public Result<String> logLevelSettings(@RequestParam LogLevelDto level) {
    log.info("Change log level {}!", level.name());
    LoggerContext logContext = (LoggerContext) LoggerFactory.getILoggerFactory();
    Logger logbackLogger = logContext.getLogger("com.mediacomm");
    logbackLogger.setLevel(Level.valueOf(level.name()));
    return Result.ok();
  }
}
