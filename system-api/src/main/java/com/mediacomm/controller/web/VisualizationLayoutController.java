package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.VisualizationLayoutService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.VisualizationLayoutType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "可视化布局", description = "可视化布局配置的API")
@RestController
@RequestMapping(ResUrlDef.VIS_LAYOUT)
public class VisualizationLayoutController extends
    SkyLinkController<VisualizationLayout, VisualizationLayoutService> {

  @Operation(summary = "获取所有可视化布局")
  @GetMapping("/visualization-layouts")
  @Parameter(name = "layoutType", description = "布局使用类型")
  public Result<Collection<VisualizationLayout>> getLayouts(@RequestParam(required = false)
                                                              VisualizationLayoutType layoutType) {
    return layoutType != null ? Result.ok(service.allByLayoutType(layoutType))
        : Result.ok(service.list());
  }

  @Operation(summary = "新增可视化布局")
  @PostMapping
  public Result<String> addLayout(@RequestBody VisualizationLayout layout) {
    service.save(layout);
    return Result.ok();
  }

  @Operation(summary = "修改可视化布局")
  @PutMapping("/{id}")
  public Result<String> updateLayout(@PathVariable Integer id,
                                     @RequestBody VisualizationLayout layout) {
    layout.setLayoutId(id);
    service.updateById(layout);
    return Result.ok();
  }
  @Operation(summary = "删除可视化布局")
  @DeleteMapping("/{id}")
  public Result<String> deleteLayout(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

}
