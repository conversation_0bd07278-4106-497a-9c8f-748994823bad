package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.vo.KvmUserVo;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmUserService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "KVM用户管理")
@RestController
@RequestMapping(ResUrlDef.KVM_USER)
public class KvmUserController extends SkyLinkController<KvmUser, KvmUserService> {

  @GetMapping("/kvm-users")
  public Result<Collection<KvmUserVo>> getKvmUsers(@RequestParam(name = "masterId",
                                                                 required = false)
                                                     String masterId) {
    return Result.ok(service.all(masterId));
  }
}
