package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceModelSignal;
import com.mediacomm.entity.dao.Signal;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceModelSignalService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ListIterator;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * .
 */
@Tag(name = "设备型号信号量")
@RestController
@RequestMapping(ResUrlDef.DEVICE_MODEL_SIGNAL)
public class DeviceModelSignalController extends
    SkyLinkController<DeviceModelSignal, DeviceModelSignalService> {

  @Operation(summary = "通过设备型号Id获取信号量")
  @GetMapping("/device-model-signals/{modelId}")
  public Result<DeviceModelSignal> getByModelId(@PathVariable Integer modelId) {
    return Result.ok(service.getById(modelId));
  }

  @OperationLogRecord(title = "添加型号信号量", operateType = OperateType.INSERT,
      requestBody = "#{signal}")
  @Operation(summary = "添加指定型号的信号量")
  @PostMapping
  public Result<String> addDeviceModelSignal(@RequestParam Integer deviceModelId, @RequestBody Signal signal) {
    DeviceModelSignal deviceModelSignal = service.getById(deviceModelId);
    if (deviceModelSignal != null) {
      for (Signal modelSignal : deviceModelSignal.getSignals()) {
        if (signal.getSignalId().equals(modelSignal.getSignalId())) {
          return Result.failure("Signal already exist.", ResponseCode.EX_CONFLICT_409);
        }
      }
      deviceModelSignal.getSignals().add(signal);
      service.updateById(deviceModelSignal);
      return Result.ok();
    }
    return Result.failure("DeviceModel not found.", ResponseCode.EX_NOTFOUND_404);
  }

  @OperationLogRecord(title = "修改型号信号量", operateType = OperateType.INSERT,
      requestBody = "#{signal}")
  @Operation(summary = "修改指定型号的信号量")
  @PutMapping
  public Result<String> updateDeviceModelSignal(@RequestParam Integer deviceModelId, @RequestBody Signal signal) {
    DeviceModelSignal deviceModelSignal = service.getById(deviceModelId);
    if (deviceModelSignal != null) {
      ListIterator<Signal> iterator = deviceModelSignal.getSignals().listIterator();
      while (iterator.hasNext()) {
        Signal modelSignal = iterator.next();
        if (signal.getSignalId().equals(modelSignal.getSignalId())) {
          iterator.set(signal);
          break;
        }
      }
      service.updateById(deviceModelSignal);
      return Result.ok();
    }
    return Result.failure("DeviceModel not found.", ResponseCode.EX_NOTFOUND_404);
  }

  @OperationLogRecord(title = "删除型号信号量", operateType = OperateType.INSERT,
      requestBody = "#{signal}")
  @Operation(summary = "修改指定型号的信号量")
  @DeleteMapping
  public Result<String> delDeviceModelSignal(@RequestParam Integer deviceModelId, @RequestParam String signalId) {
    DeviceModelSignal deviceModelSignal = service.getById(deviceModelId);
    if (deviceModelSignal != null) {
      ListIterator<Signal> iterator = deviceModelSignal.getSignals().listIterator();
      while (iterator.hasNext()) {
        Signal modelSignal = iterator.next();
        if (signalId.equals(modelSignal.getSignalId())) {
          iterator.remove();
          break;
        }
      }
      service.updateById(deviceModelSignal);
      return Result.ok();
    }
    return Result.failure("DeviceModel not found.", ResponseCode.EX_NOTFOUND_404);
  }
}
