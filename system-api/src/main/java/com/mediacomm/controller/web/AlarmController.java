package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.pojo.DeviceAlarmsDto;
import com.mediacomm.pojo.HandleAlarmDto;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.AccountService;
import com.mediacomm.system.service.AlarmService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.ServletUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "告警事件处理", description = "告警事件的API")
@RestController
@RequestMapping(ResUrlDef.ALARM)
@Slf4j
public class AlarmController extends SkyLinkController<Alarm, AlarmService> {
  @Autowired
  AccountService accountService;

  @Operation(summary = "根据指定日期获取指定告警信息")
  @Parameter(name = "startDate", description = "起点时间", required = true)
  @Parameter(name = "endDate", description = "结束时间", required = true)
  @Parameter(name = "currentPage", description = "当前查询页码", required = true)
  @Parameter(name = "pageSize", description = "每页查询数量", required = true)
  @Parameter(name = "masterId", description = "主机ID")
  @Parameter(name = "alarmLevel", description = "告警级别")
  @GetMapping("/alarms/page")
  public Result<PageResult<Alarm>> getByPage(@RequestParam("startDate") long startDate,
                                              @RequestParam("endDate") long endDate,
                                              @RequestParam("currentPage") Integer currentPage,
                                              @RequestParam("pageSize") Integer pageSize,
                                              @RequestParam(required = false, value = "masterId")
                                             String masterId,
                                                  @RequestParam(required = false, value = "alarmLevel")
                                             SubSystemType.AlarmLevel level) {
    return Result.ok(
        service.allByPageAndDate(currentPage, pageSize, startDate, endDate, masterId, level));
  }

  @Operation(summary = "查询活动告警")
  @Parameter(name = "currentPage", description = "当前查询页码", required = true)
  @Parameter(name = "pageSize", description = "每页查询数量", required = true)
  @GetMapping("/alarms/active")
  public Result<PageResult<Alarm>> getActiveAlarms(@RequestParam("currentPage") Integer currentPage,
                                                   @RequestParam("pageSize") Integer pageSize) {
    return Result.ok(service.allByPageAndEnd(currentPage, pageSize, false));
  }

  @Operation(summary = "查询单个告警")
  @GetMapping("/{id}")
  public Result<Alarm> getById(@PathVariable("id") Integer id) {
    return Result.ok(service.getById(id));
  }

  @Operation(summary = "查询设备告警总数")
  @GetMapping("/device/num-alarms")
  public Result<DeviceAlarmsDto> getDeviceAlarmsNum(@RequestParam("masterId") String masterId,
                                                    @RequestParam("subSystem")
                                                    SubSystemType systemType) {
    Collection<Alarm> alarms = service.allAlarmByEnd(false);
    DeviceAlarmsDto alarmsDto = new DeviceAlarmsDto();
    if (alarms != null) {
      for (Alarm alarm : alarms) {
        if (alarm.getMasterId().equals(masterId)) {
          countAlarms(alarmsDto, alarm.getAlarmLevel());
        }
      }
    }
    return Result.ok(alarmsDto);
  }

  @Operation(summary = "批量查询设备告警总数")
  @GetMapping("/devices/num-alarms")
  public Result<Map<String, DeviceAlarmsDto>> getDevicesAlarmsNum() {
    Collection<Alarm> alarms = service.allAlarmByEnd(false);
    if (alarms == null || alarms.isEmpty()) {
      return Result.ok(Collections.emptyMap());
    }

    Map<String, DeviceAlarmsDto> alarmNumMap = new ConcurrentHashMap<>();

    alarms.parallelStream()
            .filter(alarm -> alarm.getMasterId() != null && alarm.getDeviceId() != null)
            .forEach(alarm -> {
              // 处理masterId维度
              alarmNumMap.computeIfAbsent(alarm.getMasterId(), k -> new DeviceAlarmsDto());
              countAlarms(alarmNumMap.get(alarm.getMasterId()), alarm.getAlarmLevel());

              // 处理deviceId维度
              alarmNumMap.computeIfAbsent(alarm.getDeviceId(), k -> new DeviceAlarmsDto());
              countAlarms(alarmNumMap.get(alarm.getDeviceId()), alarm.getAlarmLevel());
            });

    return Result.ok(alarmNumMap);
  }

  @Operation(summary = "批量对告警静音")
  @PutMapping("/mute-alarms")
  public Result<String> muteAlarms(@RequestBody Integer[] alarmIds,
                                   HttpServletRequest request) {
    for (Integer alarmId : alarmIds) {
      service.setAlarmMuteValue(System.currentTimeMillis(),
          ServletUtils.getPersonnelName(request), alarmId);
    }
    return Result.ok();
  }

  @Operation(summary = "批量确认告警")
  @PutMapping("/confirm-alarms")
  public Result<String> confirmAlarms(@RequestBody HandleAlarmDto handleAlarmDto,
                                      HttpServletRequest request) {
    for (Integer alarmId : handleAlarmDto.getAlarmIds()) {
      service.setAlarmAckValue(System.currentTimeMillis(),
          ServletUtils.getPersonnelName(request), alarmId);
    }
    return Result.ok();
  }

  @Operation(summary = "批量强制结束告警")
  @PutMapping("/force-end-alarms")
  public Result<String> forceEndAlarms(@RequestBody HandleAlarmDto handleAlarmDto,
                                       HttpServletRequest request) {
    for (Integer alarmId : handleAlarmDto.getAlarmIds()) {
      service.setAlarmEndValue(System.currentTimeMillis(),
          ServletUtils.getPersonnelName(request), alarmId, handleAlarmDto.getAckDesc());
    }
    return Result.ok();
  }

  private void countAlarms(DeviceAlarmsDto alarmsDto, SubSystemType.AlarmLevel alarmLevel) {
    alarmsDto.setTotalAlarms(alarmsDto.getTotalAlarms() + 1);
    switch (alarmLevel) {
      case LEVEL_1 -> alarmsDto.setLevel1Alarms(alarmsDto.getLevel1Alarms() + 1);
      case LEVEL_2 -> alarmsDto.setLevel2Alarms(alarmsDto.getLevel2Alarms() + 1);
      case LEVEL_3 -> alarmsDto.setLevel3Alarms(alarmsDto.getLevel3Alarms() + 1);
      case LEVEL_4 -> alarmsDto.setLevel4Alarms(alarmsDto.getLevel4Alarms() + 1);
      default -> log.error("alarm level is not be set!");
    }
  }
}
