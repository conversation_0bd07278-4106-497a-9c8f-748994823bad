package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Picture;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.PictureService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * .
 */
@Slf4j
@Tag(name = "图片管理")
@RestController
@RequestMapping(ResUrlDef.PIC)
public class PictureController extends SkyLinkController<Picture, PictureService> {

  @Operation(summary = "保存图片并返回图片的hash")
  @PostMapping("/upload")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
              @SchemaProperty(
                      name = "multipartFile",
                      schema = @Schema(type = "string", format = "binary"))})})
  public Result<String> uploadPicture(MultipartFile multipartFile) {
    try {
      return service.saveMultipartFile(multipartFile);
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return Result.failure("Failed to upload the multipartFile.", ResponseCode.EX_FAILURE_500);
    }
  }
}
