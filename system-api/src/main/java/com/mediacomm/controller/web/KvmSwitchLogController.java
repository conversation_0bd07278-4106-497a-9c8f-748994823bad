package com.mediacomm.controller.web;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmSwitchLog;
import com.mediacomm.entity.vo.KvmSwitchLogVo;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmSwitchLogService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "KVM切换日志")
@RestController
@RequestMapping(ResUrlDef.SWITCH_LOG)
@Slf4j
public class KvmSwitchLogController extends SkyLinkController<KvmSwitchLog, KvmSwitchLogService> {
  @Autowired
  KvmMasterService masterService;
  @Operation(summary = "切换日志分页查询")
  @GetMapping("/switch-logs/page")
  public Result<PageResult<KvmSwitchLogVo>> getByPage(@RequestParam("currentPage")
                                                      Integer currentPage,
                                                      @RequestParam("pageSize") Integer pageSize,
                                                      @RequestParam("startTime") long startDate,
                                                      @RequestParam("endTime") long endDate,
                                                      @RequestParam(name = "rxName",
                                                          required = false) String rxName,
                                                      @RequestParam(name = "txGroupId",
                                                          required = false) String txName) {
    return Result.ok(service.allByPage(currentPage, pageSize, startDate, endDate, rxName, txName));
  }

  @GetMapping("/switch-logs/export")
  public void exportExcel(HttpServletResponse response,
                          @RequestParam long startDate,
                          @RequestParam long endDate) {
    try {
      //创建xlsx格式的
      ExcelWriter writer = ExcelUtil.getWriter(true);
      response.setContentType("application/vnd.ms-excel;charset=utf-8");
      response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
      response.setHeader("Content-Disposition", "attachment;filename=switch-logs.xlsx");
      ServletOutputStream out = response.getOutputStream();
      writer.write(service.allBetweenStartAndEndTime(startDate, endDate), true);
      writer.flush(out);
      writer.close();
      IoUtil.close(out);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

}
