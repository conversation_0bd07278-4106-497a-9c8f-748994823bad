package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.vo.KvmSlotVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmSlotService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "Kvm板卡配置")
@RestController
@RequestMapping(ResUrlDef.SLOT)
public class KvmSlotController extends SkyLinkController<KvmSlot, KvmSlotService> {

  @Operation(summary = "获取所有板卡或根据主机Id进行筛选")
  @GetMapping("/kvm-slots")
  public Result<Collection<KvmSlotVo>> getKvmSlots(@RequestParam(value = "masterId",
                                                    required = false) String masterId) {
    return Result.ok(service.allByMaster(masterId));
  }

  @OperationLogRecord(title = "删除板卡", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定板卡")
  @DeleteMapping("/{id}")
  public Result<?> delKvmSlotById(@PathVariable String id) {
    service.removeById(id);
    return Result.ok();
  }
}
