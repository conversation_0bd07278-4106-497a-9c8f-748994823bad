package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DefaultPicture;
import com.mediacomm.entity.dao.Picture;
import com.mediacomm.entity.dao.PictureMaterial;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.PictureMaterialService;
import com.mediacomm.system.service.PictureService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.FileType;
import com.mediacomm.util.SystemDefaultImageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * .
 */
@Slf4j
@Tag(name = "图片素材库", description = "管理系统内的图片素材")
@RestController
@RequestMapping(ResUrlDef.PIC_MATERIAL)
public class PictureMaterialController
        extends SkyLinkController<PictureMaterial, PictureMaterialService> {
  @Autowired
  PictureService pictureService;
  @Autowired
  SystemDefaultImageUtils defaultImageUtils;

  @Operation(summary = "上传可视化配置图片")
  @Parameter(name = "folderId", description = "目录Id")
  @PostMapping("/pic/upload")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
              @SchemaProperty(
                      name = "multipartFile",
                      schema = @Schema(type = "string", format = "binary")
              )})})
  public Result<String> uploadImage(MultipartFile multipartFile,
                                    @RequestParam("folderId") Integer folderId) {
    PictureMaterial folder = service.getById(folderId);
    if (folderId == null || folder == null || folder.getType() != FileType.FOLDER) {
      return Result.failure("No folder!", ResponseCode.EX_FAILURE_400);
    }
    try {
      Result<String> res = pictureService.saveMultipartFile(multipartFile);
      if (!Objects.equals(res.getCode(), ResponseCode.EX_OK_200)) {
        return res;
      }
      long pictureId = Long.parseLong(res.getResult());
      PictureMaterial imageInfo = new PictureMaterial();
      imageInfo.setName(multipartFile.getOriginalFilename());
      imageInfo.setType(FileType.IMAGE);
      imageInfo.setImageId(pictureId);
      imageInfo.setParentId(folderId);
      service.save(imageInfo);
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return Result.failure("Upload image failed!", ResponseCode.EX_FAILURE_500);
    }
    return Result.ok();
  }

  @Operation(summary = "获取图片素材库的所有内容")
  @GetMapping("/materials")
  public Result<List<PictureMaterial>> getMaterials() {
    System.out.println("service.list() = " + service.list());
    return Result.ok(service.list());
  }

  @Operation(summary = "根据图片的哈希值获取图片内容")
  @GetMapping(value = "/pic", produces = {MediaType.IMAGE_PNG_VALUE,
          MediaType.IMAGE_JPEG_VALUE, MediaType.IMAGE_GIF_VALUE})
  public byte[] getImage(@RequestParam("hash") @NotNull long hash) {
    Picture picture = pictureService.getById(hash);
    return picture == null ? null : picture.getContent();
  }

  @Operation(summary = "创建配置图片目录")
  @PostMapping("/folder")
  public Result<String> mkdirForImage(@RequestParam @NotNull String folderName,
                                      @RequestParam @NotNull Integer pid) {
    PictureMaterial folder = new PictureMaterial();
    folder.setName(folderName);
    folder.setParentId(pid);
    folder.setType(FileType.FOLDER);
    service.save(folder);
    return Result.ok();
  }

  @Operation(summary = "修改素材名称")
  @PutMapping("/{materialId}")
  public Result<String> updateMaterialName(@PathVariable Integer materialId,
                                           @RequestParam String name) {
    PictureMaterial image = service.getById(materialId);
    if (image == null) {
      return Result.failure("Material not found!", ResponseCode.EX_NOTFOUND_404);
    }
    image.setName(name);
    service.updateById(image);
    return Result.ok();
  }

  @Operation(summary = "删除素材")
  @DeleteMapping("{materialId}")
  public Result<String> deleteMaterial(@PathVariable Integer materialId) {
    PictureMaterial material = service.getById(materialId);
    if (material != null) {
      service.removeById(materialId);
      Collection<Long> imageIds = service.allImageIds(material.getImageId());
      int size = imageIds == null ? 0 : imageIds.size();
      if (material.getType() == FileType.IMAGE && size == 0) {
        pictureService.removeById(material.getImageId());
      }
    }
    return Result.ok();
  }

  @Operation(summary = "获取可视化控件默认图")
  @GetMapping("/visualization/default/pic")
  public Result<DefaultPicture> getVisualizationDefaultPic(@RequestParam String imageName) {
    return Result.ok(defaultImageUtils.getDefaultImage(imageName));
  }

}
