package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Permission;
import com.mediacomm.entity.dao.Role;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.PermissionService;
import com.mediacomm.system.service.RoleService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import java.util.HashSet;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "角色管理", description = "权限的集合")
@RestController
@RequestMapping(ResUrlDef.ROLE)
public class RoleController extends SkyLinkController<Role, RoleService> {
  @Autowired
  PermissionService permissionService;

  @Operation(summary = "获取所有角色信息")
  @GetMapping("/roles")
  public Result<Collection<Role>> getRoles() {
    Collection<Role> roles = service.list();
    roles.forEach(role -> {
      role.setPermissions(new HashSet<>(permissionService.allByRoleId(role.getRoleId())));
    });
    return Result.ok(roles);
  }

  @Operation(summary = "获取所有权限信息")
  @GetMapping("/permissions")
  public Result<Collection<Permission>> getPermissions() {
    return Result.ok(permissionService.list());
  }

  @Operation(summary = "通过Id获取指定角色信息")
  @GetMapping("{roleId}")
  public Result<Role> getRoleById(@PathVariable("roleId") Integer roleId) {
    Role role = service.getById(roleId);
    role.setPermissions(new HashSet<>(permissionService.allByRoleId(role.getRoleId())));
    return Result.ok(role);
  }

  @Operation(summary = "通过Id修改指定角色信息")
  @PutMapping("{roleId}")
  public Result<String> updateRoleById(@PathVariable("roleId") Integer roleId,
                                  @RequestBody Role role) {
    Role oldRole = service.getById(roleId);
    oldRole.setPermissions(new HashSet<>(permissionService.allByRoleId(oldRole.getRoleId())));
    service.updateById(role);
    if (oldRole.getPermissions() != null && !oldRole.getPermissions().isEmpty()) {
      permissionService.delBatchPermissionByRoleId(roleId);
    }
    if (role.getPermissions() != null && !role.getPermissions().isEmpty()) {
      permissionService.saveBatchPermissionByRoleId(roleId, role.getPermissions().stream().map(
          Permission::getMenuId).collect(Collectors.toSet()));
    }
    return Result.ok();
  }

  @Operation(summary = "新增角色")
  @PostMapping
  public Result<String> addRole(@RequestBody Role role) {
    service.save(role);
    Role r = service.oneByName(role.getName());
    if (role.getPermissions() != null && !role.getPermissions().isEmpty()) {
      permissionService.saveBatchPermissionByRoleId(r.getRoleId(),
          role.getPermissions().stream()
              .map(Permission::getMenuId).collect(Collectors.toSet()));
    }
    return Result.ok();
  }

  @Operation(summary = "通过Id删除指定角色信息")
  @DeleteMapping("{roleId}")
  public Result<String> delRoleById(@PathVariable Integer roleId) {
    service.removeById(roleId);
    return Result.ok();
  }

}
