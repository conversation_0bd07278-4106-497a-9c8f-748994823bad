package com.mediacomm.controller.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.PeripheralUpgradePackage;
import com.mediacomm.entity.dao.PeripheralUpgradeTask;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.IdsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PeripheralUpgradeIdRequestBody;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceModelService;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.PeripheralUpgradePackageService;
import com.mediacomm.system.service.PeripheralUpgradeService;
import com.mediacomm.system.service.PeripheralUpgradeTaskService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.system.variable.sysenum.UpgradeTaskStatus;
import com.mediacomm.util.BuildMqRequestBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.JwtUtils;
import com.mediacomm.util.PeripheralUpgradeUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 外设升级控制器.
 */
@Slf4j
@Tag(name = "外设升级管理")
@RestController
@RequestMapping(ResUrlDef.PERIPHERAL_UPGRADE)
public class PeripheralUpgradeController extends SkyLinkController<PeripheralUpgradePackage, PeripheralUpgradePackageService> {

  @Autowired
  private PeripheralUpgradeTaskService taskService;
  @Autowired
  private KvmAssetService assetService;
  @Autowired
  private PeripheralUpgradeService upgradeService;
  @Autowired
  private DeviceModelService modelService;
  @Autowired
  private RpcSenderUtils senderUtils;

  @OperationLogRecord(title = "上传外设升级包", operateType = OperateType.INSERT)
  @Operation(summary = "上传外设升级包")
  @PostMapping("/upload")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
                  @SchemaProperty(
                          name = "file",
                          schema = @Schema(type = "string", format = "binary")),
                  @SchemaProperty(
                          name = "deviceType",
                          schema = @Schema(type = "string"))})})
  public Result<String> uploadUpgradePackage(MultipartFile file, String deviceType) {
    DeviceType dt;
    try {
      dt = DeviceType.valueOf(deviceType);
    } catch (IllegalArgumentException e) {
      return Result.failure("Invalid device type.", ResponseCode.EX_FAILURE_400);
    }
    PeripheralUpgradePackage upgradePackage = new PeripheralUpgradePackage();
    // 验证升级包
    if (!PeripheralUpgradeUtils.validateUpgradePackage(file, dt, upgradePackage)) {
      return Result.failure("Upgrade package validation failed.", ResponseCode.EX_FAILURE_400);
    }
    // 计算MD5
    String md5 = PeripheralUpgradeUtils.calculateMd5(file);
    if (StringUtils.isBlank(md5)) {
      return Result.failure("Failed to calculate file MD5.", ResponseCode.EX_FAILURE_400);
    }
    // 检查是否已存在相同MD5的升级包
    PeripheralUpgradePackage existingPackage = service.getByMd5(md5);
    if (existingPackage != null) {
      return Result.failure("The same upgrade package already exists.", ResponseCode.EX_FAILURE_400);
    }
    // 创建存储目录
    if (!PeripheralUpgradeUtils.createUpgradeDirectory(upgradePackage.getDeviceType())) {
      return Result.failure("Failed to create a storage directory.", ResponseCode.EX_FAILURE_500);
    }

    // 保存文件
    String originalFilename = file.getOriginalFilename();
    String filePath = PeripheralUpgradeUtils.getUpgradePackagePath()
            + upgradePackage.getDeviceType() + "/" + md5 + "_" + originalFilename;
    File destFile = new File(filePath);
    try (InputStream inputStream = file.getInputStream();
         OutputStream outputStream = new FileOutputStream(destFile)) {
      inputStream.transferTo(outputStream);
    } catch (IOException e) {
      log.error("Failed to save the upgrade package file using streams", e);
      return Result.failure("Failed to save the upgrade package file.", ResponseCode.EX_FAILURE_500);
    }

    // 保存升级包信息到数据库
    upgradePackage.setPackageName(originalFilename);
    upgradePackage.setFileName(md5 + "_" + originalFilename);
    upgradePackage.setFilePath(filePath);
    upgradePackage.setMd5(md5);
    upgradePackage.setFileSize(file.getSize());
    upgradePackage.setUploadTime(System.currentTimeMillis());

    service.save(upgradePackage);

    // 清理旧的升级包
    PeripheralUpgradeUtils.cleanOldPackages(upgradePackage.getDeviceType());

    return Result.ok("上传成功");
  }

  @Operation(summary = "获取升级包列表")
  @GetMapping("/packages")
  public Result<Collection<PeripheralUpgradePackage>> getUpgradePackages(
          @RequestParam(value = "deviceType", required = false) DeviceType deviceType) {
    if (deviceType == null) {
      return Result.ok(service.list());
    }
    return Result.ok(service.listByDeviceType(deviceType));
  }

  @Operation(summary = "删除升级包")
  @DeleteMapping("/package/{id}")
  public Result<String> deleteUpgradePackage(@PathVariable Integer id) {
    PeripheralUpgradePackage upgradePackage = service.getById(id);
    if (upgradePackage == null) {
      return Result.failure("The upgrade package does not exist.", ResponseCode.EX_NOTFOUND_404);
    }
    File file = new File(upgradePackage.getFilePath());
    if (file.exists() && !file.delete()) {
      log.warn("Failed to delete the upgrade package file: {}", upgradePackage.getFilePath());
    }
    service.removeById(id);
    return Result.ok();
  }

  @Operation(summary = "获取可升级的设备型号")
  @GetMapping("/device/models")
  public Result<Collection<DeviceModel>> getUpgradeableDeviceTypes() {
    Collection<DeviceModel> models = new ArrayList<>();
    for (DeviceType deviceType : PeripheralUpgradeUtils.supportDeviceTypes()) {
      models.add(modelService.getById(deviceType.getDeviceTypeId()));
    }
    return Result.ok(models);
  }

  @Operation(summary = "获取可升级的设备列表")
  @GetMapping("/devices")
  public Result<PageResult<KvmAssetVo>> getUpgradeableDevices(@RequestParam("currentPage") Integer currentPage,
                                                              @RequestParam("pageSize") Integer pageSize,
                                                              @RequestParam("deviceType") DeviceType deviceType) {
    if (!PeripheralUpgradeUtils.isSupportDeviceType(deviceType)) {
      return Result.failure("Invalid device type.", ResponseCode.EX_FAILURE_400);
    }
    Page<KvmAsset> assets = assetService.page(new Page<>(currentPage, pageSize));
    assets.getRecords().removeIf(asset -> StringUtils.isBlank(asset.getDeviceIp()));
    Map<String, List<String>> ids = assets.getRecords().stream()
            .collect(Collectors.groupingBy(
                    KvmAsset::getMasterId, // 第一级：按 masterId 分组
                    Collectors.mapping(
                            KvmAsset::getAssetId,   // 第二级：将每个分组内的 KvmAsset 映射为其 assetId
                            Collectors.toList()       // 第三级：将映射后的 assetId 收集成一个 List
                    )));
    ids.forEach((k, v) -> {
      MqRequest<IdsRequestBody> mqRequest = new MqRequest<>();
      mqRequest.setMasterId(k);
      IdsRequestBody ir = new IdsRequestBody(v);
      mqRequest.setBody(ir);
      senderUtils.send(k, RoutingOperation.ASSET_VERSION_GET, mqRequest);
    });
    PageResult<KvmAssetVo> vos = assetService.allByPage(currentPage, pageSize, deviceType);
    vos.getContent().removeIf(asset -> StringUtils.isBlank(asset.getDeviceIp()));
    return Result.ok(vos);
  }

  @OperationLogRecord(title = "创建升级任务", operateType = OperateType.INSERT)
  @Operation(summary = "创建升级任务")
  @PostMapping("/task")
  public Result<String> createUpgradeTask(@RequestBody List<String> deviceIds,
                                          @RequestParam("packageId") Integer packageId,
                                          HttpServletRequest request) {
    if (deviceIds == null || deviceIds.isEmpty()) {
      return Result.failure("The list of device IDs cannot be empty.", ResponseCode.EX_FAILURE_400);
    }
    PeripheralUpgradePackage upgradePackage = service.getById(packageId);
    if (upgradePackage == null) {
      return Result.failure("The upgrade package does not exist.", ResponseCode.EX_NOTFOUND_404);
    }
    // 检查文件是否存在
    File packageFile = new File(upgradePackage.getFilePath());
    if (!packageFile.exists()) {
      return Result.failure("The upgrade package file does not exist.", ResponseCode.EX_FAILURE_400);
    }
    // 获取服务器地址
    String serverAddress = request.getScheme() + "://" + request.getServerName(); // 80 nginx -> 8181 java -> nginx
    String downloadUrl = serverAddress + ResUrlDef.PERIPHERAL_UPGRADE + "/download/" + upgradePackage.getId();
    // 使用升级服务创建升级任务
    boolean res = upgradeService.createUpgradeTasks(deviceIds, packageId, downloadUrl,
            request.getHeader(JwtUtils.AUTHORIZATION));
    return res ? Result.ok() :
            Result.failure("No effective equipment found.", ResponseCode.EX_FAILURE_400);
  }

  @Operation(summary = "手动更新升级失败的任务的状态")
  @PutMapping("/task/{id}")
  public Result<?> updateFailedTaskStatus(@PathVariable int id) {
    PeripheralUpgradeTask task = taskService.getById(id);
    if (task == null || !Objects.equals(task.getStatus(), UpgradeTaskStatus.TIMEOUT.getCode())) {
      return Result.failure("The task does not exist or is not a failed task.", ResponseCode.EX_NOTFOUND_404);
    }
    PeripheralUpgradeIdRequestBody idRequestBody = new PeripheralUpgradeIdRequestBody(task.getDeviceId(), task.getId());
    return doCmd(task.getDeviceId(), RoutingOperation.CHECK_UPGRADE_PACKAGE, idRequestBody);
  }

  @Operation(summary = "获取升级任务列表")
  @GetMapping("/tasks")
  public Result<PageResult<PeripheralUpgradeTask>> getUpgradeTasks(
          @RequestParam("currentPage") Integer currentPage,
          @RequestParam("pageSize") Integer pageSize,
          @RequestParam(value = "packageId", required = false) Integer packageId,
          @RequestParam(value = "deviceId", required = false) String deviceId,
          @RequestParam(value = "deviceType", required = false) DeviceType deviceType) {
    return Result.ok(taskService.allByPage(packageId, deviceId, deviceType, currentPage, pageSize));
  }

  @Operation(summary = "获取升级任务详情")
  @GetMapping("/task/{id}")
  public Result<PeripheralUpgradeTask> getUpgradeTask(@PathVariable Integer id) {
    PeripheralUpgradeTask task = taskService.getById(id);
    if (task == null) {
      return Result.failure("The upgrade task does not exist.", ResponseCode.EX_NOTFOUND_404);
    }
    return Result.ok(task);
  }

  @Operation(summary = "获取指定设备的升级任务详情")
  @PostMapping("/tasks/devices")
  public Result<Collection<PeripheralUpgradeTask>> getUpgradeTasksByDevice(@RequestBody List<String> deviceIds) {
    if (deviceIds == null || deviceIds.isEmpty()) {
      return Result.failure("The list of device IDs cannot be empty.", ResponseCode.EX_FAILURE_400);
    }
    return Result.ok(taskService.allByDeviceIds(deviceIds));
  }

  @OperationLogRecord(title = "取消升级任务", operateType = OperateType.UPDATE)
  @Operation(summary = "取消升级任务")
  @PostMapping("/task/{id}/cancel")
  public Result<String> cancelUpgradeTask(@PathVariable Integer id) {
    if (upgradeService.cancelUpgradeTask(id)) {
      return Result.ok("The upgrade task was canceled.");
    } else {
      return Result.failure("Failed to cancel the upgrade task.", ResponseCode.EX_FAILURE_400);
    }
  }

  @Operation(summary = "下载升级包")
  @GetMapping("/download/{id}")
  public void downloadUpgradePackage(@PathVariable Integer id, HttpServletResponse response)
          throws IOException {
    PeripheralUpgradePackage upgradePackage = service.getById(id);
    if (upgradePackage == null ) {
      response.sendError(HttpServletResponse.SC_NOT_FOUND, "Upgrade package info not found.");
      return;
    }

    File file = new File(upgradePackage.getFilePath());
    if (!file.exists()) {
      response.sendError(HttpServletResponse.SC_NOT_FOUND, "Upgrade package not found.");
      return;
    }
    String relativePath = file.getPath().substring("/var/data/peripheral_upgrade/".length());
    if (relativePath.startsWith("/")) {
      relativePath = relativePath.substring(1);
    }
    String internalPath = "/protected-downloads/" + relativePath;
    response.setContentType("application/octet-stream");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + upgradePackage.getFileName() + "\"");
    // nginx X-Accel-Redirect
    response.setHeader("X-Accel-Redirect", internalPath);
    response.setStatus(HttpServletResponse.SC_OK);
  }

  private <T> Result<T> doCmd(String assetId, String topicDto, T requestBody) {
    KvmAsset asset = assetService.getById(assetId);
    if (asset == null) {
      return Result.failure("No exist asset!", ResponseCode.EX_FAILURE_400);
    }
    return JsonUtils.decode(senderUtils.send(asset.getMasterId(), topicDto,
            BuildMqRequestBodyUtils.buildKvmAssetMqBody(asset, requestBody)), new TypeReference<>() {});
  }
}
