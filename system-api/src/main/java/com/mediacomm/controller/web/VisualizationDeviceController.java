package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.VisualizationDevice;
import com.mediacomm.entity.vo.VisualizationDeviceVo;
import com.mediacomm.pojo.VisualizationDeviceInfoDto;
import com.mediacomm.pojo.VisualizationDeviceStatus;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceModelService;
import com.mediacomm.system.service.VisualizationDeviceService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Slf4j
@Tag(name = "可视化终端")
@RestController
@RequestMapping(ResUrlDef.VIS_DEVICE)
public class VisualizationDeviceController
    extends SkyLinkController<VisualizationDevice, VisualizationDeviceService> {
  @Autowired
  DeviceModelService modelService;
  @Autowired
  RedisUtil redisUtil;

  @Operation(summary = "获取可视化终端设备列表")
  @GetMapping("/visualization-devices")
  public Result<Collection<VisualizationDeviceVo>> getVisualizationDevices() {
    return Result.ok(service.all());
  }

  @Operation(summary = "添加可视化终端设备")
  @PostMapping
  public Result<VisualizationDevice> addVisualizationDevice(@RequestBody @Validated
                                                    VisualizationDeviceInfoDto deviceInfo,
                                                    HttpServletRequest request) {
    DeviceModel model = modelService.oneByDeviceType(deviceInfo.getDeviceType());
    if (model == null) {
      Optional<DeviceModel> modelExistExtOption =
          modelService.allBySubSystem(SubSystemType.VISUALIZATION_TERMINAL)
          .stream().filter(m -> !m.getExtOption().isEmpty()).findFirst();
      if (modelExistExtOption.isPresent()) {
        model = DeviceModel.builder()
            .deviceType(deviceInfo.getDeviceType())
            .subSystem(SubSystemType.VISUALIZATION_TERMINAL)
            .manufacturer(modelExistExtOption.get().getManufacturer())
            .extOption(modelExistExtOption.get().getExtOption())
            .modelName(deviceInfo.getDeviceType())
            .maxUseAge(modelExistExtOption.get().getMaxUseAge())
            .properties(modelExistExtOption.get().getProperties())
            .extProperties(modelExistExtOption.get().getExtProperties())
            .build();
        modelService.save(model);
      } else {
        return Result.failure("Failed to generate new device model!",
            ResponseCode.EX_FAILURE_500, null);
      }
    }
    VisualizationDevice device = service.oneByHardcode(deviceInfo.getSn());
    if (device == null) {
      device = VisualizationDevice.builder()
          .hardcode(deviceInfo.getSn())
          .deviceModel(model.getModelId())
          .deviceIp(request.getRemoteAddr())
          .properties(new ArrayList<>())
          .name(deviceInfo.getSn())
          .version(deviceInfo.getVersion())
          .build();
      service.save(device);
    }
    return Result.ok(device);
  }

  @Operation(summary = "修改可视化终端设备")
  @PutMapping("/{id}")
  public Result<String> updateVisualizationDevice(@PathVariable Integer id,
                               @RequestBody VisualizationDevice device) {
    device.setId(id);
    service.updateById(device);
    return Result.ok();
  }

  @Operation(summary = "删除可视化终端设备")
  @DeleteMapping("/{id}")
  public Result<String> delVisualizationDevice(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @Operation(summary = "上传可视化终端设备状态")
  @PostMapping("/status")
  public Result<String> uploadVisualizationDeviceStatus(@RequestBody
                                                        VisualizationDeviceStatus status) {
    String redisKey = RedisUtil.redisKey(RedisKey.VIS, "device.status", status.getSn());
    redisUtil.set(redisKey, JsonUtils.encode(status));
    return Result.ok();
  }
}
