package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Alarm;
import com.mediacomm.entity.dao.AlarmRepairDesc;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.AlarmRepairDescService;
import com.mediacomm.system.service.AlarmService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.ServletUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "告警处理日志")
@RestController
@RequestMapping(ResUrlDef.ALARM_REPAIR_DESC)
public class AlarmRepairDescController
    extends SkyLinkController<AlarmRepairDesc, AlarmRepairDescService> {
  @Autowired
  AlarmService alarmService;

  @Operation(summary = "添加告警处理日志")
  @PostMapping
  public Result<String> addAlarmRepairDesc(@RequestParam("alarmId") Integer alarmId,
                                           @RequestBody String repairDesc,
                                           HttpServletRequest request) {
    Alarm alarm = alarmService.getById(alarmId);
    if (alarm == null) {
      return Result.failure("No found alarm " + alarmId + "!",
          ResponseCode.EX_NOTFOUND_404);
    }
    AlarmRepairDesc alarmRepairDesc = new AlarmRepairDesc();
    alarmRepairDesc.setAlarmId(alarmId);
    alarmRepairDesc.setRepairTime(System.currentTimeMillis());
    alarmRepairDesc.setRepairDesc(repairDesc);
    alarmRepairDesc.setRepairByPerson(ServletUtils.getPersonnelName(request));
    service.save(alarmRepairDesc);
    return Result.ok();
  }

  @Operation(summary = "根据告警Id获取处理日志")
  @GetMapping
  public Result<Collection<AlarmRepairDesc>> getAlarmRepairDescByAlarmId(@RequestParam("alarmId")
                                                                           Integer alarmId) {
    return Result.ok(service.allByAlarmId(alarmId));
  }
}
