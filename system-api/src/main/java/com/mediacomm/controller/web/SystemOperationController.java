package com.mediacomm.controller.web;

import cn.hutool.core.io.FileUtil;
import com.mediacomm.entity.Result;
import com.mediacomm.service.DbBackupServiceImpl;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.ServletUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * .
 */
@Tag(name = "系统安全运维")
@RestController
@RequestMapping(ResUrlDef.SYSTEM_SEC_OPT)
@Slf4j
public class SystemOperationController {
  @Autowired
  private DbBackupServiceImpl dbBackupService;

  @Operation(summary = "备份数据库")
  @PostMapping("/database/backup")
  public void dbBackup(HttpServletResponse response,
                                 @RequestBody String currentPwd,
                                 HttpServletRequest request) {
    boolean auth = authPwd(currentPwd, request);
    if (auth) {
      dbBackupService.dbBackup();
      try (InputStream inputStream = new FileInputStream(dbBackupService.getDbDataZipName());
           ServletOutputStream outputStream = response.getOutputStream()) {
        String dbDataZipName = dbBackupService.getDbDataZipName();
        String suffix = FileUtil.getSuffix(dbDataZipName);
        response.setContentType("application/" + suffix);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + dbDataZipName);
        byte[] buffer = new byte[inputStream.available()];
        outputStream.write(buffer, 0, inputStream.read(buffer));
        outputStream.flush();
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        response.setStatus(ResponseCode.EX_FAILURE_500);
      }
    } else {
      response.setStatus(ResponseCode.EX_FAILURE_400);
    }
  }

  @OperationLogRecord(title = "恢复数据库", operateType = OperateType.UPDATE)
  @Operation(summary = "恢复数据库")
  @PostMapping("/database/rollBack")
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = {@Content(
          mediaType = "multipart/form-data",
          schema = @Schema(type = "object"),
          schemaProperties = {
              @SchemaProperty(
                      name = "multipartFile",
                      schema = @Schema(type = "string", format = "binary")
              ),
              @SchemaProperty(
                      name = "currentPwd",
                      schema = @Schema(type = "string")
              )
          })})
  public Result<String> dbRestore(MultipartFile multipartFile, String currentPwd,
                                  HttpServletRequest request) {
    boolean auth = authPwd(currentPwd, request);
    if (auth) {
      try {
        String fileName = multipartFile.getOriginalFilename();
        if (StringUtils.isNotEmpty(fileName)) {
          String fileSuffix = FileUtil.getSuffix(fileName);
          if (!fileSuffix.equals("zip")) {
            return Result.failure("The file suffix is incorrect.", ResponseCode.EX_FAILURE_400);
          }
          String dest = "/tmp/" + fileName;
          multipartFile.transferTo(new File(dest));
          dbBackupService.dbRestore(dest);
        } else {
          return Result.failure("Empty file name.", ResponseCode.EX_FAILURE_400);
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        return Result.failure("Upload db_file failed.", ResponseCode.EX_FAILURE_500);
      }
      return Result.ok();
    } else {
      return Result.failure("The password authentication fails.", ResponseCode.EX_FAILURE_400);
    }
  }

  @OperationLogRecord(title = "清空系统缓存", operateType = OperateType.DELETE)
  @Operation(summary = "清空系统缓存")
  @PostMapping("/cache/clear")
  public Result<String> cacheClear(@RequestBody String currentPwd, HttpServletRequest request) {
    boolean auth = authPwd(currentPwd, request);
    if (auth) {
      dbBackupService.redisClear();
      return Result.ok();
    } else {
      return Result.failure("The password authentication fails.", ResponseCode.EX_FAILURE_400);
    }
  }

  private boolean authPwd(String currentPwd, HttpServletRequest request) {
    String pwd = ServletUtils.getPwd(request);
    return !StringUtils.isEmpty(pwd) && pwd.equals(currentPwd);
  }
}
