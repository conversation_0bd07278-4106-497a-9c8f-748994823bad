package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.VisualizationScene;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DeviceModelService;
import com.mediacomm.system.service.VisualizationSceneService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.system.variable.sysenum.VisualizationSceneType;
import com.mediacomm.util.WebSocketHandlerSendNoticeUtils;
import com.mediacomm.util.WebSocketTopic;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Slf4j
@Tag(name = "可视化场景", description = "移动端可视化创景配置的API")
@RestController
@RequestMapping(ResUrlDef.VIS_SCENE)
public class VisualizationSceneController extends
    SkyLinkController<VisualizationScene, VisualizationSceneService> {

  @Autowired
  DeviceModelService modelService;
  @Autowired
  WebSocketHandlerSendNoticeUtils noticeUtils;

  @Operation(summary = "获取可视化终端设备类型的所有设备型号")
  @GetMapping("/visualization-device-types")
  public Result<Collection<DeviceModel>> getVisualizationDevices() {
    return Result.ok(modelService.allBySubSystem(SubSystemType.VISUALIZATION_TERMINAL));
  }

  @Operation(summary = "获取指定房间及可视化终端设备型号的所有场景")
  @Parameter(name = "roomId", description = "房间Id")
  @Parameter(name = "deviceModel", description = "设备型号Id")
  @GetMapping("/visualization-scenes")
  public Result<Collection<VisualizationScene>> getVisualizationScenesByRoomAndModel(
                                                             @RequestParam(value = "roomId",
                                                                 required = false) Integer roomId,
                                                             @RequestParam(value = "deviceModel",
                                                                 required = false)
                                                             Integer modelId) {
    return Result.ok(service.allByRoomIdAndModel(roomId, modelId));
  }

  @Operation(summary = "根据Id获取指定场景信息")
  @GetMapping("/{id}")
  public Result<VisualizationScene> getVisualizationScene(@PathVariable Integer id) {
    return Result.ok(service.getById(id));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改场景", operateType = OperateType.UPDATE,
      requestBody = "#{scene}")
  @Operation(summary = "根据Id修改指定场景")
  @PutMapping("/{id}")
  public Result<?> updateVisualizationScene(@PathVariable Integer id,
                                            @RequestBody VisualizationScene scene) {
    scene.setSceneId(id);
    service.updateById(scene);
    noticeUtils.sendSceneChangeNotice(WebSocketTopic.EVENT_CHANGE_VIS_SCENE, id);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除场景", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定场景")
  @DeleteMapping("/{id}")
  public Result<?> delVisualizationScene(@PathVariable Integer id) {
    service.removeById(id);
    noticeUtils.sendSceneChangeNotice(WebSocketTopic.EVENT_DELETE_VIS_SCENE, id);
    return Result.ok();
  }

  @OperationLogRecord(title = "创建场景", operateType = OperateType.INSERT,
      requestBody = "#{scene}")
  @Operation(summary = "创建可视化场景")
  @PostMapping
  public Result<?> addVisualizationScene(@RequestBody VisualizationScene scene) {
    if (scene.getSceneType().equals(VisualizationSceneType.GUIDE_PAGE)) {
      Collection<VisualizationScene> guidePage =
          service.allGuidePageByModel(scene.getDeviceModel());
      if (guidePage.size() > 1) {
        return Result.failure("Duplicate boot page!", ResponseCode.EX_FAILURE_400);
      }
    }
    service.save(scene);
    return Result.ok();
  }

  @Operation(summary = "根据设备型号获取可视化引导页")
  @Parameter(name = "deviceModel", description = "设备型号Id")
  @GetMapping("/guide-page")
  public Result<VisualizationScene> getGuidePage(@RequestParam("deviceModel") Integer modelId) {
    return Result.ok(service.allGuidePageByModel(modelId).stream().toList().get(0));
  }

}
