package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.VisualizationLayout;
import com.mediacomm.entity.vo.KvmSeatVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmSeatService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "坐席管理", description = "坐席管理的API")
@RestController
@RequestMapping(ResUrlDef.KVM_SEAT)
public class KvmSeatController extends SkyLinkController<KvmSeat, KvmSeatService> {

  @Operation(summary = "获取所有坐席信息")
  @Parameter(name = "positionId", description = "坐席组Id")
  @GetMapping("/kvm-seats")
  public Result<Collection<KvmSeatVo>> getKvmSeats(@RequestParam(name = "positionId",
                                                    required = false) Integer positionId) {
    return Result.ok(service.allByPositionId(positionId));
  }

  @Operation(summary = "获取指定Id的坐席信息")
  @GetMapping("/{id}")
  public Result<KvmSeatVo> getKvmSeatById(@PathVariable Integer id) {
    return Result.ok(service.oneById(id));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改指定坐席", operateType = OperateType.UPDATE, requestBody = "#{seat}")
  @Operation(summary = "根据Id修改指定坐席组")
  @PutMapping("/{id}")
  public Result<?> updateKvmSeat(@PathVariable Integer id, @RequestBody KvmSeat seat) {
    seat.setSeatId(id);
    service.updateById(seat);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除坐席", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定坐席组")
  @DeleteMapping("/{id}")
  public Result<?> delKvmSeat(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @Operation(summary = "获取坐席当前勾选使用的布局")
  @GetMapping("/layouts")
  public Result<Collection<VisualizationLayout>> getSeatLayouts(@RequestParam("seatId")
                                                                  Integer id) {
    return Result.ok(service.allLayoutsBySeatId(id));
  }

  @Operation(summary = "增加坐席的可用布局")
  @PostMapping("/layouts")
  public Result<String> addSeatLayout(@RequestParam("seatId") Integer id,
                                      @RequestBody Collection<Integer> layoutIds) {

    service.saveBatchLayoutById(id, layoutIds);
    return Result.ok();
  }

  @Operation(summary = "根据Id删除指定坐席的布局")
  @DeleteMapping("/layouts")
  public Result<String> delVideoWallLayout(@RequestParam("seatId") Integer id,
                                           @RequestBody Collection<Integer> layoutIds) {
    service.delBatchLayoutById(id, layoutIds);
    return Result.ok();
  }

}
