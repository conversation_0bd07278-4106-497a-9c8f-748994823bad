package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Department;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.DepartmentService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.sysenum.OperateType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "部门管理", description = "部门配置的API")
@RestController
@RequestMapping(ResUrlDef.DEPARTMENT)
public class DepartmentController extends SkyLinkController<Department, DepartmentService> {

  @Operation(summary = "获取所有部门信息")
  @GetMapping("/departments")
  public Result<Collection<Department>> getDepartments() {
    return Result.ok(service.list());
  }

  @Operation(summary = "通过Id获取指定部门信息")
  @GetMapping("/{id}")
  public Result<Department> getDepartment(@PathVariable Integer id) {
    return Result.ok(service.getById(id));
  }

  @OperationLogRecord(title = "修改指定部门", operateType = OperateType.UPDATE,
      requestBody = "#{department}")
  @Operation(summary = "通过Id修改指定部门信息")
  @PutMapping("/{id}")
  public Result<Department> updateDepartment(@PathVariable Integer id,
                                             @RequestBody Department department) {
    department.setDepartmentId(id);
    return service.updateDepartmentById(department);
  }

  @OperationLogRecord(title = "删除指定部门", operateType = OperateType.DELETE)
  @Operation(summary = "通过Id删除指定部门信息")
  @DeleteMapping("/{id}")
  public Result<?> delDepartment(@PathVariable Integer id) {
    service.removeById(id);
    return Result.ok();
  }

  @OperationLogRecord(title = "批量删除指定部门", operateType = OperateType.DELETE, requestBody = "#{ids}")
  @Operation(summary = "通过Id批量删除部门信息")
  @DeleteMapping("/departments")
  public Result<?> delDepartments(@RequestBody Collection<Integer> ids) {
    service.removeBatchByIds(ids);
    return Result.ok();
  }

  @OperationLogRecord(title = "新增部门", operateType = OperateType.INSERT,
      requestBody = "#{department}")
  @Operation(summary = "新增部门信息")
  @PostMapping
  public Result<?> addDepartment(@RequestBody Department department) {
    return service.saveDepartment(department);
  }
}
