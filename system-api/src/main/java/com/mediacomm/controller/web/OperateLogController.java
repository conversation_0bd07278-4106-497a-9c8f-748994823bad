package com.mediacomm.controller.web;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.OperateLog;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.OperateLogService;
import com.mediacomm.system.variable.ResUrlDef;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "操作日志")
@RestController
@RequestMapping(ResUrlDef.OPERATION_LOG)
public class OperateLogController extends SkyLinkController<OperateLog, OperateLogService> {

  @Operation(summary = "分页查询系统操作日志，并支持按时间及账号进行筛选")
  @GetMapping("/operate-logs")
  public Result<PageResult<OperateLog>> getByPageAndOther(@RequestParam("currentPage")
                                                          Integer currentPage,
                                              @RequestParam("pageSize")
                                                          Integer pageSize,
                                              @RequestParam("startTime")
                                                          long startTime,
                                              @RequestParam("endTime")
                                                          long endTime,
                                              @RequestParam (name = "personnel",
                                                              required = false)
                                                          String personnel) {
    return Result.ok(service
        .allByPageAndOther(currentPage, pageSize, startTime, endTime, personnel));
  }
}
