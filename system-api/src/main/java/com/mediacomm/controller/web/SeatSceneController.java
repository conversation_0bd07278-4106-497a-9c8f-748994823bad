package com.mediacomm.controller.web;

import com.google.common.collect.Maps;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.SeatScene;
import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.SeatSceneService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.BuildWsEventNoticeBodyUtils;
import com.mediacomm.util.WebSocketTopic;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "坐席预案管理")
@RestController
@RequestMapping(ResUrlDef.VIS_SEAT_SCENE)
public class SeatSceneController extends SkyLinkController<SeatScene, SeatSceneService> {

  @Autowired
  private WebSocketHandler handler;

  @OperationLogRecord(title = "创建坐席预案", operateType = OperateType.INSERT,
          requestBody = "#{scene}")
  @Operation(summary = "创建预案")
  @PostMapping
  public Result<String> addScene(@RequestBody SeatScene scene) {
    service.save(scene);
    sendWebSocketEventMsg(WebSocketTopic.EVENT_CHANGE_SEAT_SCENE, scene.getUniqueSearchKey());
    return Result.ok();
  }

  @Operation(summary = "根据可视化页面key值获取相关的预案")
  @Parameter(name = "pageKey", description = "可视化页面key值")
  @GetMapping("/scenes")
  public Result<Collection<SeatScene>> getSceneByKey(@RequestParam String pageKey) {
    return Result.ok(service.allByKey(pageKey));
  }

  @Operation(summary = "根据可视化页面key值删除相关的预案")
  @DeleteMapping("/scenes")
  public Result<String> delSceneByKey(@RequestParam String pageKey) {
    service.delByKey(pageKey);
    sendWebSocketEventMsg(WebSocketTopic.EVENT_CHANGE_SEAT_SCENE, pageKey);
    return Result.ok();
  }

  @Operation(summary = "根据预案Id修改预案名称")
  @PutMapping("/{id}")
  public Result<String> updateSceneNameById(@PathVariable Integer id,
                                            @RequestParam("sceneName") String sceneName) {
    SeatScene seatScene = service.getById(id);
    if (seatScene == null) {
      return Result.failure("No exist scene!", ResponseCode.EX_FAILURE_400);
    }
    if (!seatScene.getName().equals(sceneName)) {
      seatScene.setName(sceneName);
      service.updateById(seatScene);
      sendWebSocketEventMsg(WebSocketTopic.EVENT_CHANGE_SEAT_SCENE, seatScene.getUniqueSearchKey());
    }
    return Result.ok();
  }

  @Operation(summary = "根据预案Id删除相关的预案")
  @DeleteMapping("/{id}")
  public Result<String> delSceneById(@PathVariable Integer id) {
    SeatScene seatScene = service.getById(id);
    if (seatScene == null) {
      return Result.failure("No exist scene!", ResponseCode.EX_FAILURE_400);
    }
    service.removeById(id);
    sendWebSocketEventMsg(WebSocketTopic.EVENT_CHANGE_SEAT_SCENE, seatScene.getUniqueSearchKey());
    return Result.ok();
  }

  private void sendWebSocketEventMsg(String eventType, String pageKey) {
    Map<String, String> msg = Maps.newHashMap();
    msg.put("pageKey", pageKey);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
            BuildWsEventNoticeBodyUtils.buildWsEventNoticeBody(eventType, msg));
  }
}
