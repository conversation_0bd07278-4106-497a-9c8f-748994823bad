package com.mediacomm.controller.web;

import com.mediacomm.config.websocket.client.KvmWebSocketClientManager;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.EncoderAsso;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.vo.EncoderAssoVo;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.EncoderAssoService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.util.SkyLinkStringUtil;
import com.mediacomm.util.mq.RabbitSender;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "Kvm主机", description = "Kvm主机配置的API")
@RestController
@RequestMapping(ResUrlDef.KVM)
public class KvmMasterController extends SkyLinkController<KvmMaster, KvmMasterService> {

  @Autowired
  RabbitSender sender;
  @Autowired
  EncoderAssoService encoderAssoService;
  @Autowired
  KvmWebSocketClientManager kvmWebSocketClientManager;

  //@PreAuthorize("hasAuthority('master')")
  @Operation(summary = "获取所有主机信息")
  @GetMapping("/kvms")
  public Result<Collection<KvmMasterVo>> getKvms() {
    return Result.ok(service.all());
  }

  @Operation(summary = "根据Id获取指定主机信息")
  @GetMapping("/{id}")
  public Result<KvmMasterVo> getKvm(@PathVariable String id) {
    return Result.ok(service.oneById(id));
  }

  @OperationLogRecord(title = "删除指定主机", operateType = OperateType.DELETE)
  @Operation(summary = "根据Id删除指定主机信息")
  @DeleteMapping("/{id}")
  public Result<?> delKvm(@PathVariable String id) {
    service.removeById(id);
    kvmWebSocketClientManager.close(id);
    return Result.ok();
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改指定主机", operateType = OperateType.UPDATE,
      requestBody = "#{master}")
  @Operation(summary = "根据Id修改指定主机信息")
  @PutMapping("/{id}")
  public Result<?> updateKvm(@PathVariable String id, @RequestBody KvmMaster master) {
    master.setMasterId(id);
    service.updateById(master);
    return Result.ok();
  }

  /**
   * .
   */
  @OperationLogRecord(title = "新增Kvm主机", operateType = OperateType.INSERT,
      requestBody = "#{master}")
  @Operation(summary = "新增Kvm主机")
  @PostMapping
  public Result<?> addKvm(@RequestBody @Validated KvmMaster master) {
    master.setMasterId(SkyLinkStringUtil.uuid());
    service.save(master);
    return Result.ok();
  }

  @Operation(summary = "添加关联编码器")
  @PostMapping("/encoder-asso")
  public Result<String> addEncoderAsso(@RequestBody @Validated EncoderAsso encoderAsso) {
    EncoderAssoVo vo = encoderAssoService.oneByMasterId(encoderAsso.getMasterId());
    if (vo != null) {
      return Result.failure("Only one can exist.", ResponseCode.EX_FAILURE_400);
    }
    encoderAssoService.save(encoderAsso);
    return Result.ok();
  }

  @Operation(summary = "删除关联编码器")
  @DeleteMapping("/encoder-asso")
  public Result<String> delEncoderAsso(@RequestParam("encoderId") Integer encoderId) {
    encoderAssoService.removeById(encoderId);
    return Result.ok();
  }

  @Operation(summary = "获取关联编码器")
  @Parameter(name = "masterId", description = "主机ID")
  @GetMapping("/encoder-asso")
  public Result<EncoderAssoVo> getEncoderAsso(@RequestParam(name = "masterId")
                                                          String masterId) {
    return Result.ok(encoderAssoService.oneByMasterId(masterId));
  }

  @Operation(summary = "修改关联编码器")
  @PutMapping("/encoder-asso")
  public Result<String> updateEncoderAsso(@RequestBody @Validated EncoderAsso encoderAsso) {
    encoderAssoService.updateById(encoderAsso);
    return Result.ok();
  }
}
