package com.mediacomm.controller.mqtt;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * SwitcherUpgradeFileController.
 */
@RestController
@RequestMapping("/switcher")
public class SwitcherUpgradeFileController {
  private static final String EXTENSION = ".swu";
  private static final String UPGRADE_PACKAGE_PATH = System.getProperty("user.home") + "/upgrade/";

  @RequestMapping(path = "/download", method = RequestMethod.GET)
  public ResponseEntity<Resource> download(@RequestParam("version") String fileName) throws IOException {
    File file = new File(UPGRADE_PACKAGE_PATH + fileName + EXTENSION);

    HttpHeaders header = new HttpHeaders();
    header.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName + EXTENSION);
    header.add("Cache-Control", "no-cache, no-store, must-revalidate");
    header.add("Pragma", "no-cache");
    header.add("Expires", "0");

    Path path = Paths.get(file.getAbsolutePath());
    ByteArrayResource resource = new ByteArrayResource(Files.readAllBytes(path));

    return ResponseEntity.ok()
        .headers(header)
        .contentLength(file.length())
        .contentType(MediaType.parseMediaType("application/octet-stream"))
        .body(resource);
  }
}
