package com.mediacomm.handler;

import com.mediacomm.entity.Result;
import com.mediacomm.system.variable.ResponseCode;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * Controller异常拦截器.
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@ControllerAdvice
@ResponseBody
@Slf4j
public class ControllerExceptionHandler {

  /**
   * 参数校验失败异常.
   *
   * @param e MethodArgumentNotValidException.
   * @return Result.
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public Result<?> handleErrorArgumentException(MethodArgumentNotValidException e) {
    BindingResult bindingResult = e.getBindingResult();
    //获取所有校验异常信息进行拼接返回
    String message = bindingResult.getAllErrors().stream()
        .map(DefaultMessageSourceResolvable::getDefaultMessage)
        .collect(Collectors.joining(","));
    return Result.failure(message, ResponseCode.EX_FAILURE_400);
  }

  /**
   * 数据库语句执行失败.
   *
   * @param e .
   * @return Result.
   */
  @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
  public Result<?> handleErrorSqlException(SQLIntegrityConstraintViolationException e) {
    return Result.failure("", ResponseCode.EX_SQL_CONSTRAINT_FOREIGN_1001);
  }

  /**
   * 请求消息体格式错误导致序列化失败.
   *
   * @param e .
   * @return Result.
   */
  @ExceptionHandler(HttpMessageNotReadableException.class)
  public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
    return Result.failure(e.getMessage(), ResponseCode.EX_FAILURE_400);
  }

  /**
   * 处理全局异常.
   *
   * @param e Exception.
   * @return Result.
   */
  @ExceptionHandler({RuntimeException.class, Exception.class})
  public Result<?> handleCustomException(Exception e) {
    if (e instanceof NoHandlerFoundException) {
      return Result.failure(e.getMessage(), ResponseCode.EX_NOTFOUND_404);
    } else if (e instanceof HttpRequestMethodNotSupportedException) {
      return Result.failure(e.getMessage(), ResponseCode.EX_METHOD_NOT_ALLOW_405);
    } else if (e.getMessage().contains("duplicate key")) {
      return Result.failure("Duplicate key value.", ResponseCode.EX_FAILURE_400);
    } else {
      log.error(e.getMessage(), e);
      return Result.failure(e.getMessage(), ResponseCode.EX_FAILURE_500);
    }
  }


}
