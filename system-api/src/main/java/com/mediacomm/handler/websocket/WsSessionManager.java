package com.mediacomm.handler.websocket;

import com.google.common.collect.Maps;
import com.mediacomm.system.variable.PropertyKeyConst;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

/**
 * websocket的session管理.
 */
@Slf4j
public class WsSessionManager {
  /**
   * key:session id.
   */
  private static Map<String, WebSocketSession> SESSION_MAP = Maps.newConcurrentMap();

  public static void add(WebSocketSession session) {
    SESSION_MAP.put(session.getId(), session);
  }

  public static Collection<WebSocketSession> getSessionsByPath(String path) {
    Collection<WebSocketSession> sessions = new ArrayList<>();
    SESSION_MAP.forEach((k, v) -> {
      if (v.getUri() != null && v.getUri().getPath().equals(path)) {
        sessions.add(v);
      }
    });
    return sessions;
  }

  public static void removeAndClose(WebSocketSession session) {
    try {
      // 关闭连接
      SESSION_MAP.remove(session.getId()).close();
    } catch (IOException e) {
      log.error(e.getMessage(), e);
    }
  }

  /**
   * 根据token 删除 session.
   */
  public static void removeAndCloseByToken(String targetToken) {
    SESSION_MAP.forEach((k, v) -> {
      String sessionToken = v.getAttributes().get(PropertyKeyConst.LICENCE).toString();
      if (targetToken.equals(sessionToken)) {
        removeAndClose(v);
      }
    });
  }
}
