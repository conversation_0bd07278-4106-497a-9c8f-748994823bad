package com.mediacomm.handler.websocket;

import com.google.common.base.Strings;
import com.google.common.primitives.Bytes;
import com.google.protobuf.ByteString;
import com.mediacomm.entity.dao.EncoderAsso;
import com.mediacomm.entity.dao.VisualizationDevice;
import com.mediacomm.entity.message.UdpMsg;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.Heartbeat;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.message.reqeust.body.TxIdRequestBody;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.pojo.KvmKeyboardDataDto;
import com.mediacomm.pojo.KvmMouseDataDto;
import com.mediacomm.pojo.MessageDto;
import com.mediacomm.pojo.VisualizationDeviceRoomSceneDto;
import com.mediacomm.system.service.EncoderAssoService;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.VisualizationDeviceService;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.FileTransferProtoBuf;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.UdpClient;
import com.mediacomm.util.WebSocketTopic;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

/**
 * websocket连接成功后的消息处理器.
 */
@Component
@Slf4j
public class WebSocketHandler extends TextWebSocketHandler {

  @Autowired
  private RpcSenderUtils senderUtils;
  @Autowired
  private KvmAssetService assetService;
  @Autowired
  private EncoderAssoService encoderAssoService;
  @Autowired
  private VisualizationDeviceService visualizationDeviceService;
  private final UdpClient udpClient = new UdpClient();

  /**
   * 向所有未过期的客户端发送websocket msg.
   *
   * @param msg 发送的消息.
   */
  public void sendMessage(String path, String msg) {
    WsSessionManager.getSessionsByPath(path).forEach(wsSession -> sendMessage(wsSession, msg));
  }

  /**
   * 发送websocket msg.
   *
   * @param session 目标会话.
   * @param msg 发送的消息.
   */
  public void sendMessage(WebSocketSession session, String msg) {
    Thread.ofVirtual().start(() -> {
      if (session != null) {
        TextMessage textMessage = new TextMessage(msg.getBytes(StandardCharsets.UTF_8));
        try {
          session.sendMessage(textMessage);
        } catch (IOException e) {
          log.error(e.getMessage(), e);
        }
      }
    });
  }

  @Override
  protected void handleTextMessage(WebSocketSession session, TextMessage message) {
    try {
      MessageDto<?> msgDto = JsonUtils.decode(message.getPayload(), MessageDto.class);
      if (msgDto != null && !Strings.isNullOrEmpty(msgDto.getTopic())) {
        String body = JsonUtils.encode(msgDto.getBody());
        switch (msgDto.getTopic()) {
          case WebSocketTopic.TOPIC_KVM_MOUSE -> {
            KvmMouseDataDto mouseDataDto = JsonUtils.decode(body, KvmMouseDataDto.class);
            if (mouseDataDto != null) {
              sendMouseKeyboardUdpMsg(mouseDataDto.getTxId(), makeMouseData(mouseDataDto));
            }
          }
          case WebSocketTopic.TOPIC_KVM_KEYBOARD -> {
            KvmKeyboardDataDto keyboardDataDto = JsonUtils.decode(body, KvmKeyboardDataDto.class);
            if (keyboardDataDto != null) {
              sendMouseKeyboardUdpMsg(keyboardDataDto.getTxId(), makeKeyboardData(keyboardDataDto));
            }
          }
          case WebSocketTopic.TOPIC_KVM_ASSET_SNAPSHOT -> {
            SnapshotStatus status = JsonUtils.decode(body, SnapshotStatus.class);
            File file = new File(String.format("/tmp/snapshot/%s", status.getPath()));
            byte[] bytes = new byte[0];
            FileTransferProtoBuf.Response protoBuf;
            if (file.exists()) {
              TxIdRequestBody snapshotReqBody = new TxIdRequestBody();
              snapshotReqBody.setTxId(status.getId());
              // 取图通知
              senderUtils.send(status.getMasterId(), RoutingOperation.SNAPSHOT_GET,
                  buildRpcMqRequest(status.getMasterId(), snapshotReqBody));
              try (FileInputStream fileInputStream = new FileInputStream(file)) {
                bytes = new byte[fileInputStream.available()];
                fileInputStream.read(bytes, 0, fileInputStream.available());
              } catch (IOException e) {
                log.error(e.getMessage(), e);
              }
              if (status.getModifyTime() == file.lastModified()) {
                protoBuf = FileTransferProtoBuf.Response.newBuilder()
                        .setModifyTime(file.lastModified())
                        .setMsgId(msgDto.getMsgId())
                        .setStatus(FileTransferProtoBuf.Signal.NO_UPDATE)
                        .build();
              } else {
                protoBuf = FileTransferProtoBuf.Response.newBuilder()
                        .setData(ByteString.copyFrom(bytes))
                        .setModifyTime(file.lastModified())
                        .setMsgId(msgDto.getMsgId())
                        .setStatus(FileTransferProtoBuf.Signal.NORMAL)
                        .build();
              }
            } else {
              protoBuf = FileTransferProtoBuf.Response.newBuilder()
                      .setModifyTime(0)
                      .setMsgId(msgDto.getMsgId())
                      .setStatus(FileTransferProtoBuf.Signal.NONE)
                      .build();
            }
            session.sendMessage(new BinaryMessage(protoBuf.toByteArray()));
          }
          case WebSocketTopic.TOPIC_VISUALIZATION_SCENE_SWITCH -> {
            VisualizationDeviceRoomSceneDto sceneDto = JsonUtils.decode(body,
                VisualizationDeviceRoomSceneDto.class);
            if (sceneDto != null) {
              VisualizationDevice device = visualizationDeviceService.oneByHardcode(sceneDto.getHardcode());
              if (device != null) {
                device.setRoomId(sceneDto.getRoomId());
                device.setDefaultScene(sceneDto.getSceneId());
                visualizationDeviceService.updateById(device);
              }
            } else {
              log.warn("Err request body {}:{}", body, msgDto.getTopic());
            }
          }
          case WebSocketTopic.TOPIC_CLIENT_HEARTBEAT -> {
            Heartbeat heartbeat = JsonUtils.decode(body, Heartbeat.class);
            if (heartbeat != null && heartbeat.getMasterId() != null) {
              senderUtils.send(heartbeat.getMasterId(), RoutingOperation.CLIENT_HEARTBEAT,
                      buildRpcMqRequest(heartbeat.getMasterId(), heartbeat));
            }
            session.sendMessage(new TextMessage("ok"));
          }
          default -> log.warn("Unknown type:{}", msgDto.getTopic());
        }
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
  @Override
  public void afterConnectionEstablished(@NonNull WebSocketSession session) {
    WsSessionManager.add(session);
  }

  /**
   * 删除会话.
   *
   * @param session .
   * @param status .
   */
  @Override
  public void afterConnectionClosed(@NonNull WebSocketSession session,
                                    @NonNull CloseStatus status) {
    WsSessionManager.removeAndClose(session);
    log.warn("Remote client session closed:{}:{}", session.getRemoteAddress(), status);
  }

  private <R> MqRequest<R> buildRpcMqRequest(String masterId, R body) {
    MqRequest<R> req = new MqRequest<>();
    req.setMasterId(masterId);
    req.setBody(body);
    return req;
  }

  private void sendMouseKeyboardUdpMsg(String txId, byte[] data) {
    KvmAssetVo asset = assetService.oneById(txId);
    if (asset != null) {
      DeviceType deviceType = DeviceType.valueOf(asset.getDeviceType());
      switch (deviceType) {
        case AIRCON_ENCODER -> {
          if (asset.getDeviceIp() != null) {
            UdpMsg sendMsg = UdpMsg.builder()
                    .address(asset.getDeviceIp())
                    .port(8001)
                    .data(data)
                    .build();
            udpClient.send(sendMsg);
          }
        }
        case CAESAR_TX -> {
          EncoderAsso encoderAsso = encoderAssoService.oneByMasterId(asset.getMasterId());
          if (encoderAsso != null) {
            UdpMsg sendMsg = UdpMsg.builder()
                    .address(encoderAsso.getIp())
                    .port(encoderAsso.getPort())
                    .data(data)
                    .build();
            udpClient.send(sendMsg);
          }
        }
        default -> log.error("sendMouseKeyboardUdpMsg:Unsupported device type:{}", asset.getDeviceType());
      }
    } else {
      log.error("sendMouseKeyboardUdpMsg:Asset not found:{}", txId);
    }
  }

  private byte[] makeMouseData(KvmMouseDataDto data) {
    List<Integer> buff = new ArrayList<>();
    buff.add(0);
    buff.add(1);
    buff.add(0);
    buff.add(11);
    buff.add(3);
    int key = 0x00;
    if (data.getMouseKeys() != null) {
      for (String datum : data.getMouseKeys()) {
        if ("left".equals(datum)) {
          key = key | 0x01;
        } else if ("right".equals(datum)) {
          key = key | 0x02;
        } else if ("middle".equals(datum)) {
          key = key | 0x04;
        }
      }
    }
    buff.add(key);
    buff.add(data.getWidth() >> 8);
    buff.add(data.getWidth() & 0xFF);
    buff.add(data.getHeight() >> 8);
    buff.add(data.getHeight() & 0xFF);
    buff.add(data.getMoveX() >> 8);
    buff.add(data.getMoveX() & 0xFF);
    buff.add(data.getMoveY() >> 8);
    buff.add(data.getMoveY() & 0xFF);
    if (data.isScrollDirection()) {
      buff.add((0x100 - data.getScroll()) & 0xFF);
    } else {
      buff.add(data.getScroll() & 0xFF);
    }
    return Bytes.toArray(buff);
  }

  private byte[] makeKeyboardData(KvmKeyboardDataDto data) {
    if (data.getFuncKeys() == null && data.getKeys() == null) {
      return new byte[]{0, 1, 0, 4, (byte) 131, 0, 0, 0}; // Tag(16bit) Len(16bit) Value(可变长度)
    }
    if (data.getKeys() == null) {
      data.setKeys(new ArrayList<>());
    }
    int len = data.getKeys().size();
    if (data.getKeys().size() > 6) {
      len = 6;
      data.setKeys(data.getKeys().subList(0, 6));
    }
    if (data.getKeys().isEmpty()) {
      data.getKeys().add(0);
    }
    List<Integer> buff = new ArrayList<>();
    buff.add(0);
    buff.add(1);
    buff.add(0);
    buff.add(len + 3);
    buff.add(0x80 | (len + 2));
    int key = 0x00;
    if (data.getFuncKeys() == null) {
      data.setFuncKeys(new ArrayList<>());
    }
    for (Integer funcKey : data.getFuncKeys()) {
      key = key | (0x01 << (8 - funcKey));
    }
    buff.add(key);
    buff.add(0);
    buff.addAll(data.getKeys());
    return Bytes.toArray(buff);
  }
}
