package com.mediacomm.handler.websocket;

import com.mediacomm.system.base.kvm.KvmWebSocket;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;

/**
 * .
 */
@Slf4j
public class KvmWebSocketClientHandler extends WebSocketClient {
  private KvmWebSocket clientPre;
  /**
   * .
   */
  public KvmWebSocketClientHandler(KvmWebSocket clientPre) {
    super(clientPre.makeUri(), new Draft_6455(), null, 1000);
    this.clientPre = clientPre;
    setConnectionLostTimeout(1000);
  }

  @Override
  public void onOpen(ServerHandshake handshake) {
    log.debug("The websocket connection is open.");
  }

  @Override
  public void onMessage(String message) {
    clientPre.onMessage(message);
  }

  @Override
  public void onClose(int code, String reason, boolean remote) {
    log.error("Device {} websocket connection closed!Reason:{}, code:{}, state:{}",
            clientPre.getMasterIp(), reason, code, getReadyState());
    if (!isOpen() && !clientPre.isExit()) {
      try {
        Thread.sleep(3000);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
      log.error("Device {} websocket try reconnection.", clientPre.getMasterIp());
      Thread.ofVirtual().name(clientPre.getMasterIp() + " websocket reconnection.").start(this::reconnect);
    }
  }

  @Override
  public void onError(Exception ex) {
    log.error("The websocket connection is abnormal.", ex);
  }

  /**
   * 关闭当前连接，并调用reconnect方法.
   */
  @Override
  public void close() {
    log.info("The {} websocket connection is closed.", clientPre.getMasterIp());
    super.close();
  }

  /**
   * 完全关闭连接并且不会重连.
   */
  public void exit() {
    log.info("The {} websocket exit.", clientPre.getMasterIp());
    clientPre.setExit(true);
    super.close();
  }
}
