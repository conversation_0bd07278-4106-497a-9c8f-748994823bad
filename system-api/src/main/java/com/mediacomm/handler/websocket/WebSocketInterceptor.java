package com.mediacomm.handler.websocket;

import com.google.common.base.Strings;
import com.mediacomm.pojo.PayloadDto;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.util.JwtUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

/**
 * .
 */
@Component
@Slf4j
public class WebSocketInterceptor extends HttpSessionHandshakeInterceptor {

  @Autowired
  private RedisUtil redisUtil;
  private static final String SEC_WS_PROTOCOL = "sec-websocket-protocol";

  /**
   * 在升级为websocket前的http连接，在此处检验用户名及密码.
   *
   * @param request http请求.
   * @param response 响应.
   * @param wsHandler .
   * @param attributes session属性域.
   * @return true建立连接,false拒绝连接.
   */
  @Override
  public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                 WebSocketHandler wsHandler, Map<String, Object> attributes) {
    String key = JwtUtils.TOKEN_PREFIX + request.getHeaders().getFirst(SEC_WS_PROTOCOL);
    String token = JwtUtils.parseJwt(key);
    if (!Strings.isNullOrEmpty(token)) {
      PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
      if (payloadDto != null) {
        Optional<Map<String, String>> onlineTokenMap = redisUtil.hmget(RedisKey.SA_TK_ID);
        if (onlineTokenMap.isPresent()) {
          String existToken = onlineTokenMap.get().get(payloadDto.getJti());
          if (!Strings.isNullOrEmpty(existToken)) {
            attributes.put(PropertyKeyConst.LICENCE, payloadDto.getJti());
            log.info(request.getRemoteAddress() + " trying to connect to ws!");
            return true;
          }
        }
      }
    }
    return false;
  }

  @Override
  public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                             WebSocketHandler wsHandler, Exception ex) {
    HttpServletResponse httpResponse = ((ServletServerHttpResponse) response).getServletResponse();
    // 初次握手访问后，将前端自定义协议头sec-websocket-protocol返回
    httpResponse.addHeader(SEC_WS_PROTOCOL, request.getHeaders().getFirst(SEC_WS_PROTOCOL));
    log.info(request.getRemoteAddress() + " connect to ws success!");
  }

}
