package com.mediacomm.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Set;
import java.util.regex.Pattern;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Service
@Slf4j
public class DbBackupServiceImpl {
  @Value("${DATABASE_HOST}")
  private String databaseHost;
  @Value("${spring.datasource.username}")
  private String databaseUser;
  @Value("${spring.datasource.password}")
  private String databasePassword;
  @Value("${DATABASE_PORT}")
  private Integer databasePort;
  private static final String database = "skylink";
  @Getter
  private String dbDataZipName;
  private String sqlFileName;
  private String redisFileName;
  @Autowired
  private RedisUtil redisUtil;
  private Pattern redisDataKeyMatcher = Pattern.compile("^(?!(signal:|desc:)).*");

  /**
   * 执行mysql、redis备份操作.
   */
  public void dbBackup() {
    mysqlBackup();
    redisBackup();
    URL url = DbBackupServiceImpl.class.getResource("/META-INF/VERSION.properties");
    File version = new File("/tmp/VERSION.properties");
    if (url != null) {
      try (InputStream in = url.openStream();
           OutputStream out = new BufferedOutputStream(new FileOutputStream(version))) {
        byte[] buf = new byte[4096];
        int i;
        while ((i = in.read(buf)) != -1) {
          out.write(buf, 0, i);
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
      dbDataZipName = "/tmp/dbData_" + DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss") + ".zip";
      ZipUtil.zip(new File(dbDataZipName), StandardCharsets.UTF_8,
              false, new File(redisFileName), new File(sqlFileName), version);
      return;
    }
    log.error("Failed to get VERSION.properties");
  }

  /**
   * 执行mysql、redis还原操作.
   */
  public void dbRestore(String zipFileName) {
    String rollbackDirName = "/tmp" + "/skylink_rollback_data_"
            + DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
    File destFolder = new File(rollbackDirName);
    boolean success = destFolder.mkdirs();
    if (success) {
      ZipUtil.unzip(zipFileName, rollbackDirName);
      File[] files = destFolder.listFiles();
      if (files != null) {
        for (File file : files) {
          if (file.getName().endsWith(".sql")) {
            mysqlRestore(file.getAbsolutePath());
          } else if (file.getName().endsWith(".txt")) {
            redisRestore(file.getAbsolutePath());
          }
        }
      }
    } else {
      log.error("Failed to create backup folder.");
    }
  }

  /**
   * 执行数据库备份操作.
   * 通过构造备份文件名并调用mysqldump命令行工具来实现数据库的备份.
   * 备份文件名格式为/skylink_YYYY-MM-DD-HH-mm-ss.sql，其中时间戳确保了文件名的唯一性.
   * 备份命令包含数据库的连接信息（主机、端口、用户和密码）以及数据库名称.
   *
   * @see DateUtil 格式化日期的工具类.
   * @see Runtime#exec(String) 执行命令行指令的方法.
   */
  public void mysqlBackup() {
    StringBuilder fileName = new StringBuilder()
            .append("/tmp/")
            .append("skylink")
            .append("_")
            .append(DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss"))
            .append(".sql");
    sqlFileName = String.valueOf(fileName);
    StringBuilder command = new StringBuilder()
            .append("mysqldump ")
            .append("--no-tablespaces ")
            .append("-h")
            .append(databaseHost)
            .append(" -P")
            .append(databasePort)
            .append(" -u")
            .append(databaseUser)
            .append(" -p")
            .append(databasePassword)
            .append(" --ignore-table=skylink.flyway_schema_history")
            .append(" --hex-blob")
            .append(" --no-create-info")
            .append(" --insert-ignore")
            .append(" --complete-insert")
            .append(" ")
            .append(database)
            .append(" > ")
            .append(fileName);
    doCmd(String.valueOf(command));
  }

  /**
   * 执行数据库还原操作.
   * 通过调用mysql命令行工具来实现数据库的还原.
   * 还原命令包含数据库的连接信息（主机、端口、用户和密码）以及要还原的SQL文件名.
   *
   * @param sqlFileName 要还原的SQL文件名.
   * @see Runtime#exec(String) 执行命令行指令的方法.
   */
  public void mysqlRestore(String sqlFileName) {
    if (FileUtil.exist(sqlFileName)) {
      StringBuilder command = new StringBuilder()
              .append("mysql ")
              .append("-h")
              .append(databaseHost)
              .append(" -P")
              .append(databasePort)
              .append(" -u")
              .append(databaseUser)
              .append(" -p")
              .append(databasePassword)
              .append(" ")
              .append(database)
              .append(" < ")
              .append(sqlFileName);
      doCmd(String.valueOf(command));
    } else {
      log.error("Database restore failed, file not found.");
    }
  }

  /**
   * apt-get install redis-tools -y
   * #!/bin/bash
   * # 获取所有 keys
   * all_keys=$(redis-cli --scan --pattern '*' | grep -v '^$')
   * # 过滤出除了 signal 和 desc 开头的 keys
   * filtered_keys=$(echo "$all_keys" | grep -Ev '^(signal|desc)').
   */
  public void redisBackup() {
    String needBackKeyPattern = "*";
    Set<String> keys = redisUtil.getKeys(needBackKeyPattern);
    keys.removeIf(key -> !redisDataKeyMatcher.matcher(key).matches());
    redisFileName = "/tmp/"
            + "skylink_cache"
            + "_"
            + DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss") + ".txt";
    redisUtil.backup(keys, redisFileName);
  }

  /**
   * 读取 redis 备份文件，并还原到 redis 中.
   *
   * @param fileName 要还原的 redis 备份文件名.
   */
  public void redisRestore(String fileName) {
    log.info("Start to restore redis data from file: {}", fileName);
    try (BufferedReader reader = new BufferedReader(new FileReader(fileName, StandardCharsets.UTF_8))) {
      String line;
      while ((line = reader.readLine()) != null) {
        String[] parts = line.split("=");
        if (parts.length == 2) {
          String key = parts[0];
          String value = parts[1];
          if (value.startsWith("{") && value.endsWith("}")) {
            // 如果是 hash 类型
            redisUtil.hset(key, JsonUtils.decodeMap(value, String.class, String.class));
          } else {
            // 如果是字符串类型
            redisUtil.set(key, value);
          }
        }
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  /**
   * 清空 redis 数据.
   */
  public void redisClear() {
    redisUtil.clear();
  }

  private void doCmd(String cmd) {
    log.info("Execute command: {}", cmd);
    String osName = System.getProperty("os.name").toLowerCase();
    String[] command;
    if (osName.contains("windows")) {
      command = new String[]{"cmd", "/c", cmd};
    } else {
      command = new String[]{"sh", "-c", cmd};
    }
    try {
      Process process = Runtime.getRuntime().exec(command);
      if (process.waitFor() == 0) {
        log.info("Database opt success.");
      } else {
        log.error("Database opt failed.");
      }
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }
}
