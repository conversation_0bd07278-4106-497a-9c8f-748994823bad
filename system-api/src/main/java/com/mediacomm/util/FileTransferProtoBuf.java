// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SanpshotTransfer.proto
// Protobuf Java Version: 4.26.1

package com.mediacomm.util;

public final class FileTransferProtoBuf {
  private FileTransferProtoBuf() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 26,
      /* patch= */ 1,
      /* suffix= */ "",
      FileTransferProtoBuf.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code com.mediacomm.util.Signal}
   */
  public enum Signal
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>NONE = 0;</code>
     */
    NONE(0),
    /**
     * <code>NORMAL = 1;</code>
     */
    NORMAL(1),
    /**
     * <code>NO_UPDATE = 2;</code>
     */
    NO_UPDATE(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        Signal.class.getName());
    }
    /**
     * <code>SIGNAL_NONE = 0;</code>
     */
    public static final int SIGNAL_NONE_VALUE = 0;
    /**
     * <code>SIGNAL_START = 1;</code>
     */
    public static final int SIGNAL_START_VALUE = 1;
    /**
     * <code>SIGNAL_END = 2;</code>
     */
    public static final int SIGNAL_END_VALUE = 2;


    public int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static Signal valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Signal forNumber(int value) {
      return switch (value) {
        case 0 -> NONE;
        case 1 -> NORMAL;
        case 2 -> NO_UPDATE;
        default -> null;
      };
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Signal>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Signal> internalValueMap =
            Signal::forNumber;

    public com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return FileTransferProtoBuf.getDescriptor().getEnumTypes().get(0);
    }

    private static final Signal[] VALUES = values();

    public static Signal valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    Signal(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:com.mediacomm.util.Signal)
  }

  public interface ResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.mediacomm.util.Response)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string msgId = 1;</code>
     * @return The msgId.
     */
    String getMsgId();
    /**
     * <code>string msgId = 1;</code>
     * @return The bytes for msgId.
     */
    com.google.protobuf.ByteString
        getMsgIdBytes();

    /**
     * <code>uint64 modifyTime = 2;</code>
     * @return The modifyTime.
     */
    long getModifyTime();

    /**
     * <code>bytes data = 3;</code>
     * @return The data.
     */
    com.google.protobuf.ByteString getData();

    /**
     * <code>.com.mediacomm.util.Signal status = 4;</code>
     * @return The enum numeric value on the wire for status.
     */
    int getStatusValue();
    /**
     * <code>.com.mediacomm.util.Signal status = 4;</code>
     * @return The status.
     */
    Signal getStatus();
  }
  /**
   * Protobuf type {@code com.mediacomm.util.Response}
   */
  public static final class Response extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.mediacomm.util.Response)
      ResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        Response.class.getName());
    }
    // Use Response.newBuilder() to construct.
    private Response(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Response() {
      msgId_ = "";
      data_ = com.google.protobuf.ByteString.EMPTY;
      status_ = 0;
    }

    public static com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return internal_static_com_mediacomm_util_Response_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return internal_static_com_mediacomm_util_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Response.class, Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object msgId_ = "";
    /**
     * <code>string msgId = 1;</code>
     * @return The msgId.
     */
    @Override
    public String getMsgId() {
      Object ref = msgId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        msgId_ = s;
        return s;
      }
    }
    /**
     * <code>string msgId = 1;</code>
     * @return The bytes for msgId.
     */
    @Override
    public com.google.protobuf.ByteString
        getMsgIdBytes() {
      Object ref = msgId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        msgId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MODIFYTIME_FIELD_NUMBER = 2;
    private long modifyTime_ = 0L;
    /**
     * <code>uint64 modifyTime = 2;</code>
     * @return The modifyTime.
     */
    @Override
    public long getModifyTime() {
      return modifyTime_;
    }

    public static final int DATA_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString data_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>bytes data = 3;</code>
     * @return The data.
     */
    @Override
    public com.google.protobuf.ByteString getData() {
      return data_;
    }

    public static final int STATUS_FIELD_NUMBER = 4;
    private int status_ = 0;
    /**
     * <code>.com.mediacomm.util.Signal status = 4;</code>
     * @return The enum numeric value on the wire for status.
     */
    @Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.mediacomm.util.Signal status = 4;</code>
     * @return The status.
     */
    @Override public Signal getStatus() {
      Signal result = Signal.forNumber(status_);
      return result == null ? Signal.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!isStringEmpty(msgId_)) {
        writeString(output, 1, msgId_);
      }
      if (modifyTime_ != 0L) {
        output.writeUInt64(2, modifyTime_);
      }
      if (!data_.isEmpty()) {
        output.writeBytes(3, data_);
      }
      if (status_ != Signal.NONE.getNumber()) {
        output.writeEnum(4, status_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!isStringEmpty(msgId_)) {
        size += computeStringSize(1, msgId_);
      }
      if (modifyTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, modifyTime_);
      }
      if (!data_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, data_);
      }
      if (status_ != Signal.NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, status_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Response)) {
        return super.equals(obj);
      }
      Response other = (Response) obj;

      if (!getMsgId()
          .equals(other.getMsgId())) return false;
      if (getModifyTime()
          != other.getModifyTime()) return false;
      if (!getData()
          .equals(other.getData())) return false;
      if (status_ != other.status_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = 19 * hash + getDescriptor().hashCode();
      hash = 37 * hash + MSGID_FIELD_NUMBER;
      hash = 53 * hash + getMsgId().hashCode();
      hash = 37 * hash + MODIFYTIME_FIELD_NUMBER;
      hash = 53 * hash + com.google.protobuf.Internal.hashLong(
          getModifyTime());
      hash = 37 * hash + DATA_FIELD_NUMBER;
      hash = 53 * hash + getData().hashCode();
      hash = 37 * hash + STATUS_FIELD_NUMBER;
      hash = 53 * hash + status_;
      hash = 29 * hash + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Response parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Response parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Response parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Response parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Response parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Response parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Response parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseWithIOException(PARSER, input);
    }
    public static Response parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static Response parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedWithIOException(PARSER, input);
    }

    public static Response parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Response parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return parseWithIOException(PARSER, input);
    }
    public static Response parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Response prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.mediacomm.util.Response}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.mediacomm.util.Response)
        ResponseOrBuilder {
      public static com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return internal_static_com_mediacomm_util_Response_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return internal_static_com_mediacomm_util_Response_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Response.class, Builder.class);
      }

      // Construct using com.mediacomm.util.FileTransferProtoBuf.Response.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        msgId_ = "";
        modifyTime_ = 0L;
        data_ = com.google.protobuf.ByteString.EMPTY;
        status_ = 0;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return internal_static_com_mediacomm_util_Response_descriptor;
      }

      @Override
      public Response getDefaultInstanceForType() {
        return getDefaultInstance();
      }

      @Override
      public Response build() {
        Response result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public Response buildPartial() {
        Response result = new Response(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(Response result) {
        int from_bitField0_ = bitField0_;
        if ((from_bitField0_ & 0x00000001) != 0) {
          result.msgId_ = msgId_;
        }
        if ((from_bitField0_ & 0x00000002) != 0) {
          result.modifyTime_ = modifyTime_;
        }
        if ((from_bitField0_ & 0x00000004) != 0) {
          result.data_ = data_;
        }
        if ((from_bitField0_ & 0x00000008) != 0) {
          result.status_ = status_;
        }
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Response) {
          return mergeFrom((Response)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Response other) {
        if (other == getDefaultInstance()) return this;
        if (!other.getMsgId().isEmpty()) {
          msgId_ = other.msgId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getModifyTime() != 0L) {
          setModifyTime(other.getModifyTime());
        }
        if (other.getData() != com.google.protobuf.ByteString.EMPTY) {
          setData(other.getData());
        }
        if (other.status_ != 0) {
          setStatusValue(other.getStatusValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                msgId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                modifyTime_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                data_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                status_ = input.readEnum();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private Object msgId_ = "";
      /**
       * <code>string msgId = 1;</code>
       * @return The msgId.
       */
      public String getMsgId() {
        Object ref = msgId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          msgId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string msgId = 1;</code>
       * @return The bytes for msgId.
       */
      public com.google.protobuf.ByteString
          getMsgIdBytes() {
        Object ref = msgId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          msgId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string msgId = 1;</code>
       * @param value The msgId to set.
       * @return This builder for chaining.
       */
      public Builder setMsgId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        msgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string msgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgId() {
        msgId_ = getDefaultInstance().getMsgId();
        bitField0_ = bitField0_ & ~0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string msgId = 1;</code>
       * @param value The bytes for msgId to set.
       * @return This builder for chaining.
       */
      public Builder setMsgIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        msgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private long modifyTime_ ;
      /**
       * <code>uint64 modifyTime = 2;</code>
       * @return The modifyTime.
       */
      @Override
      public long getModifyTime() {
        return modifyTime_;
      }
      /**
       * <code>uint64 modifyTime = 2;</code>
       * @param value The modifyTime to set.
       * @return This builder for chaining.
       */
      public Builder setModifyTime(long value) {

        modifyTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 modifyTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearModifyTime() {
        bitField0_ = bitField0_ & ~0x00000002;
        modifyTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString data_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>bytes data = 3;</code>
       * @return The data.
       */
      @Override
      public com.google.protobuf.ByteString getData() {
        return data_;
      }
      /**
       * <code>bytes data = 3;</code>
       * @param value The data to set.
       * @return This builder for chaining.
       */
      public Builder setData(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        data_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>bytes data = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearData() {
        bitField0_ = bitField0_ & ~0x00000004;
        data_ = getDefaultInstance().getData();
        onChanged();
        return this;
      }

      private int status_ = 0;
      /**
       * <code>.com.mediacomm.util.Signal status = 4;</code>
       * @return The enum numeric value on the wire for status.
       */
      @Override public int getStatusValue() {
        return status_;
      }
      /**
       * <code>.com.mediacomm.util.Signal status = 4;</code>
       * @param value The enum numeric value on the wire for status to set.
       * @return This builder for chaining.
       */
      public Builder setStatusValue(int value) {
        status_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.com.mediacomm.util.Signal status = 4;</code>
       * @return The status.
       */
      @Override
      public Signal getStatus() {
        Signal result = Signal.forNumber(status_);
        return result == null ? Signal.UNRECOGNIZED : result;
      }
      /**
       * <code>.com.mediacomm.util.Signal status = 4;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(Signal value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        status_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.com.mediacomm.util.Signal status = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = bitField0_ & ~0x00000008;
        status_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.mediacomm.util.Response)
    }

    // @@protoc_insertion_point(class_scope:com.mediacomm.util.Response)
    private static final Response DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Response();
    }

    public static Response getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Response>
        PARSER = new com.google.protobuf.AbstractParser<Response>() {
      @Override
      public Response parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Response> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Response> getParserForType() {
      return PARSER;
    }

    @Override
    public Response getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_mediacomm_util_Response_descriptor;
  private static final
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_mediacomm_util_Response_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\026SanpshotTransfer.proto\022\022com.mediacomm." +
      "util\"g\n\010Response\022\r\n\005msgId\030\001 \001(\t\022\022\n\nmodif" +
      "yTime\030\002 \001(\004\022\014\n\004data\030\003 \001(\014\022*\n\006status\030\004 \001(" +
      "\0162\032.com.mediacomm.util.Signal*;\n\006Signal\022" +
      "\017\n\013SIGNAL_NONE\020\000\022\020\n\014SIGNAL_START\020\001\022\016\n\nSI" +
      "GNAL_END\020\002B*\n\022com.mediacomm.utilB\024FileTr" +
      "ansferProtoBufb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_mediacomm_util_Response_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_mediacomm_util_Response_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_mediacomm_util_Response_descriptor,
        new String[] { "MsgId", "ModifyTime", "Data", "Status", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
