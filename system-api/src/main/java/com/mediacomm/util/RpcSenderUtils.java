package com.mediacomm.util;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.mq.RabbitSender;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Objects;

/**
 * .
 */
@Component
@Slf4j
public class RpcSenderUtils {
  @Autowired
  RabbitSender sender;
  @Autowired
  KvmMasterService masterService;

  /**
   * .
   */
  public <M> String send(@NonNull String masterId, String route, MqRequest<M> mqRequest) {
    String targetRoute = getRouteByDeviceType(masterId, route);
    Object msg = null;
    if (!targetRoute.isEmpty()) {
      msg = sender.syncSend(targetRoute, mqRequest);
    }
    return msg != null ? JsonUtils.encode(msg) :
        JsonUtils.encode(
            Result.failure("Remote message time out", ResponseCode.REMOTE_MSG_TIME_OUT));
  }

  public <M> String send(String route, MqRequest<M> mqRequest) {
    Object msg = sender.syncSend(route, mqRequest);
    return msg != null ? JsonUtils.encode(msg) :
        JsonUtils.encode(
            Result.failure("Remote message time out", ResponseCode.REMOTE_MSG_TIME_OUT));
  }

  /**
   * 异步发送消息到指定的主设备.
   *
   * @param masterId 主设备的唯一标识，不可为null.
   * @param route 消息的路由信息.
   * @param mqRequest 要发送的消息请求对象，包含消息内容和相关参数.
   */
  public <M> void asyncSend(@NonNull String masterId, String route, MqRequest<M> mqRequest) {
    String targetRoute = getRouteByDeviceType(masterId, route);
    if (!targetRoute.isEmpty()) {
      sender.asyncSend(targetRoute, mqRequest);
    }
  }

  private String getRouteByDeviceType(String masterId, String route) {
    KvmMasterVo master = masterService.oneById(masterId);
    if (master == null) {
      log.warn("Master {} no exist!", masterId);
      return "";
    }
    DeviceType deviceType = master.getDeviceType();
    if (Objects.equals(deviceType.getRootRoute(), RoutingKey.DEAD_LETTER_ROUTING)
            || StringUtils.isBlank(deviceType.getRootRoute())) {
      log.info("DeviceType {} no route!", deviceType);
      return "";
    }
    return deviceType.getRootRoute() + route;
  }
}
