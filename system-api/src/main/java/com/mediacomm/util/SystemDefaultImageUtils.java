package com.mediacomm.util;

import cn.hutool.core.util.HashUtil;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.DefaultPicture;
import com.mediacomm.entity.dao.Picture;
import com.mediacomm.system.service.DefaultPicService;
import com.mediacomm.system.service.PictureService;
import com.mediacomm.system.variable.ResponseCode;
import java.io.IOException;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * .
 */
@Component
@Slf4j
public class SystemDefaultImageUtils {
  @Autowired
  private PictureService pictureService;
  @Autowired
  private DefaultPicService defaultPicService;

  /**
   * 根据图片名称获取图片.
   * 此方法尝试从Redis缓存中获取与图片名称对应的图片信息如果缓存中存在该图片信息.
   * 则直接返回；否则，返回一个表示图片拉伸状态的对象.
   *
   * @param imageName 图片名称，用于从Redis中检索图片信息
   * @return 返回一个SystemDefaultImageDto对象，包含图片是否默认拉伸的信息及图片内容
   */
  public DefaultPicture getDefaultImage(String imageName) {
    DefaultPicture defaultPic = defaultPicService.oneByName(imageName);
    if (defaultPic != null) {
      return defaultPic;
    }
    defaultPic = new DefaultPicture();
    byte[] bytes = new byte[0];
    ClassPathResource resource = new ClassPathResource(String.format("static/images/%s.png", imageName));
    if (resource.exists()) {
      try (InputStream fileInputStream = resource.getInputStream()) {
        bytes = new byte[fileInputStream.available()];
        fileInputStream.read(bytes, 0, fileInputStream.available());
      } catch (IOException e) {
        log.error(e.getMessage(), e);
      }
    }
    defaultPic.setName(imageName);
    defaultPic.setStretch(Boolean.TRUE);
    defaultPic.setContent(bytes);
    return defaultPic;
  }

  /**
   * 保存默认图片.
   */
  public Result<String> saveDefaultImage(String imageName, boolean stretch, MultipartFile multipartFile) {
    if (StringUtils.isEmpty(imageName)) {
      return Result.failure("No file name!", ResponseCode.EX_FAILURE_400);
    }
    DefaultPicture defaultPic = new DefaultPicture();
    defaultPic.setName(imageName);
    defaultPic.setStretch(stretch);
    if (multipartFile != null && !multipartFile.isEmpty()) {
      try {
        if (ServletUtils.isPngInMultipartFile(multipartFile)) {
          long pictureId = Math.abs(HashUtil.cityHash64(multipartFile.getBytes()));
          defaultPic.setPicHash(pictureId);
          Picture picture = pictureService.getById(pictureId);
          if (picture == null) {
            picture = new Picture();
            picture.setId(pictureId);
            picture.setContent(multipartFile.getBytes());
            pictureService.save(picture);
          }
        } else {
          return Result.failure("Error content-type!", ResponseCode.EX_FAILURE_400);
        }
      } catch (Exception e) {
        return Result.failure("Upload image failed.", ResponseCode.EX_FAILURE_500);
      }
    }
    defaultPicService.saveOrUpdate(defaultPic);
    return Result.ok();
  }
}
