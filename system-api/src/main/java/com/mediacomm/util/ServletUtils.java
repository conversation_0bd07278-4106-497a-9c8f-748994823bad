package com.mediacomm.util;

import com.mediacomm.entity.vo.AccountVo;
import com.mediacomm.pojo.PayloadDto;
import com.mediacomm.system.service.AccountService;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 响应到前端页面.
 */
@Component
public class ServletUtils {
  @Autowired
  private AccountService service;
  private static AccountService accountService;

  @SuppressWarnings("unused") //此处pmd会报错方法未使用，忽略检查
  @PostConstruct
  private void init() {
    accountService = service;
  }

  /**
   * 响应会客户端.
   *
   * @param object 待渲染的实体类，会自动转为json
   */
  public static void render(HttpServletRequest request, HttpServletResponse response, Object object)
      throws IOException {
    // 允许跨域
    response.setHeader("Access-Control-Allow-Origin", "*");
    // 允许自定义请求头token(允许head跨域)
    response.setHeader("Access-Control-Allow-Headers",
        "token, Accept, Origin, X-Requested-With, Content-Type, Last-Modified");
    response.setHeader("Content-type", "application/json;charset=UTF-8");
    response.getWriter().print(JsonUtils.encode(object));
  }

  /**
   * .
   */
  public static String getPersonnelName(HttpServletRequest request) {
    String token = JwtUtils.parseJwt(request);
    PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
    return accountService.oneById(payloadDto.getAccountId()).getPersonnelName();
  }

  /**
   * .
   */
  public static String getPwd(HttpServletRequest request) {
    String token = JwtUtils.parseJwt(request);
    PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
    if (payloadDto != null) {
      AccountVo account = accountService.oneById(payloadDto.getAccountId());
      if (account != null) {
        return account.getAccountPassword();
      }
    }
    return "";
  }

  /**
   * 上传png图片.
   */
  public static boolean isPngInMultipartFile(MultipartFile multipartFile) {
    if (multipartFile.getContentType() == null) {
      return false;
    }
    String contentType = multipartFile.getContentType();
    if (contentType == null) {
      return false;
    }
    String[] type = contentType.split("/");
    return type.length == 2
            || Objects.equals(type[0], "image")
            || Objects.equals(type[1], "png");
  }
}
