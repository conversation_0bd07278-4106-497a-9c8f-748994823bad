package com.mediacomm.util;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import cn.hutool.core.date.DateUtil;
import com.mediacomm.entity.dao.Version;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.springframework.web.multipart.MultipartFile;
import com.mediacomm.entity.dao.PeripheralUpgradePackage;
import com.mediacomm.pojo.PeripheralUpgradePackageDto;
import com.mediacomm.system.variable.sysenum.DeviceType;

/**
 * 外设升级工具类.
 */
@Slf4j
public class PeripheralUpgradeUtils {

  private static final String UPGRADE_PACKAGE_PATH = "/var/data/peripheral_upgrade/";
  private static final int MAX_PACKAGE_COUNT = 10; // 每种类型最多保留的升级包数量

  /**
   * 获取升级包存储路径.
   *
   * @return 存储路径
   */
  public static String getUpgradePackagePath() {
    return UPGRADE_PACKAGE_PATH;
  }

  /**
   * 创建升级包存储目录.
   *
   * @param deviceType 设备类型
   * @return 是否成功
   */
  public static boolean createUpgradeDirectory(DeviceType deviceType) {
    File file = new File(UPGRADE_PACKAGE_PATH + deviceType);
    if (!file.exists()) {
      return file.mkdirs();
    }
    return true;
  }

  /**
   * 计算文件MD5.
   *
   * @param file 文件
   * @return MD5值
   */
  public static String calculateMd5(MultipartFile file) {
    try {
      MessageDigest md = MessageDigest.getInstance("MD5");
      try (BufferedInputStream bis = new BufferedInputStream(file.getInputStream())) {
        byte[] buffer = new byte[8192];
        int read;
        while ((read = bis.read(buffer)) != -1) {
          md.update(buffer, 0, read);
        }
        byte[] digest = md.digest();
        return String.format("%032x", new BigInteger(1, digest));
      } catch (IOException e) {
        log.error("Failed to calculate file MD5.", e);
        return null;
      }
    } catch (NoSuchAlgorithmException ex) {
      log.error("The MD5 algorithm is not available.", ex);
      return null;
    }
  }

  /**
   * 清理旧的升级包，保留最新的N个.
   *
   * @param deviceType 设备类型
   */
  public static void cleanOldPackages(DeviceType deviceType) {
    File directory = new File(UPGRADE_PACKAGE_PATH + deviceType);
    if (!directory.exists() || !directory.isDirectory()) {
      return;
    }
    File[] files = directory.listFiles();
    if (files == null || files.length <= MAX_PACKAGE_COUNT) {
      return;
    }
    List<File> fileList = Stream.of(files)
        .filter(File::isFile)
        .sorted(Comparator.comparingLong(File::lastModified).reversed())
        .toList();
    if (fileList.size() > MAX_PACKAGE_COUNT) {
      fileList.subList(MAX_PACKAGE_COUNT, fileList.size()).forEach(file -> {
        if (!file.delete()) {
          log.error("Failed to delete the old upgrade package: {}", file.getAbsolutePath());
        }
      });
    }
  }

  /**
   * 验证升级包.
   * 这里可以根据实际需求实现验证逻辑，例如检查文件格式、版本号等
   *
   * @param file 升级包文件
   * @return 是否有效
   */
  public static boolean validateUpgradePackage(MultipartFile file, DeviceType expectType,
                                               PeripheralUpgradePackage upgradePackage) {
    // 文件内容是流式传输的（例如从网络流中读取），无法提前知道其大小，size会是-1
    if (file == null || file.isEmpty()) {
      log.info("The upgrade package is null or empty.");
      return false;
    }
    String extension = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('.') + 1);
    // swu是CPIO格式的归档
    try (InputStream in = file.getInputStream()) {
      ArchiveInputStream<?> archiveInputStream;

      if ("zip".equals( extension)) {
        archiveInputStream = new ZipArchiveInputStream(in);
      } else if ("swu".equals(extension)) {
        archiveInputStream = new CpioArchiveInputStream(in);
      } else {
        log.info("The upgrade package is not support file.");
        return false;
      }

      try (archiveInputStream) {
        ArchiveEntry entry;
        while ((entry = archiveInputStream.getNextEntry()) != null) {
          if (entry.isDirectory()) {
            continue;
          }
          if (entry.getName().equals("version.txt")) {
            StringBuilder content = new StringBuilder();
            byte[] butter = new byte[1024];
            int read;
            while ((read = archiveInputStream.read(butter)) != -1) {
              content.append(new String(butter, 0, read));
            }
            PeripheralUpgradePackageDto dto = toSkylinkPackageDto(expectType, content.toString());
            if (dto.getDeviceType() != expectType) {
              log.info("The device type does not match. Device type: {}", content);
              return false;
            }
            if (isSupportDeviceType(dto.getDeviceType())) {
              upgradePackage.setVersion(dto.getVersion());
              upgradePackage.setDescription(dto.getDescription());
              upgradePackage.setDeviceType(dto.getDeviceType());
              return true;
            }
            log.info("The device type is not supported. Device type: {}", content);
          }
        }
        // 如果循环结束都没找到 version.txt
        log.info("The 'version.txt' file was not found in the package.");
        return false;
      }

    } catch (Exception e) {
      log.error("Failed to process upgrade package: {}", e.getMessage(), e);
      return false;
    }
  }

  public static boolean isSupportDeviceType(DeviceType deviceType) {
    for (DeviceType i : supportDeviceTypes()) {
      if (i == deviceType) {
        return true;
      }
    }
    return false;
  }

  public static DeviceType[] supportDeviceTypes() {
    List<DeviceType> its = new ArrayList<>();
    for (DeviceType type : DeviceType.values()) {
      if (type.isSupportUpgrade()) {
        its.add(type);
      }
    }
    return its.toArray(DeviceType[]::new);
  }

  private static PeripheralUpgradePackageDto toSkylinkPackageDto(DeviceType expectType, String versionStr) {
    return switch (expectType) {
      case CAESAR_VP7 -> {
        Map<String, String> map = KeyValueToJsonConverter.parseToMap(versionStr);
        String product = map.get("product");
        if (product.isBlank() || !product.startsWith("dkm_vp7", 0)) {
          yield new PeripheralUpgradePackageDto(DeviceType.UNKNOWN);
        }
        PeripheralUpgradePackageDto pd = new PeripheralUpgradePackageDto();
        pd.setDeviceType(DeviceType.CAESAR_VP7);
        map.forEach((k, v) -> {
          if (!k.equals("product")) {
            String[] vAndD = v.split("-");
            if (vAndD.length == 2) {
              pd.getVersion().add(new Version(k, vAndD[0].replace("V", ""),
                      DateUtil.format(DateUtil.parse(vAndD[1], "yyMMdd"),  "yyyy.MM.dd")));
            }
          }
        });
        pd.setDescription(map.get("description") == null ? product : map.get("description"));
        yield pd;
      }
      default -> new PeripheralUpgradePackageDto(DeviceType.UNKNOWN);
    };
  }
}
