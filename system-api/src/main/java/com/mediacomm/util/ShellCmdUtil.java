package com.mediacomm.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ShellCmdUtil {
  /**
   * 调用脚本方法。
   *
   * @param script 脚本文件.
   * @param args 脚本参数.
   * @param workspace 工作空间目录.
   * @throws IOException 抛出IO异常.
   */
  public static void callScript(String script, String args, String... workspace)
      throws IOException {
    String[] cmd = {"sh", args != null ? args : "", script};
    File dir = null;
    if (workspace[0] != null) {
      dir = new File(workspace[0]);
    }
    String[] e = {"val=2", "call=Bash Shell"};
    Process process = Runtime.getRuntime().exec(cmd, e, dir);
    BufferedReader bufferedReader = new BufferedReader(
            new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
    String line = "";
    StringBuilder outRes = new StringBuilder();
    while ((line = bufferedReader.readLine()) != null) {
      outRes.append(line).append(System.lineSeparator());
    }
    log.info(String.format("Shell cmd %s exec result:%s", Arrays.toString(cmd), outRes));
    bufferedReader.close();
  }
}
