package com.mediacomm.util;

import com.google.common.base.Strings;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.pojo.PayloadDto;
import jakarta.servlet.http.HttpServletRequest;

/**
 * KvmOperationUtils.
 */
public class KvmOperationUtils {
  private static final String LOCAL_IP = "s127.0.0.1".substring(1);
  private static final String UNKNOWN = "unknown";

  /**
   * getFrom.
   */
  public static KvmOperationFrom getFrom(HttpServletRequest request) {
    KvmOperationFrom from = new KvmOperationFrom();
    String address = getIpAddr(request);
    from.setAddress(address);
    String token = JwtUtils.parseJwt(request);
    if (!Strings.isNullOrEmpty(token)) {
      PayloadDto payloadDto = JwtUtils.verifyTokenByHmac(token);
      if (payloadDto != null) {
        String username = payloadDto.getUsername() != null ? payloadDto.getUsername() : "未知用户";
        from.setUser(username);
      }
    }
    return from;
  }

  /**
   * 获取IP地址.
   * 使用Nginx等反向代理软件， 则不能通过request.getRemoteAddr()获取IP地址
   * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址.
   */
  private static String getIpAddr(HttpServletRequest request) {
    if (request == null) {
      return UNKNOWN;
    }
    String ip = request.getHeader("x-forwarded-for");
    if (Strings.isNullOrEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (Strings.isNullOrEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("X-Forwarded-For");
    }
    if (Strings.isNullOrEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (Strings.isNullOrEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
      ip = request.getHeader("X-Real-IP");
    }
    if (Strings.isNullOrEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }
    return "s0:0:0:0:0:0:0:1".substring(1).equals(ip) ? LOCAL_IP : ip;
  }
}
