package com.mediacomm.util;

/**
 * .
 */
public class WebSocketTopic {
  /**
   * 预览图.
   */
  public static final String TOPIC_KVM_ASSET_SNAPSHOT = "kvm-asset-snapshot";
  /**
   * 接收键盘指令.
   */
  public static final String TOPIC_KVM_KEYBOARD = "visualization-kvm-keyboard";
  /**
   * 接收鼠标指令.
   */
  public static final String TOPIC_KVM_MOUSE = "visualization-kvm-mouse";
  /**
   * 接收终端的场景房间切换的指令.
   */
  public static final String TOPIC_VISUALIZATION_SCENE_SWITCH = "visualization-scene-switch";
  /**
   * 客户端心跳检测.
   */
  public static final String TOPIC_CLIENT_HEARTBEAT = "client-heartbeat";

  /**
   * 转发坐席窗口改变事件.
   */
  public static final String EVENT_CHANGE_SEAT_PANELS = "change-seat-panels";
  /**
   * 转发大屏窗口改变事件.
   */
  public static final String EVENT_CHANGE_VW_PANELS = "change-vw-panels";

  /**
   * 转发大屏配置改变事件.
   */
  public static final String EVENT_CHANGE_VW_CONFIG = "change-vw-config";
  /**
   * 转发场景改变事件.
   */
  public static final String EVENT_CHANGE_VIS_SCENE = "change-visualization-scene";
  /**
   * 转发场景删除事件.
   */
  public static final String EVENT_DELETE_VIS_SCENE = "delete-visualization-scene";
  /**
   * 大屏预案改变事件.
   */
  public static final String EVENT_CHANGE_VW_SCENE = "change-vw-scene";
  /**
   * 大屏横幅发生改变.
   */
  public static final String EVENT_CHANGE_VW_BANNER = "change-vw-banner";
  /**
   * 告警改变.
   */
  public static final String ALARM_CHANGE = "alarm-change";
  /**
   * 控件信息改变.
   */
  public static final String EVENT_CHANGE_VIS_UI = "change-visualization-ui";
  /**
   * 坐席预案改变事件.
   */
  public static final String EVENT_CHANGE_SEAT_SCENE = "change-seat-scene";
}
