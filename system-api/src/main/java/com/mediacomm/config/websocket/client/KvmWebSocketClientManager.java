package com.mediacomm.config.websocket.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.mediacomm.caesar.domain.CaesarSwitchSeatSignal;
import com.mediacomm.caesar.domain.CaesarSwitchVwSignal;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.caesar.util.mapper.CaesarEntityMapper;
import com.mediacomm.config.websocket.client.notify.AirconMsg;
import com.mediacomm.config.websocket.client.notify.CaesarMsg;
import com.mediacomm.config.websocket.client.notify.KaitoMsg;
import com.mediacomm.config.websocket.client.notify.domain.SeatPanelsChange;
import com.mediacomm.config.websocket.client.notify.domain.VideoWallChange;
import com.mediacomm.domain.request.KaitoLayerReq;
import com.mediacomm.entity.DeviceSignalValue;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmOperationLog;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSeatDecoder;
import com.mediacomm.entity.dao.KvmSwitchLog;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.message.SeatPanels;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ChannelIdRequestBody;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.KvmMasterVo;
import com.mediacomm.handler.websocket.KvmWebSocketClientHandler;
import com.mediacomm.handler.websocket.WebSocketHandler;
import com.mediacomm.system.base.kvm.KvmWebSocket;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmOperationLogService;
import com.mediacomm.system.service.KvmSeatService;
import com.mediacomm.system.service.KvmSwitchLogService;
import com.mediacomm.system.service.KvmUserService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.OperationType;
import com.mediacomm.system.variable.sysenum.RedisSignalKey;
import com.mediacomm.system.variable.sysenum.SwitchMode;
import com.mediacomm.util.BuildWsEventNoticeBodyUtils;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import com.mediacomm.util.RpcSenderUtils;
import com.mediacomm.util.WebSocketTopic;
import com.mediacomm.util.websocket.client.Notice;
import com.mediacomm.util.websocket.client.NoticeTemplate;
import jakarta.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;


/**
 * .
 */
@Configuration
@Slf4j
public class KvmWebSocketClientManager implements CommandLineRunner {

  @Autowired
  private WebSocketHandler handler;
  @Autowired
  private KvmMasterService masterService;
  @Autowired
  private KvmAssetService kvmAssetService;
  @Autowired
  private KvmUserService kvmUserService;
  @Autowired
  private KvmVideoWallService videoWallService;
  @Autowired
  private KvmSeatService kvmSeatService;
  @Autowired
  private KvmSwitchLogService kvmSwitchLogService;
  @Autowired
  private KvmOperationLogService kvmOperationLogService;
  @Autowired
  private RedisUtil redisUtil;
  @Autowired
  private CaesarEntityMapper caesarEntityMapper;
  @Autowired
  private RpcSenderUtils rpcSenderUtils;
  private final Executor executor = Executors.newSingleThreadExecutor();
  // key:masterId
  private final Map<String, KvmWebSocketClientHandler> socketClientMap = Maps.newConcurrentMap();
  private final Set<String> decoderIds = new HashSet<>();

  @Override
  public void run(String... args) {
    Collection<KvmMaster> masters = masterService.list();
    for (KvmMaster master : masters) {
      checkConnect(master.getMasterId());
    }
  }

  private KvmWebSocket buildCaesarWsClient(KvmMaster master) {
    return new KvmWebSocket() {
      @Override
      public void onMessage(String msg) {
        try {
          Notice notify = JsonUtils.decode(msg, Notice.class);
          if (notify == null || notify.getType() == null
              || notify.getType().equals("heart-breat")) {
            return;
          }
          synchronized (handler) {
            switch (notify.getType()) {
              case "add-tx" -> {
                CaesarMsg.CaesarTxNotice tn = JsonUtils.decode(msg, CaesarMsg.CaesarTxNotice.class);
                KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(tn.getMessage(),
                    master.getMasterId());
                kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
              }
              case "add-rx" -> {
                CaesarMsg.CaesarRxNotice rn = JsonUtils.decode(msg, CaesarMsg.CaesarRxNotice.class);
                KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(rn.getMessage(),
                    master.getMasterId());
                kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
              }
              case "delete-tx", "delete-rx" -> {
                CaesarMsg.CaesarDeviceSnNotice snNotice =
                        JsonUtils.decode(msg, CaesarMsg.CaesarDeviceSnNotice.class);
                log.info("caesar delete device: {}", msg);
                kvmAssetService.delByHardcode(snNotice.getMessage().getSn());
              }
              case "update-user" -> {
                CaesarMsg.CaesarUserNotice un =
                    JsonUtils.decode(msg, CaesarMsg.CaesarUserNotice.class);
                kvmUserService.saveOrUpdate(
                    Collections.singleton(caesarEntityMapper
                        .toKvmUser(un.getMessage(), master.getMasterId())).stream().toList(),
                    master.getMasterId());
              }
              case "update-vw" -> {
                CaesarMsg.CaesarVideoWallNotice vn =
                    JsonUtils.decode(msg, CaesarMsg.CaesarVideoWallNotice.class);
                redisUtil.hset(RedisKey.KC_SC + master.getMasterId(),
                    String.valueOf(vn.getMessage().getId()),
                    JsonUtils.encode(vn.getMessage()));
                KvmVideoWall wall = caesarEntityMapper.toKvmVideoWall(vn.getMessage(),
                    master.getMasterId());
                videoWallService.saveOrUpdate(wall);
                sendChangeVideoWallConfig(wall);
              }
              case "delete-vw" -> {
                CaesarMsg.CaesarVideoWallNotice vn =
                    JsonUtils.decode(msg, CaesarMsg.CaesarVideoWallNotice.class);
                KvmVideoWall wall = videoWallService.oneByMasterIdAndDeviceId(master.getMasterId(),
                    vn.getMessage().getId());
                videoWallService.removeById(wall.getWallId());
                sendChangeVideoWallConfig(wall);
                // todo 释放海康解码终端设备
              }
              case "add-seat" -> {
                CaesarMsg.CaesarSeatNotice sn =
                    JsonUtils.decode(msg, CaesarMsg.CaesarSeatNotice.class);
                KvmSeat seat = caesarEntityMapper.toKvmSeat(sn.getMessage(), master.getMasterId());
                for (KvmSeatDecoder decoder : seat.getDecoders()) {
                  KvmAssetVo rx =
                      kvmAssetService.oneByDeviceId(decoder.getDeviceId(), seat.getMasterId());
                  if (rx != null) {
                    decoder.setAssetId(rx.getAssetId());
                    decoder.setDeviceName(rx.getName());
                    decoder.setDeviceType(rx.getDeviceType());
                  }
                }
                kvmSeatService.saveOrUpdate(seat, master.getMasterId());
              }
              case "delete-seat" -> {
                CaesarMsg.CaesarSeatNotice sn =
                    JsonUtils.decode(msg, CaesarMsg.CaesarSeatNotice.class);
                KvmSeat seat = kvmSeatService.oneByMasterIdAndDeviceId(master.getMasterId(),
                    sn.getMessage().getId());
                kvmSeatService.removeById(seat);
              }
              case "change-vw-panels", "java-change-vw-panels" -> {
                CaesarMsg.CaesarVideoWallChangeNotice cn = JsonUtils.decode(msg,
                    CaesarMsg.CaesarVideoWallChangeNotice.class);
                if (cn != null && cn.getMessage() != null) {
                  String relatedId = Property.findValueByKey(master.getCollectorProperties(),
                          "associateHost", master.getMasterId());
                  // 冗余主机，都会连接websocket接口
                  // 1.一台掉线时，发送大屏改变的消息，要包含掉线的冗余对端主机，并且也要包含当前能收到change-vw-panels消息的主机
                  // ，因为配置的可视化页面可以是主用主机的视频墙，也可以是备用主机的视频墙
                  // 2.主机都在线时，冗余对端主机也能正常发出change-vw-panels消息，所以只处理当前收到change-vw-panels消息的主机既可
                  if (isRedundantMasterLinkFail(relatedId)) {
                    sendChangeVideoWallPanelsNotice(relatedId, cn.getMessage().getVwId(),
                            DeviceType.CAESAR_VP6_VIDEO_WALL.getDeviceTypeId(),
                            DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId());
                  }
                  sendChangeVideoWallPanelsNotice(master.getMasterId(), cn.getMessage().getVwId(),
                         DeviceType.CAESAR_VP6_VIDEO_WALL.getDeviceTypeId(),
                         DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId());
                }
              }
              case "change-kaito-panels" -> {
                CaesarMsg.KaitoVideoWallChangeNotice ck = JsonUtils.decode(msg,
                    CaesarMsg.KaitoVideoWallChangeNotice.class);
                if (ck != null && ck.getMessage() != null) {
                  String relatedId = Property.findValueByKey(master.getCollectorProperties(),
                          "associateHost", master.getMasterId());
                  // 参考上文change-vw-panels的注释
                  if (isRedundantMasterLinkFail(relatedId)) {
                    sendKaitoPanelChangeMsg(relatedId,
                            ck.getMessage().getWallId(), ck.getMessage().getGroupId());
                  }
                  sendKaitoPanelChangeMsg(master.getMasterId(),
                          ck.getMessage().getWallId(), ck.getMessage().getGroupId());
                }
              }
              case "switch-vw-signal" -> {
                CaesarMsg.CaesarSwitchVwSignalNotice
                    switchVwSignalTemplate =
                    JsonUtils.decode(msg, CaesarMsg.CaesarSwitchVwSignalNotice.class);
                if (switchVwSignalTemplate != null && switchVwSignalTemplate.getMessage() != null) {
                  int vwId = switchVwSignalTemplate.getMessage().getVwId();
                  List<KvmVideoWall> walls = videoWallService.allByMasterIdAndDeviceId(
                      master.getMasterId(), vwId).stream().toList();
                  CaesarSwitchVwSignal message = switchVwSignalTemplate.getMessage();
                  // 避免全光屏和VP6屏的deviceId相同导致的异常
                  // 如果检索到的结果只有一个，并且是VP6屏，才保存日志
                  // 如果检索到的结果大于1，则遍历所有结果，如果存在VP6屏，才保存日志
                  if (walls.size() == 1 && Objects.equals(walls.getFirst().getDeviceModel(),
                          DeviceType.CAESAR_VP6_VIDEO_WALL.getDeviceTypeId())) {
                    saveVwOperationLog(message, master.getMasterId());
                  } else if (walls.size() > 1) {
                    walls.forEach(wall -> {
                      if (Objects.equals(wall.getDeviceModel(),
                              DeviceType.CAESAR_VP6_VIDEO_WALL.getDeviceTypeId())) {
                        saveVwOperationLog(message, master.getMasterId());
                      }
                    });
                  }
                }
              }
              case "switch-seat-signal" -> {
                CaesarMsg.CaesarSwitchSeatSignalNotice seatSignalNotice = JsonUtils.decode(msg,
                    CaesarMsg.CaesarSwitchSeatSignalNotice.class);
                if (seatSignalNotice != null && seatSignalNotice.getMessage() != null) {
                  KvmAsset rx = kvmAssetService.oneByDeviceId(
                      seatSignalNotice.getMessage().getRxId(), master.getMasterId());
                  KvmAsset tx = kvmAssetService.oneByDeviceId(
                      seatSignalNotice.getMessage().getTxId(), master.getMasterId());
                  // 发送窗口改变通知
                  if (rx != null && tx != null) {
                    sendChangeSeatPanelsNotice(rx);
                    // 记录切换日志
                    KvmSwitchLog switchLog = KvmSwitchLog.builder()
                        .masterId(rx.getMasterId())
                        .con(rx.getName())
                        .cpu(tx.getName())
                        .switchTime(System.currentTimeMillis())
                        .fromUser(seatSignalNotice.getMessage().getUser())
                        .switchMode(SwitchMode.from(seatSignalNotice.getMessage().getType()))
                        .disconnect(SwitchMode.from(seatSignalNotice.getMessage().getType())
                            == SwitchMode.UNKNOWN)
                        .build();
                    kvmSwitchLogService.save(switchLog);
                    saveSeatOperationLog(seatSignalNotice.getMessage(), master.getMasterId());
                  }
                }
              }
              case "change-seat-panels" -> {
                CaesarMsg.CaesarChangeSeatPanelsNotice seatPanelsNotice = JsonUtils.decode(msg,
                    CaesarMsg.CaesarChangeSeatPanelsNotice.class);
                if (seatPanelsNotice != null && seatPanelsNotice.getMessage() != null) {
                  KvmAsset rx = kvmAssetService.oneByDeviceId(
                      seatPanelsNotice.getMessage().getDecoderId(), master.getMasterId());
                  // 发送窗口改变通知
                  sendChangeSeatPanelsNotice(rx);
                }
              }
              case "keepalived-state-change" -> {
                String relatedMasterId = InspectCaesarUtil.getRelatedMasterId(master);
                if (!relatedMasterId.equals(master.getMasterId())) {
                  CaesarMsg.CaesarRedundantModeNotice redundantModeNotice = JsonUtils.decode(msg,
                      CaesarMsg.CaesarRedundantModeNotice.class);
                  if (redundantModeNotice != null) {
                    RedundantModeEnum redundantModeEnum = RedundantModeEnum.getMode(
                            redundantModeNotice.getMessage().getState());
                    redisUtil.set(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, master.getMasterId()),
                            String.valueOf(redundantModeEnum));
                    if (redundantModeEnum == RedundantModeEnum.MASTER) {
                      redisUtil.set(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, relatedMasterId),
                              String.valueOf(RedundantModeEnum.BACKUP));
                      log.info(String.format("Caesar %s is MASTER mode now.", master.getName()));
                    }
                  }

                }
              }
              default -> log.debug(String.format("Caesar undefined ws type:%s", notify.getType()));
            }
          }
        } catch (RuntimeException e) {
          throw e;
        } catch (Exception e) {
          log.error(e.getMessage(), e);
        }
      }

      @Override
      public String getWebSocketFullPath() {
        return String.format("ws://%s:%d%s", master.getDeviceIp(), 8080,
            "/mediacomm/notify");
      }

      @Override
      public String getMasterIp() {
        return master.getDeviceIp();
      }

      /**
       * 冗余对端主机link.status为true掉线.
       */
      private boolean isRedundantMasterLinkFail(String relatedId) {
        if (!relatedId.equals(master.getMasterId())) {
          String redisKey = RedisSignalKey.getDeviceStatusKey(DeviceType.CAESAR.getDeviceType(),
                  relatedId);
          Optional<String> link = redisUtil.hget(redisKey, RedisSignalKey.LINK_STATUS);
          if (link.isPresent()) {
            DeviceSignalValue value = JsonUtils.decode(link.get(), DeviceSignalValue.class);
            return value.getSignalValue().getBitValue();
          }
        }
        return false;
      }

      private void sendKaitoPanelChangeMsg(String masterId, int wallDeviceId, int wallGroupId) {
        Collection<KvmVideoWall> kaitoWalls = videoWallService.allByMasterIdAndDeviceId(
                masterId, wallDeviceId);
        for (KvmVideoWall kaitoWall : kaitoWalls) {
          int groupId = Integer.parseInt(Property.findValueByKey(
                  kaitoWall.getProperties(), "groupId", "-1"));
          if (groupId > -1 && groupId == wallGroupId) {
            handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
                    buildChangeVideoWallNoticeMsg(kaitoWall.getWallId()));
            break;
          }
        }
      }
    };
  }

  private KvmWebSocket buildAirconWsClient(KvmMaster master) {
    return new KvmWebSocket() {
      @Override
      public void onMessage(String msg) {
        Notice notify = JsonUtils.decode(msg, Notice.class);
        if (notify == null) {
          return;
        }
        switch (notify.getType()) {
          case "change-vw-panels" -> {
            AirconMsg.AirconVideoWallChangeNotice cn = JsonUtils.decode(msg,
                    AirconMsg.AirconVideoWallChangeNotice.class);
            if (cn != null && cn.getMessage() != null) {
              sendChangeVideoWallPanelsNotice(master.getMasterId(), cn.getMessage().getVwId(),
                      DeviceType.AIRCON_VIDEO_WALL.getDeviceTypeId());
            } else {
              log.error("Aircon change-vw-panels msg is wrong! {}", msg);
            }
          }
          case "change-seat-panels" -> {
            AirconMsg.AirconSeatChangeNotice seatPanelsNotice = JsonUtils.decode(msg,
                    AirconMsg.AirconSeatChangeNotice.class);
            if (seatPanelsNotice != null && seatPanelsNotice.getMessage() != null) {
              KvmAsset rx = kvmAssetService.oneByDeviceId(
                      seatPanelsNotice.getMessage().getDecoderId(), master.getMasterId());
              sendChangeSeatPanelsNotice(rx);
            }
          }
          case "heart-breat" -> {
          }
          default -> log.debug(String.format("Aircon undefined ws type:%s", notify.getType()));
        }
      }

      @Override
      public String getWebSocketFullPath() {
        return String.format("ws://%s:%d%s", getMasterIp(), 80, "/aircon/interface/notify");
      }

      @Override
      public String getMasterIp() {
        return master.getDeviceIp();
      }

    };
  }

  private KvmWebSocket buildKaitoWsClient(KvmMaster master) {
    return new KvmWebSocket() {
      @Override
      public void onMessage(String msg) {
        try {
          KaitoMsg kaitoMsg = JsonUtils.decode(msg, new TypeReference<>() {
          });
          if (kaitoMsg == null || kaitoMsg.getData() == null || kaitoMsg.getName() == null) {
            return;
          }
          switch (kaitoMsg.getName()) {
            case "layer/writeWindow", "layer/create", "layer/delete", "layer/writeSource",
                 "layer/writeZIndex", "layer/screenLayerLayout", "layer/deleteBatch" -> {
              KaitoLayerReq kaitoLayerReq = JsonUtils.decode(
                  JsonUtils.encode(kaitoMsg.getData()), KaitoLayerReq.class);
              if (kaitoLayerReq != null) {
                KvmVideoWall wall = videoWallService.oneByMasterIdAndDeviceId(master.getMasterId(),
                    kaitoLayerReq.getScreenId());
                handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
                    buildChangeVideoWallNoticeMsg(wall.getWallId()));
              }
            }
            default -> log.debug(String.format("Kaito undefined ws type:%s", kaitoMsg.getName()));
          }
        } catch (Exception e) {
          log.error(e.getMessage(), e);
        }
      }

      @Override
      public String getWebSocketFullPath() {
        return String.format("ws://%s:%d%s", master.getDeviceIp(), 8080, "/");
      }

      @Override
      public String getMasterIp() {
        return master.getDeviceIp();
      }

    };
  }

  private void sendChangeSeatPanelsNotice(@NotNull KvmAsset rx) {
    // 防止重复的多次更新同一个坐席屏的窗口信息
    // 当收到change-seat-panels消息时，先将assetId添加到decoderIds中，然后开启一个线程去查询当前的窗口信息
    // 当查询到窗口信息后，将assetId从decoderIds中移除，然后再发送窗口改变通知，以保证能处理最新的一次坐席窗口更新
    synchronized (decoderIds) {
      decoderIds.add(rx.getAssetId());
    }
    executor.execute(() -> {
      boolean exist;
      synchronized (decoderIds) {
        exist = decoderIds.removeIf(decoderId -> Objects.equals(decoderId, rx.getAssetId()));
      }
      if (exist) {
        ChannelIdRequestBody body = new ChannelIdRequestBody();
        body.setDecoderId(rx.getDeviceId());
        body.setChannelId(1);
        if (Objects.equals(rx.getDeviceModel(), DeviceType.AIRCON_SEAT_RX.getDeviceTypeId())) {
          KvmSeatDecoder seatDecoder = kvmSeatService.getDecoderByMasterIdAndDecoderId(
                  rx.getMasterId(), rx.getDeviceId());
          if (seatDecoder != null) {
            body.setChannelId(seatDecoder.getChannelId());
          }
        }
        MqRequest<ChannelIdRequestBody> mqRequest = new MqRequest<>();
        mqRequest.setMasterId(rx.getMasterId());
        mqRequest.setBody(body);
        String currentSeatPanels = rpcSenderUtils.send(rx.getMasterId(), RoutingOperation.SEAT_PANELS_GET, mqRequest);
        Result<SeatPanels> result = JsonUtils.decode(currentSeatPanels, new TypeReference<>() {
        });
        if (result != null && Objects.equals(result.getCode(), ResponseCode.EX_OK_200)) {
          SeatPanelsChange changeMsg = new SeatPanelsChange(rx.getAssetId(), result.getResult());
          handleSeatPanelsMsg(changeMsg);
        } else {
          log.error("Kvm seat panels get error! {}", currentSeatPanels);
        }
      }
    });
  }

  private void handleSeatPanelsMsg(SeatPanelsChange seatPanelsChange) {
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE,
            BuildWsEventNoticeBodyUtils.buildWsEventNoticeBody(
                    WebSocketTopic.EVENT_CHANGE_SEAT_PANELS, seatPanelsChange));
  }


  private void sendChangeVideoWallPanelsNotice(String masterId, int wallDeviceId, Integer... expectDeviceModelId) {
    Set<Integer> expectModelIds = Arrays.stream(expectDeviceModelId).collect(Collectors.toSet());
    // 电视墙通过masterId + deviceId的查询方式有可能出现多结果，例如全光视频墙和VP6。
    // 查出结果后，再通过预期的型号ID进行过滤。
    // 不直接通过masterId + deviceId + deviceModel查询是考虑到方法的易用性，比如凯中的"change-vw-panels"，后期还有多个VP7类型
    // ，并且调用allByMasterIdAndDeviceId不用担心空指针的问题。
    Collection<KvmVideoWall> walls = videoWallService.allByMasterIdAndDeviceId(masterId, wallDeviceId);
    for (KvmVideoWall wall : walls) {
      if (expectModelIds.contains(wall.getDeviceModel())) {
        NoticeTemplate<VideoWallChange> videoWallNotice = new NoticeTemplate<>();
        VideoWallChange videoWallChange = new VideoWallChange();
        videoWallChange.setVwId(wall.getWallId());
        videoWallNotice.setMessage(videoWallChange);
        videoWallNotice.setType(WebSocketTopic.EVENT_CHANGE_VW_PANELS);
        handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE, JsonUtils.encode(videoWallNotice));
      }
    }
  }

  private void sendChangeVideoWallConfig(KvmVideoWall wall) {
    NoticeTemplate<VideoWallChange> videoWallNotice = new NoticeTemplate<>();
    VideoWallChange videoWallChange = new VideoWallChange();
    videoWallChange.setVwId(wall.getWallId());
    videoWallNotice.setMessage(videoWallChange);
    videoWallNotice.setType(WebSocketTopic.EVENT_CHANGE_VW_CONFIG);
    handler.sendMessage(ResUrlDef.WS_TERMINAL_NOTICE, JsonUtils.encode(videoWallNotice));
  }

  /**
   * 关闭指定主机的ws连接.
   *
   * @param masterId .
   */
  public void close(String masterId) {
    KvmWebSocketClientHandler client = socketClientMap.remove(masterId);
    if (client != null) {
      client.exit();
    }
  }

  /**
   * 检查是否已包含该主机的ws连接，无则创建.
   *
   * @param masterId .
   */
  public void checkConnect(String masterId) {
    KvmMasterVo masterVo = masterService.oneById(masterId);
    synchronized (socketClientMap) {
      if (masterVo != null) {
        // 防止改了Ip后，还在尝试连接旧ip的ws
        if (socketClientMap.containsKey(masterVo.getMasterId())) {
          close(masterId);
        }
        KvmWebSocket clientPre = null;
        switch (masterVo.getDeviceType()) {
          case CAESAR -> clientPre = buildCaesarWsClient(masterVo);
          case AIRCON -> clientPre = buildAirconWsClient(masterVo);
          case KAITO02 -> clientPre = buildKaitoWsClient(masterVo);
          default -> log.info("{} ip {} do not websocket connect!", masterVo.getDeviceType(), masterVo.getDeviceIp());
        }
        if (clientPre != null) {
          KvmWebSocketClientHandler client = new KvmWebSocketClientHandler(clientPre);
          Thread.ofVirtual().name(masterVo.getDeviceIp() + " websocket connection.").start(client);
          socketClientMap.put(masterVo.getMasterId(), client);
        }
      }
    }
  }

  private String buildChangeVideoWallNoticeMsg(Integer wallId) {
    Map<String, Object> message = new HashMap<>(1);
    Map<String, Object> notice = new HashMap<>(2);
    message.put("vwId", wallId);
    notice.put("type", WebSocketTopic.EVENT_CHANGE_VW_PANELS);
    notice.put("message", message);
    return JsonUtils.encode(notice);
  }

  private void saveVwOperationLog(CaesarSwitchVwSignal message, String masterId) {
    KvmOperationLog log = new KvmOperationLog();
    log.setMasterId(masterId);
    log.setOperationTime(System.currentTimeMillis());
    log.setOperationType(OperationType.VIDEO_WALL_OPT);
    log.setOperationEvent(message.getType());
    log.setOperationSrcAddress(message.getOperator().getAddress());
    log.setOperationSrcUser(message.getOperator().getUser());
    log.setOperationTargetDevice(message.getTarget());
    kvmOperationLogService.save(log);
  }

  private void saveSeatOperationLog(CaesarSwitchSeatSignal message, String masterId) {
    KvmOperationLog log = new KvmOperationLog();
    log.setMasterId(masterId);
    log.setOperationTime(System.currentTimeMillis());
    log.setOperationType(OperationType.SEAT_OPT);
    log.setOperationEvent(message.getType());
    log.setOperationSrcAddress(message.getOperator().getAddress());
    log.setOperationSrcUser(message.getOperator().getUser());
    log.setOperationSrcDevice(message.getSrc());
    log.setOperationTargetDevice(message.getTarget());
    kvmOperationLogService.save(log);
  }
}
