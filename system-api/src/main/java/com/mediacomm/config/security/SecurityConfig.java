package com.mediacomm.config.security;

import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.Permission;
import com.mediacomm.entity.dao.Right;
import com.mediacomm.handler.JwtRequestFilter;
import com.mediacomm.system.service.PermissionService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.util.ServletUtils;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.sql.init.dependency.DependsOnDatabaseInitialization;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

/**
 * Spring Security.
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
// 要确保数据库初始化完成，才能注入PermissionService
@DependsOnDatabaseInitialization
public class SecurityConfig {

  @Autowired
  AuthenticationConfiguration authenticationConfiguration;
  @Autowired
  PermissionService permissionService;

  /**
   * 自定义过滤拦截器.
   *
   * @return .
   */
  @Bean
  public JwtRequestFilter jwtRequestFilter() {
    return new JwtRequestFilter();
  }

  /**
   * 加密方式.
   *
   * @return BCryptPasswordEncoder.
   */
  @Bean
  public BCryptPasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }

  /**
   * 认证管理器，接收登录时的参数.
   *
   * @param config .
   * @return .
   * @throws Exception .
   */
  @Bean
  public AuthenticationManager authenticationManager(AuthenticationConfiguration config)
      throws Exception {
    return config.getAuthenticationManager();
  }

  /**
   * .
   */
  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
    // 关闭csrf
    httpSecurity.csrf(AbstractHttpConfigurer::disable)
        .cors(cors -> {
          cors.configurationSource(corsConfiguration());
          cors.configure(httpSecurity);
        })
        // 不通过Session获取SecurityContext
        .sessionManagement(session ->
            session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        // 配置路径认证
        .authorizeHttpRequests(request -> {
          // 所有人员可用
          request.requestMatchers(
              "/v3/api-docs/**",
              "/swagger-ui/**",
              "/favicon.ico",
              ResUrlDef.LOGIN,
              ResUrlDef.LOGOUT,
              ResUrlDef.WS_BACKSTAGE_NOTICE,
              ResUrlDef.WS_TERMINAL_NOTICE,
              ResUrlDef.WS_FILE_NOTICE,
              ResUrlDef.KVM_SWITCHER_RPC + "/download/**",
              ResUrlDef.GLOBAL_COMMON + "/**",
              ResUrlDef.KVM_ASSET_RPC + "/snapshot/**").permitAll();
          request.requestMatchers(
              HttpMethod.GET,
              ResUrlDef.PIC_MATERIAL + "/pic"
          ).permitAll();
          Collection<Permission> permissions = permissionService.list();
          for (Permission permission : permissions) {
            for (Right right : permission.getRights()) {
              for (String method : right.getMethods()) {
                request.requestMatchers(
                    HttpMethod.valueOf(method),
                    right.getRegexp()
                ).hasAnyAuthority(permission.getName());
              }
            }
          }
          request.anyRequest().authenticated();
        })
        .authenticationManager(authenticationManager(authenticationConfiguration))
        .addFilterBefore(jwtRequestFilter(), UsernamePasswordAuthenticationFilter.class)
        .headers(header -> header.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable));
    // 权限不足
    httpSecurity.exceptionHandling(configurer -> configurer.accessDeniedHandler(
        (request, response, accessDeniedException) -> ServletUtils.render(request, response,
            Result.failure("Unauthorized.", ResponseCode.UNAUTHORIZED_3001))));
    return httpSecurity.build();
  }

  /**
   * 跨域配置.
   *
   * @return .
   */
  @Bean
  public CorsConfigurationSource corsConfiguration() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowCredentials(true);
    configuration.setAllowedOriginPatterns(Collections.singletonList("*"));
    configuration.setAllowedMethods(Collections.singletonList("*"));
    configuration.setAllowedHeaders(Collections.singletonList("*"));
    configuration.setMaxAge(Duration.ofHours(1));
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }

}
