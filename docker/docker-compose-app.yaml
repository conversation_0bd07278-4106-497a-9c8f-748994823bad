version: "3.8"

services:
  skylink-server:
    image: *************/mediacomm/skylink-server:V6.0.0.beta-17
    ports:
      - "8181:8181"
      - "8182:8182"
      - "80:80"
      - "9002:9002"
    container_name: skylink-application
    environment:
      - "REDIS_SERVICE_HOST=${REDIS_SERVICE_HOST}"
      - "REDIS_HOST_PORT=${REDIS_HOST_PORT}"
      - "DATABASE_HOST=${DATABASE_HOST}"
      - "DATABASE_USER=${DATABASE_USER}"
      - "DATABASE_PASSWORD=${DATABASE_PASSWORD}"
      - "DATABASE_NAME=${DATABASE_NAME}"
      - "DATABASE_DRIVER_CLASS=${DATABASE_DRIVER_CLASS}"
      - "DATABASE_DRIVER_NAME=${DATABASE_DRIVER_NAME}"
      - "DATABASE_PORT=${DATABASE_PORT}"
      - "RABBITMQ_SERVICE_HOST=${RABBITMQ_SERVICE_HOST}"
      - "RABBITMQ_HOST_PORT=${RABBITMQ_HOST_PORT}"
      - "RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}"
      - "RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}"
      - "DATABASE_JDBC_URL_PARAMS=${DATABASE_JDBC_URL_PARAMS}"
      #- "NACOS_SERVICE_HOST=${NACOS_SERVICE_HOST}"
    external_links:
      - mysql:mysql
      - redis:redis
      - nacos:nacos
      - rabbitmq:rabbitmq
      - kingbase:kingbase
