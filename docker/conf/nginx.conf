#user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;


events {
    worker_connections 1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile off; # 根据之前的调试结果，保持关闭
    #tcp_nopush     on;

    keepalive_timeout  65;

    # (推荐) 明确允许在HTTP头中使用下划线，增加兼容性
    underscores_in_headers on;

    #gzip  on;

    index index.html;

    #======================== SERVER for PORT 80 ========================
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location ~ ^/skylink-api/peripheral/upgrade/download/(\d+)$ {
            proxy_pass http://localhost:8181;
            proxy_pass_request_body off;
            proxy_set_header Host $host;
            # ...
            proxy_buffering off;
        }

        # 【核心修正】处理内部文件下载的 location
        location /protected-downloads/ {
            internal; # 标记为内部专用，禁止外网直接访问

            # 使用 alias 正确映射文件路径
            # 请求 /protected-downloads/SUB_DIR/file.swu
            # 会被映射到 /var/data/peripheral_upgrade/SUB_DIR/file.swu
            alias /var/data/peripheral_upgrade/;
        }

        # 前端获取图片素材时，通过nginx转发到后端服务
        location /server-material/pic {
            proxy_pass http://localhost:8181/skylink-api/picture-material/pic;
        }
    }

    #======================== SERVER for PORT 8182 ========================
    server {
        listen 8182;
        server_name localhost;
        root /usr/share/nginx/html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        # 前端获取图片素材时，通过nginx转发到后端服务
        location /server-material/pic {
            proxy_pass http://localhost:8181/skylink-api/picture-material/pic;
        }
    }
}
