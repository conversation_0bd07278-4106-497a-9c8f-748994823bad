import hashlib

def sha256_sign(input_string):
    """
    使用SHA-256算法对输入字符串进行加密签名

    :param input_string: 需要加密签名的字符串
    :return: SHA-256加密后的十六进制字符串
    """
    # 创建一个sha256哈希对象
    sha256_hash = hashlib.sha256()

    # 更新哈希对象以包含输入字符串的字节
    sha256_hash.update(input_string.encode('utf-8'))

    # 返回十六进制表示的哈希值
    return sha256_hash.hexdigest()

# 示例用法
input_string = "{\"deviceId\":0, \"screenId\":2}" + "1735524681" + "cgmf6hcUg" + "TQtg3y4yfC3FYCMq";
signature = sha256_sign(input_string)
print(f"SHA-256签名: {signature}")
