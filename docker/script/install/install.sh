#!/bin/bash

##############################################
# Descr:升级前后端程序,并更新数据库表
###############################################
file_name=/tmp/skylink.tar.gz

rm -rf /tmp/skylink*
rm -rf /system-api-*.jar

sed -n '1, /^exit 0$/!p' "$0" > ${file_name}

dd if=${file_name} | tar zxf - -C /tmp

cp -rf /tmp/skylink/system-api-*.jar /
cp -rf /tmp/skylink/dist/* /usr/share/nginx/html
cp -rf /tmp/skylink/conf/nginx.conf /etc/nginx
cp -rf /tmp/skylink/conf/supervisord.conf /etc

supervisorctl reread
supervisorctl update

exit 0
