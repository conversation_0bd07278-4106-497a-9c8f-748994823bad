FROM maven:3.9.5-eclipse-temurin-21 AS java-build
# FROM maven:3.9.5-eclipse-temurin-21 AS java-build
ARG VERSION
ENV VERSION=${VERSION}
WORKDIR /backend-ccms-platform
COPY base-core /backend-ccms-platform/base-core
COPY kvm-collectors /backend-ccms-platform/kvm-collectors
COPY system-api /backend-ccms-platform/system-api
COPY system-monitor /backend-ccms-platform/system-monitor
COPY system-protocol /backend-ccms-platform/system-protocol
COPY spotbugs-exclude.xml /backend-ccms-platform/spotbugs-exclude.xml
COPY pom.xml /backend-ccms-platform
COPY docker/conf/settings.xml /usr/share/maven/conf/
RUN --mount=type=cache,target=/root/.m2; \
    sed -i -e "s:system.appVersion = .*:system.appVersion = ${VERSION}:g" /backend-ccms-platform/system-api/src/main/resources/META-INF/VERSION.properties; \
    mvn install:install-file -Dfile=./base-core/libs/spring-data-redis-time-series-1.2.0.jar -DgroupId=org.springframework.data -DartifactId=spring-data-redis-time-series -Dversion=1.2.0 -Dpackaging=jar; \
    mvn install:install-file -Dfile=./kvm-collectors/skylink-hikvision/libs/artemis-http-client-1.1.7.jar -DgroupId=com.hikvision.ga -DartifactId=artemis-http-client -Dversion=1.1.7 -Dpackaging=jar; \
    mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase -DartifactId=kingbase8 -Dversion=8.6.0 -Dpackaging=jar; \
    mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase8 -DartifactId=pgjdbc-core-parent -Dversion=1.1.6 -Dpackaging=jar; \
    mvn package -DskipTests=true

FROM nginx:1.27.5-bookworm
ENV PATH="/opt/java/openjdk/bin:${PATH}"
ENV JAVA_HOME="/opt/java/openjdk"
RUN set -ex; \
    sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources; \
    apt-get update; \
    apt-get install default-mysql-client -y; \
    apt-get install --no-install-recommends -y curl supervisor unzip iproute2 iputils-ping; \
    apt-get install --no-install-recommends -y python-pip && pip install --no-cache-dir pip --upgrade; \
    apt-get clean; \
    rm -rf /var/lib/apt/lists/*; \
    mkdir -p /var/log/supervisor;
COPY --from=java-build /backend-ccms-platform/system-api/target/system-api-0.0.1-SNAPSHOT.jar /
COPY --from=java-build /opt/java/openjdk /opt/java/openjdk
COPY docker/conf/supervisord.conf /etc/
COPY docker/conf/nginx.conf /etc/nginx/
COPY dist /usr/share/nginx/html
EXPOSE 80/tcp
EXPOSE 8181/tcp
EXPOSE 9002/tcp
CMD ["supervisord", "-c", "/etc/supervisord.conf"]
