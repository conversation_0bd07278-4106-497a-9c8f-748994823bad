# 基于 GB/T 28181 的安防监控平台对接方案

## 1. 背景

当前系统 (`feat/hikvision-pkp-v6` 分支) 采用的是通过厂商私有 SDK 或 API 直连安防硬件的方案。该方案为每个品牌的设备（如海康、凯撒等）都开发了独立的采集器微服务 (`kvm-collectors`)。这种架构虽然能深度利用厂商提供的特定功能，但存在严重的**厂商锁定 (Vendor Lock-in)** 问题，导致每接入一个新品牌的设备，都需要投入大量的研发资源，系统维护成本高，可扩展性差。

为了解决以上痛点，我们提出一套全新的、基于国家标准 **GB/T 28181** 的对接方案。新方案将摒弃私有 SDK，通过统一的国标协议与安防平台进行交互，旨在实现系统的标准化、高可扩展性和低维护成本。

## 2. 现有方案分析

### 2.1. 架构图

```
+----------------+      +----------------+      +------------------+      +----------------+
|                |      |                |      |  skylink-hikvision |      |                |
|   前端应用     +----->+   system-api   +----->+   (海康采集器)   +----->+  海康设备 SDK  |
|                |      | (业务与网关)   |      +------------------+      |                |
+----------------+      +-------+--------+      |  skylink-caesar  |      +----------------+
                                |               |   (凯撒采集器)   |
                                | RabbitMQ      +------------------+
                                | (消息队列)    |       ...        |
                                +-------------> +------------------+
```

### 2.2. 核心原理

- **微服务架构**: 基于 Spring Cloud，`system-api` 作为业务入口，通过 RabbitMQ 消息队列将指令下发给各个 `kvm-collectors` 采集器。
- **私有协议对接**: 每个采集器服务都内嵌了特定厂商的 SDK，负责将标准指令翻译成私有协议指令，直接控制硬件。
- **紧耦合**: 业务逻辑与特定的硬件实现紧密耦合，难以适配不同品牌的设备。

### 2.3. 优缺点

- **优点**:
  - **功能全面**: 可以利用厂商私有 SDK 提供的所有高级功能。
  - **性能可能更高**: SDK 通常经过优化，与自家硬件的通信效率最高。
- **缺点**:
  - **厂商锁定**: 接入新品牌设备需要重新开发一个采集器服务。
  - **协议不统一**: 不同厂商的 API 和事件通知机制千差万别，难以形成统一的管理模型。
  - **维护成本高**: 需要维护多个采集器服务，代码冗余。
  - **可扩展性差**: 无法平滑地接入新的、不同品牌的安防平台。

---

## 3. 新方案设计 (GB/T 28181)

新方案的核心是引入一个**“GB/T 28181 网关服务”**，它将作为本系统与所有国标安防平台通信的唯一桥梁。

### 3.1. 新架构设计

```
+----------------+      +----------------+      +------------------------+      +------------------------+
|                |      |                |      |                        |      |                        |
|   前端应用     +----->+   system-api   +----->+   GB/T 28181 网关服务  +----->+  GB/T 28181 安防平台   |
|                |      | (核心业务逻辑) |      | (协议转换与信令控制)   |      | (如：海康、大华等平台) |
+----------------+      +-------+--------+      +-----------+------------+      +-----------+------------+
                                |                          | SIP/RTP                         |
                                | Redis/DB                 |                                 |
                                |                          v                                 v
                          +-----+-----+          +------------------------+      +------------------------+
                          |           |          |                        |      |                        |
                          |  数据库   |          |     媒体服务 (ZLM)     |      |      摄像头等设备      |
                          |           |          | (RTP接收与协议转换)    |      |                        |
                          +-----------+          +------------------------+      +------------------------+
```

### 3.2. 核心组件改造

1.  **开发/引入 GB/T 28181 网关服务 (Gateway Service)**
    -   **角色**: 新架构的核心，负责将系统内部的业务指令（如“播放视频”）转换成 GB/T 28181 的 SIP 信令。
    -   **职责**:
        -   向上游 (`system-api`) 提供统一的、协议无关的 RESTful API。
        -   向下游 (国标平台) 发送和处理 SIP 信令（注册、心跳、点播、PTZ控制等）。
        -   从国标平台同步设备目录，并进行管理。
        -   与媒体服务协作，管理视频流。
    -   **技术选型**: 强烈建议基于成熟的开源项目（如 `wvp-GB28181-pro`）进行二次开发或直接部署，以缩短开发周期。

2.  **引入媒体服务 (Media Server)**
    -   **角色**: 负责接收从安防平台传来的 RTP 视频流，并将其转换为前端易于播放的格式。
    -   **职责**:
        -   接收 RTP 实时视频流。
        -   将 RTP 流转换成 HLS、FLV、WebRTC 等格式。
        -   提供视频流的分发能力。
    -   **技术选型**: 推荐使用 `ZLMediaKit`，它与 `wvp-GB28181-pro` 等开源项目能无缝集成。

3.  **改造 `system-api`**
    -   **设备资产管理**: `KvmAssetService` 不再存储设备的私有连接信息（IP、端口、密码），而是存储其在国标平台中的唯一ID (国标ID)。
    -   **指令下发**: 当需要控制设备时，`system-api` 不再向 RabbitMQ 发送指令，而是**直接调用 GB/T 28181 网关服务提供的标准 API**。

4.  **废弃 `kvm-collectors`**
    -   所有与厂商私有 SDK 对接的采集器微服务 (`skylink-hikvision`, `skylink-caesar` 等) 将被**完全移除**。这是新架构带来的最大好处，极大地简化了系统结构，降低了维护成本。

### 3.3. 新工作流程（以“摄像头预览”为例）

1.  **前置条件**: 所有摄像头都已通过 GB/T 28181 协议注册到统一的安防平台。GB/T 28181 网关服务也已向该平台注册成功，并同步了设备列表。
2.  **请求入口**: 用户在前端点击某个摄像头的“预览”按钮。
3.  **指令转换**: `system-api` 接收到请求，从数据库查出该摄像头的国标ID，然后调用 **GB/T 28181 网关服务** 的 API，例如 `POST /api/play`, body: `{ "deviceId": "国标ID" }`。
4.  **SIP 信令交互**:
    -   网关服务收到请求后，生成一个 SIP `INVITE` 请求，通过安防平台发往对应的摄像头。
    -   摄像头响应 `200 OK`，并开始向媒体服务器的指定端口推送 RTP 视频流。
5.  **视频流处理与播放**:
    -   媒体服务 (ZLMediaKit) 接收到 RTP 流，并将其转换成例如 FLV 格式的直播流地址 (`http://.../live.flv`)。
    -   网关服务将此可播放地址返回给 `system-api`，最终由 `system-api` 返回给前端。
    -   前端播放器（如 `video.js`）直接播放该地址。

## 4. 实施与迁移计划

1.  **技术选型确认**: 确定用于网关服务和媒体服务的开源项目，并完成基础环境搭建。
2.  **接口定义**: 设计并评审 `system-api` 与 GB/T 28181 网关服务之间的 RESTful API，确保其通用性和稳定性。
3.  **分步实施**:
    -   **第一阶段 (POC)**: 搭建 GB/T 28181 网关和媒体服务，实现与安防平台的基本对接和视频点播功能。
    -   **第二阶段 (功能迁移)**: 改造 `system-api`，将视频预览、云台控制 (PTZ)、录像回放等核心功能，从调用 RabbitMQ 切换到调用新的网关服务 API。
    -   **第三阶段 (旧模块下线)**: 在所有功能迁移完毕并验证稳定后，正式下线所有 `kvm-collectors` 模块，并清理相关代码和配置。

## 5. 新方案优势总结

- **消除厂商锁定**: 可轻松对接任何支持 GB/T 28181 标准的安防设备和平台。
- **简化系统架构**: 移除所有冗余的采集器服务，降低系统复杂度和维护成本。
- **统一管理模型**: 所有设备通过统一的协议和数据模型进行管理，便于功能扩展。
- **符合国家标准**: 具备更好的合规性和互操作性。
- **降低研发成本**: 未来接入新设备或平台时，无需或只需少量开发工作。

---

## 6. 核心场景详析：硬件解码上墙

此场景是系统的核心功能，它涉及到多个硬件设备的协同工作，需要对原方案进行细化。视频流向为：`摄像头 -> 安防平台 -> 解码终端 -> 凯撒TX -> 凯撒主机 -> 凯撒RX -> 屏幕`。

### 6.1. 场景分析与挑战

1.  **控制流分离**: 存在两个独立的控制域：
    *   **视频流控制 (GB/T 28181)**: 控制安防平台将指定摄像头的视频流推送到哪个解码终端。
    *   **矩阵切换控制 (私有协议)**: 控制凯撒KVM矩阵将哪个输入源切换到哪个输出屏幕。
2.  **资源管理**: 解码终端是可变的、有限的资源池，必须对其进行有效的分配、占用和释放管理。
3.  **任务编排**: 需要一个中心节点（编排器）来协调上述两个控制流，确保它们按正确的顺序和参数执行。

### 6.2. 调整后架构

`system-api` 在此场景中扮演核心的**上墙任务编排器**角色。旧的 `skylink-caesar` 可改造为职责单一的 `凯撒矩阵控制服务`。

```mermaid
graph TD
    subgraph "用户操作"
        A[前端应用]
    end

    subgraph "核心业务层"
        B(system-api <br> 上墙任务编排器)
    end

    subgraph "控制服务层"
        C(GB/T 28181 网关服务)
        D(凯撒矩阵控制服务 <br> 私有协议)
    end

    subgraph "安防平台 (GB/T 28181)"
        E[安防平台]
        F[摄像头]
    end

    subgraph "硬件设备层"
        G["解码终端 (资源池)"]
        H[凯撒 KVM 矩阵]
        I[视频墙/屏幕]
    end

    A -- "1. 上墙请求 <br> (摄像头ID, 屏幕ID)" --> B
    B -- 2. 分配解码终端 & 构造指令 --> B
    B -- "3. 请求视频流 <br> (摄像头ID, 解码终端IP)" --> C
    C -- 4. SIP INVITE --> E
    E -- 5. 命令推流 --> F
    F -- 6. RTP视频流 --> G
    B -- "7. 请求矩阵切换 <br> (输入ID, 输出ID)" --> D
    D -- 8. SDK/API调用 --> H
    G -- 视频信号 --> H
    H -- 视频信号 --> I
```

### 6.3. 详细工作流程

#### 文字说明

1.  **入口**: 前端向 `system-api` 发送上墙指令，包含源（摄像头ID）和目的地（屏幕ID）。
2.  **编排与资源分配**: `system-api` 作为编排器：
    *   查询系统拓扑，明确屏幕、凯撒RX、凯撒TX、解码终端之间的物理连接关系。
    *   从资源池中**分配一个空闲的解码终端**，并获取其IP地址。
3.  **请求视频流**: `system-api` 调用 `GB/T 28181 网关服务`，请求将指定摄像头的视频流推送到已分配的解码终端的IP地址。
4.  **SIP信令交互**: 网关服务向安防平台发送 `INVITE` 请求，安防平台命令摄像头向解码终端推送RTP流。
5.  **请求矩阵切换**: `system-api` 调用 `凯撒矩阵控制服务`，传入解码终端对应的凯撒输入ID和屏幕对应的凯撒输出ID。
6.  **硬件执行**: 凯撒服务调用私有SDK完成切换。解码终端解码后的视频信号通过凯撒矩阵，最终显示在目标屏幕上。
7.  **任务结束**: `system-api` 监控任务状态，上墙成功后更新数据库。当用户关闭视频时，执行反向流程，释放解码终端和矩阵端口资源。

#### Mermaid 时序图

```mermaid
sequenceDiagram
    participant FE as 前端应用
    participant API as system-api (编排器)
    participant GB28181 as GB/T 28181 网关
    participant Caesar as 凯撒矩阵控制服务
    participant Platform as 安防平台
    participant Decoder as 解码终端
    participant Matrix as 凯撒KVM矩阵

    FE->>+API: 1. 请求上墙(camId, screenId)
    API->>API: 2. 分配空闲解码终端(decoderX), 查询拓扑关系
    API->>+GB28181: 3. 请求视频流(camId, decoderX_IP)
    GB28181->>+Platform: 4. SIP INVITE(camId, decoderX_IP)
    Platform-->>-GB28181: 200 OK
    Platform->>Decoder: 5. 推送RTP视频流
    GB28181-->>-API: 操作成功
    
    API->>+Caesar: 6. 请求切换(input_tx_id, output_rx_id)
    Caesar->>+Matrix: 7. SDK调用切换指令
    Matrix-->>-Caesar: 切换成功
    Caesar-->>-API: 操作成功

    API-->>-FE: 8. 上墙成功
```

### 6.4. 方案核心思想总结

- **分离控制平面**: 将标准的**信令控制 (GB/T 28181)** 与私有的**硬件控制 (SDK/API)** 分离开，由不同的服务负责。
- **集中任务编排**: `system-api` 作为“大脑”，负责协调和编排所有服务和硬件资源，完成复杂的跨设备任务。
- **保留并重构采集器**: 旧的采集器不完全废弃，而是改造为职责单一的**“硬件能力服务”**（如矩阵控制、拼接屏控制），向上提供标准的API。
- **资源池化管理**: 对解码器、屏幕等有限资源进行池化管理，是实现自动化调度的关键。