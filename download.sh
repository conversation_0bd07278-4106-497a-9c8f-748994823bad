#!/bin/bash

#set -eux #指令返回值不为0则退出，执行未定义变量显示错误信息

CI_API_V4_URL=$1
PRIVATE_TOKEN=$2
MESSAGE_FILE_PATH=$3

# clean dist
if [ -d "dist" ]; then
  rm -rf "dist"
fi

CUR_WORK_DIR=$(pwd)

mkdir -p tmp
cd tmp

while IFS=' ' read -r key value tag; do
  case "$key" in
  "app-ccms-android")
    value=$(echo "${value}" | tr -d '\r\n')
    ref=$(python3 -c "import urllib.parse; print(urllib.parse.quote('${value}',safe=''))")

    curl -o ${key}.zip -L --header "PRIVATE-TOKEN: ${PRIVATE_TOKEN}" "$CI_API_V4_URL/projects/skylink%2Fccms%2F$key/jobs/artifacts/$ref/download?job=build_apk"
    unzip ${key}.zip -d ${key}
    mkdir -p $CUR_WORK_DIR/dist/app/ccms/apk
    cp -R ${key}/artifacts/* $CUR_WORK_DIR/dist/app/ccms/apk

    # 初始化versions.json文件
    versions_file=$CUR_WORK_DIR/dist/app/ccms/apk/versions.json
    if [ ! -f "$versions_file" ]; then
      jq -n '[]' >"$versions_file"
    fi
    # 读取version.json，并补充信息
    new_data=$(cat ${key}/artifacts/version.json | jq ".productType = \"${tag}\"")
    # 将数据插入到versions.json文件中
    jq -c --argjson new_obj "$new_data" '. + [$new_obj]' "$versions_file" >"${versions_file}.tmp" && mv "${versions_file}.tmp" "$versions_file"
    
    rm -r ${key}
    ;;
  "app-ccms-windows")
    value=$(echo "${value}" | tr -d '\r\n')
    ref=$(python3 -c "import urllib.parse; print(urllib.parse.quote('${value}',safe=''))")
    curl -o ${key}.zip -L --header "PRIVATE-TOKEN: ${PRIVATE_TOKEN}" "$CI_API_V4_URL/projects/skylink%2Fccms%2F$key/jobs/artifacts/$ref/download?job=build_win"
    unzip ${key}.zip -d ${key}
    mkdir -p $CUR_WORK_DIR/dist/app/ccms/exe
    cp -R ${key}/dist_electron/* $CUR_WORK_DIR/dist/app/ccms/exe
    rm -r ${key}
    ;;
  "frontend-ccms-admin")
    value=$(echo "${value}" | tr -d '\r\n')
    ref=$(python3 -c "import urllib.parse; print(urllib.parse.quote('${value}',safe=''))")
    curl -o ${key}.zip -L --header "PRIVATE-TOKEN: ${PRIVATE_TOKEN}" "$CI_API_V4_URL/projects/skylink%2Fccms%2F$key/jobs/artifacts/$ref/download?job=build_web"
    unzip ${key}.zip -d ${key}
    mkdir -p $CUR_WORK_DIR/dist
    cp -R ${key}/dist/* $CUR_WORK_DIR/dist
    rm -r ${key}
    ;;
  "frontend-ccms-visualization")
    value=$(echo "${value}" | tr -d '\r\n')
    ref=$(python3 -c "import urllib.parse; print(urllib.parse.quote('${value}',safe=''))")
    curl -o ${key}.zip -L --header "PRIVATE-TOKEN: ${PRIVATE_TOKEN}" "$CI_API_V4_URL/projects/skylink%2Fccms%2F$key/jobs/artifacts/$ref/download?job=build_web"
    unzip ${key}.zip -d ${key}
    mkdir -p $CUR_WORK_DIR/dist
    cp -R ${key}/dist/* $CUR_WORK_DIR/dist
    rm -r ${key}
    ;;
  esac
done <$MESSAGE_FILE_PATH

cd $CUR_WORK_DIR
rm -fr tmp
