## 1 代码结构

### 1.1 目录结构
```
├── base-core      # 基础核心模块，含Dao
├── docker         # 构建服务镜像的配置及compose文件
├── kvm-collectors # 远程控制服务模块
├── system-api     # 系统所有API
```

### 1.2 分层畛域模型规约

**DO（Data Object）**：与数据库表构造一一对应，通过DAO层向上传输数据源对象。

**DTO（Data Transfer Object）**：数据传输对象，Service或Manager向外传输的对象。

**BO（Business Object）**：业务对象。由Service层输入的封装业务逻辑的对象。

**AO（Application Object）**：利用对象。在Web层与Service层之间形象的复用对象模型，极为贴近展现层，复用度很低

**VO（View Object）**：显示层对象，通常是Web向模板渲染引擎层传输的对象。

**Query**：数据查问对象，各层接管下层的查问申请。留神超过2个参数的查问封装，禁止应用Map类来传输。

**畛域模型命名规约**

1） 数据对象：xxxDO，xxx即为数据表名。

2） 数据传输对象：xxxDTO，xxx为业务畛域相干的名称。

3） 展现对象：xxxVO，xxx个别为网页名称。

4） POJO是DO/DTO/BO/VO的统称，禁止命名成xxxPOJO。

## 2 构建镜像
### 2.1 开发环境
idea安装docker插件，并配置远程服务器上的docker打开远程连接端口

```
vi /usr/lib/systemd/system/docker.service
......
[Service]
......
ExecStart=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock -H tcp://0.0.0.0:2375
......
```

### 2.2 构建步骤
#### 2.2.1 命令示例
```
docker build --force-rm --squash --build-arg VERSION=0.0.1-SNAPSHOT -t mediacomm/skylink-server:0.0.1-SNAPSHOT .

docker run --name jdk21 -d mediacomm/skylink-server:0.0.1-SNAPSHOT

```
#### 2.2.2 Kingbase构建示例
```
前提：已从人大金仓下载kingbase镜像

1.docker run -tdi --privileged -p 54321:54321 -e DB_USER=skylink -e DB_PASSWORD=123456 --name kb kingbase:v1 /usr/sbin/init

2.使用kingbase开发工具，创建skylink数据库，账号skylink，密码123456

3.使用kingbase迁移工具，将mysql初始化的数据导入启动的kingbase容器中

4.docker commit <容器名称> skylink/kingbase:<tag>
```

### 2.3 版本号后缀使用说明
- **SNAPSHOT**：是指“快照”版本，是一种特殊的版本，代表当前开发版本。
- **ALPHA**：代表内部测试版本，通常只在开发团队内部使用。
- **BETA**：代表公测版本，已经相对稳定，但可能仍有一些bug。
- **RC**：代表发布候选版本，通常是发布正式版本之前的最后一个版本。
- **RELEASE**：代表正式版本，通常是不会再进行大的改动和更新。

## 3 安装外部依赖
- **手动安装依赖**
```
# kingbase8
mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase -DartifactId=kingbase8 -Dversion=8.6.0 -Dpackaging=jar
mvn install:install-file -Dfile=./base-core/libs/kingbase8-8.6.0.jar -DgroupId=com.kingbase8 -DartifactId=pgjdbc-core-parent -Dversion=1.1.6 -Dpackaging=jar
# redis-time-series
mvn install:install-file -Dfile=./base-core/libs/spring-data-redis-time-series-1.2.0.jar -DgroupId=org.springframework.data -DartifactId=spring-data-redis-time-series -Dversion=1.2.0 -Dpackaging=jar
# artemis-http-client
mvn install:install-file -Dfile=./kvm-collectors/skylink-hikvision/libs/artemis-http-client-1.1.7.jar -DgroupId=com.hikvision.ga -DartifactId=artemis-http-client -Dversion=1.1.7 -Dpackaging=jar
```

## 4 ProtoBuf编译
```
# window
下载protobuf编译器: https://github.com/protocolbuffers/protobuf/releases/tag/v26.1
执行命令: .\protoc.exe -I=D:\protoc-26.1\bin --java_out=D:\ D:\protoc-26.1\bin\SanpshotTransfer.proto
```


## 5 CI
### 5.1 在`gitlab`上打`tag`触发，构建docker镜像
- 镜像的`tag`取决于触发`ci`的`tag`
- `tag注解`需要将前端资源对应的`tag`标签填入（资源格式：项目名 tag/分支名），构建镜像前会逐行遍历注解进行解析、下载、整理
#### 示例：
- tag
```
0.0.1-SNAPSHOT
```
- 注解：
```
app-ccms-windows V2.0.1-build.1
app-ccms-android V2.0.2-build.202403201348
frontend-ccms-admin V6.0.1-build.202403201338
frontend-ccms-visualization V6.0.1-build.202403201345

...(其他内容：如功能更新等)
```

## 数据库初始化方式
### Flyway

目标：
- 初始化数据仅执行一次：指的是那些基础的、种子性的数据，比如系统配置的初始默认值、超级管理员账号等。这些数据在数据库第一次建立时插入，之后不应该被重复插入。
- 后续升级迭代，需要覆盖更新初始化的那些数据：指的是某些初始数据可能在后续的版本中需要被更新其值，而不是简单地添加新数据。例如，某个系统参数的默认值在 V2 版本改变了。

#### 使用 Flyway 实现的方案
- 版本化迁移 (Versioned Migrations): 文件名以 V 开头，后跟版本号，例如 V1__Initial_schema.sql, V1.1__Insert_seed_data.sql, V2__Update_config_value.sql。
    - 特性： 每个版本化迁移脚本只会成功执行一次。Flyway 会在 flyway_schema_history 表中记录已执行的脚本。一旦执行过，就不会再次执行，除非你手动进行 clean 操作或修改历史表（不推荐）。
    - 用途： 非常适合创建/修改表结构 (DDL) 和执行一次性的数据插入或更新 (DML)。
- 可重复执行的迁移 (Repeatable Migrations): 文件名以 R 开头，例如 R__Recalculate_user_scores.sql, R__Refresh_materialized_views.sql。
    - 特性： 可重复迁移脚本每次校验和 (checksum) 发生变化时都会被重新执行。它们总是在所有待处理的版本化迁移都执行完毕之后才执行。
    - 用途： 通常用于管理数据库对象（如视图、存储过程、函数）的定义，或者执行一些每次部署都可能需要运行的数据维护任务（比如重新计算某些汇总数据，但要小心副作用）。

```
需求一：初始化数据仅执行一次
策略： 使用版本化迁移 (V__*.sql) 来插入这些只需要执行一次的初始化数据。
示例：
V1__Create_initial_schema.sql: 包含所有 CREATE TABLE IF NOT EXISTS ... 语句。
V1.1__Insert_core_configuration.sql: 包含 INSERT INTO configuration (key, value) VALUES ('feature_x_enabled', 'true'); 等。
V1.2__Insert_admin_user.sql: 包含 INSERT INTO users (username, password, role) VALUES ('admin', '...', 'ADMIN');。
这些脚本在第一次 flyway.migrate() 时会被执行，之后就不会再执行了。如果用户后来修改或删除了这些数据，Flyway 不会自动恢复它们。
```
```
需求二：后续升级迭代，需要覆盖更新初始化的那些数据
这里有两种主要情况和对应的策略：
情况 A：你知道在特定版本需要更新特定数据的值。
策略： 使用新的版本化迁移 (V__*.sql) 来执行 UPDATE 语句。
示例：
假设在 V1.1 中插入了配置 'feature_x_enabled', 'true'。现在在 V2 版本，你需要将其默认值改为 'false'。
V2__Update_feature_x_configuration.sql:
UPDATE configuration
SET value = 'false'
WHERE key = 'feature_x_enabled';
-- 如果需要确保该配置项存在才更新，可以加上条件，或者之前的 V1.1 脚本保证了它的存在。
-- 如果你希望如果不存在就插入，如果存在就更新 (UPSERT)，MySQL可以用:
-- INSERT INTO configuration (key, value) VALUES ('feature_x_enabled', 'false')
-- ON DUPLICATE KEY UPDATE value = 'false';
-- PostgreSQL 可以用 INSERT ... ON CONFLICT ... DO UPDATE ...

这个 V2 脚本也只会执行一次。它会在 V1.1 之后执行，更新已存在的数据。

情况 B：你有一些“参考数据”或“默认配置”，希望在每次部署时（或者当这些数据的定义发生变化时）都能确保它们是最新的值，即使之前可能被用户修改过（这种场景要谨慎，可能会覆盖用户数据）。或者更常见的是，这些数据是“如果不存在就插入，如果存在就更新为最新定义”。
策略 1 (推荐，更可控)：仍然使用版本化迁移 (V__*.sql) 配合 UPSERT 逻辑。
这是最清晰和最可控的方式。每次你需要“刷新”这些数据到新定义时，就创建一个新的版本化迁移脚本。
示例：   

假设你有一张 default_settings 表，你希望不断更新其中的默认值。
V1__Init_default_settings.sql:
INSERT INTO default_settings (setting_name, setting_value) VALUES ('theme', 'light')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value); -- MySQL UPSERT
INSERT INTO default_settings (setting_name, setting_value) VALUES ('notifications', 'on')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

V2__Update_theme_default_setting.sql:
INSERT INTO default_settings (setting_name, setting_value) VALUES ('theme', 'dark')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- 注意：这条语句会确保 theme 的值是 'dark'。如果之前是 'light'，会被更新。
-- 如果之前用户手动改成了 'blue'，也会被这条语句覆盖回 'dark'。
这种方式下，每次变更都是一个明确的版本，只会执行一次。但请注意，如果你的目标是“覆盖”用户可能已经修改的数据，这种方式是有效的，但也可能不是用户期望的。
```
