<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
  <!--  <suppress>-->
  <!--    <notes><![CDATA[-->
  <!--   file name: mybatis-plus-3.5.3.1.jar-->
  <!--   ]]></notes>-->
  <!--    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus@.*$</packageUrl>-->
  <!--    <cpe>cpe:/a:mybatis:mybatis</cpe>-->
  <!--  </suppress>-->
  <!--  <suppress>-->
  <!--    <notes><![CDATA[-->
  <!--   file name: mybatis-plus-core-3.5.3.1.jar-->
  <!--   ]]></notes>-->
  <!--    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-core@.*$</packageUrl>-->
  <!--    <cpe>cpe:/a:baomidou:mybatis-plus</cpe>-->
  <!--  </suppress>-->
  <suppress>
    <notes><![CDATA[
   file name: mybatis-plus-core-3.5.3.1.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-core@.*$</packageUrl>
    <cve>CVE-2020-26945</cve>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: mybatis-plus-extension-3.5.3.1.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-extension@.*$</packageUrl>
    <cve>CVE-2020-26945</cve>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: mybatis-plus-3.5.3.1.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus@.*$</packageUrl>
    <cve>CVE-2020-26945</cve>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: mybatis-plus-annotation-3.5.3.1.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-annotation@.*$</packageUrl>
    <cve>CVE-2020-26945</cve>
  </suppress>

  <suppress>
    <notes><![CDATA[
   file name: mybatis-plus-boot-starter-3.5.3.1.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-boot-starter@.*$</packageUrl>
    <cve>CVE-2020-26945</cve>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: base-core-0.0.1-SNAPSHOT.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.mediacomm/base\-core@.*$</packageUrl>
    <cpe>cpe:/a:media-server_project:media-server</cpe>
  </suppress>
  <suppress>
    <notes><![CDATA[
      file name: nacos-client-2.2.1.jar (shaded: com.google.guava:guava:30.1-jre)
      ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.google\.guava/guava@.*$</packageUrl>
    <vulnerabilityName>CVE-2023-2976</vulnerabilityName>
  </suppress>
</suppressions>